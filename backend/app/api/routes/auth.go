package routes

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/config"
	"github.com/nocytech/butce360/pkg/database"
	"github.com/nocytech/butce360/pkg/domains/auth"
	"github.com/nocytech/butce360/pkg/domains/oauth"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
	"github.com/nocytech/butce360/pkg/fin_notebooklog"
	"github.com/nocytech/butce360/pkg/middleware"
	"github.com/nocytech/butce360/pkg/services"
	"github.com/nocytech/butce360/pkg/state"
	"github.com/nocytech/butce360/pkg/utils"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service) {
	r.POST("/login", Login(s))
	r.POST("/register", Register(s))
	r.GET("/me", middleware.Authorized(), GetMe(s))
	r.POST("/logout", middleware.Authorized(), Logout(s))
	r.POST("/refresh", middleware.Authorized(), RefreshToken(s))
	r.GET("/validate", middleware.Authorized(), ValidateToken())
	r.PUT("/profile", middleware.Authorized(), UpdateProfile(s))
	r.POST("/change-password", middleware.Authorized(), ChangePassword(s))
	r.DELETE("/delete-account", middleware.Authorized(), DeleteUserAccount(s))

	// Guest user routes
	r.POST("/guest/upgrade", middleware.Authorized(), ConvertGuestToRegistered(s))
	r.GET("/limit-status", middleware.Authorized(), GetTransactionLimitStatus(s))

	// Google OAuth routes
	oauthService := oauth.NewService()
	r.GET("/google/login", GoogleLogin(oauthService))
	r.GET("/google/callback", GoogleCallback(s, oauthService))

	// Mobile OAuth routes
	r.POST("/google/mobile", GoogleMobileLogin(s))
	r.POST("/apple/mobile", AppleMobileLogin(s))
	r.POST("/apple/callback", AppleCallback(s))

	// Data Import routes
	r.POST("/import", ImportUserData(s))
}

// @Summary Refresh JWT token
// @Description Issue a new JWT token for the current user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Returns new token"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /auth/refresh [post]
func RefreshToken(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := state.GetCurrentUserID(c)
		// Load user to include proper claims
		user, err := s.GetUserByID(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error(), "status": 400})
			return
		}

		jwt := utils.JwtWrapper{
			SecretKey: config.ReadValue().App.JwtSecret,
			Expire:    int(config.ReadValue().App.JwtExpire),
		}

		loginType := auth.FindLoginType(*user)

		token, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan, loginType)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "failed to generate token"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": gin.H{"token": token}, "status": 200})
	}
}

// @Summary Validate token
// @Description Validates the provided JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Token is valid"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /auth/validate [get]
func ValidateToken() func(c *gin.Context) {
	return func(c *gin.Context) {
		// If Authorized middleware passed, token is valid
		c.JSON(http.StatusOK, gin.H{"data": gin.H{"valid": true}, "status": 200})
	}
}

// @Summary Update current user's profile
// @Description Update name or email of the logged in user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.UpdateProfileRequest true "Profile fields"
// @Success 200 {object} map[string]interface{} "Returns updated user"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /auth/profile [put]
func UpdateProfile(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.UpdateProfileRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error(), "status": 400})
			return
		}

		userID := state.GetCurrentUserID(c)
		// Load user
		user, err := s.GetUserByID(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error(), "status": 400})
			return
		}

		// Update fields
		if req.Name != nil && *req.Name != "" {
			user.Name = *req.Name
		}
		if req.Email != nil && *req.Email != "" {
			user.Email = *req.Email
		}

		// Persist selective fields to avoid overwriting others
		if err := database.DBClient().Model(&entities.User{}).Where("id = ?", user.ID).Updates(map[string]interface{}{
			"name":  user.Name,
			"email": user.Email,
		}).Error; err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error(), "status": 400})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": user, "status": 200})
	}
}

// @Summary Login user
// @Description Login with username and password to get JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dtos.AuthenticationRequest true "Login credentials"
// @Success 200 {object} map[string]interface{} "Returns JWT token and user data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /login [post]
func Login(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.AuthenticationRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Login Failed",
				Message: "Login Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		resp, err := s.Login(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Register new user
// @Description Register a new user with username, email, password and name
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dtos.RegisterRequest true "User registration data"
// @Success 201 {object} map[string]interface{} "Returns JWT token and user data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /register [post]
func Register(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RegisterRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Register Failed",
				Message: "Register Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.Register(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get current user
// @Description Get information about the currently logged in user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Returns user data"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /auth/me [get]
func GetMe(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		// Get user by ID
		user, err := s.GetUserByID(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   user,
			"status": 200,
		})
	}
}

// @Summary Logout user
// @Description Logout the current user by invalidating the JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Success message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /logout [post]
func Logout(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context
		userID := state.GetCurrentUserID(c)

		// Get token from context
		token := state.GetCurrentToken(c)
		if token == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "token not found",
				"status": 400,
			})
			return
		}

		// Logout the user
		err := s.Logout(token, userID.String())
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   "logout successful",
			"status": 200,
		})
	}
}

// @Summary Google OAuth Login
// @Description Redirect to Google OAuth authorization page
// @Tags auth
// @Accept json
// @Produce json
// @Success 302 {string} string "Redirect to Google OAuth"
// @Router /auth/google/login [get]
func GoogleLogin(oauthService oauth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Generate a random state parameter for security
		state := uuid.New().String()

		// Store state in session or cache for validation later
		// For now, we'll just use it directly

		authURL := oauthService.GetGoogleAuthURL(state)
		c.Redirect(http.StatusTemporaryRedirect, authURL)
	}
}

// @Summary Google OAuth Callback
// @Description Handle Google OAuth callback and create/login user
// @Tags auth
// @Accept json
// @Produce json
// @Param code query string true "Authorization code from Google"
// @Param state query string true "State parameter for security"
// @Success 200 {object} map[string]interface{} "Returns JWT token and user data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /auth/google/callback [get]
func GoogleCallback(authService auth.Service, oauthService oauth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		code := c.Query("code")
		state := c.Query("state")

		if code == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "authorization code not provided",
				"status": 400,
			})
			return
		}

		if state == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "state parameter not provided",
				"status": 400,
			})
			return
		}

		// Exchange code for token
		token, err := oauthService.ExchangeCodeForToken(code)
		if err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Google OAuth Failed",
				Message: "Failed to exchange code for token: " + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "failed to exchange authorization code",
				"status": 400,
			})
			return
		}

		// Get user info from Google
		userInfo, err := oauthService.GetGoogleUserInfo(token)
		if err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Google OAuth Failed",
				Message: "Failed to get user info: " + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "failed to get user information",
				"status": 400,
			})
			return
		}

		// Try to find existing user by email or Google ID
		resp, err := authService.GoogleOAuthLogin(userInfo.Email, userInfo.ID, userInfo.Name)
		if err != nil {
			// Redirect to frontend with error
			c.Redirect(http.StatusTemporaryRedirect, "/auth/google/callback?error="+err.Error())
			return
		}

		// Redirect to frontend with success data
		// We'll pass the token and user data as query parameters (not ideal for production)
		// In production, you might want to use a temporary token or session
		redirectURL := fmt.Sprintf("/auth/google/callback?token=%s&user_id=%s&username=%s&email=%s&name=%s",
			resp.Token, resp.User.ID, resp.User.Username, resp.User.Email, resp.User.Name)
		c.Redirect(http.StatusTemporaryRedirect, redirectURL)
	}
}

// @Summary Google Mobile OAuth Login
// @Description Authenticate user with Google ID token from mobile app
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dtos.GoogleOAuthRequest true "Google OAuth Request"
// @Success 200 {object} map[string]interface{} "Returns JWT token and user data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /auth/google/mobile [post]
func GoogleMobileLogin(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GoogleOAuthRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Google Mobile Login Failed",
				Message: "Invalid request format: " + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid request format",
				"status": 400,
			})
			return
		}

		// Verify Google ID token and extract user info
		oauthService := services.NewOAuthService(database.DBClient())
		oauthResp, err := oauthService.AuthenticateWithGoogle(&req)
		if err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Google Mobile Login Failed",
				Message: "Google authentication failed: " + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Google authentication failed",
				"status": 400,
			})
			return
		}

		if !oauthResp.Success {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  oauthResp.Error,
				"status": 400,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data": gin.H{
				"token": oauthResp.Token,
				"user":  oauthResp.User,
			},
			"status": 200,
		})
	}
}

// @Summary Apple Mobile OAuth Login
// @Description Authenticate user with Apple identity token from mobile app
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dtos.AppleOAuthRequest true "Apple OAuth Request"
// @Success 200 {object} map[string]interface{} "Returns JWT token and user data"
// @Failure 400 {object} map[string interface{} "Error message"
// @Router /auth/apple/mobile [post]
func AppleMobileLogin(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.AppleOAuthRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid request format",
				"status": 400,
			})
			return
		}

		// Verify Apple identity token and authenticate user
		resp, err := s.AppleOAuthLogin(
			req.IdentityToken,
			req.UserIdentifier,
			req.Email,
			req.FullName.GivenName,
			req.FullName.FamilyName,
			req.IsFirstTimeSignIn,
		)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Apple authentication failed: " + err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data": gin.H{
				"token": resp.Token,
				"user":  resp.User,
			},
			"status": 200,
		})
	}
}

// @Summary Import User Data
// @Description Import user data from CSV file
// @Tags auth
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "CSV file to import"
// @Success 200 {object} map[string]interface{} "Import result"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /auth/import [post]
func ImportUserData(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		file, header, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(400, gin.H{
				"error":  "File upload failed",
				"status": 400,
			})
			return
		}
		defer file.Close()

		// TODO: Parse CSV and import data
		// For now, just return success
		c.JSON(200, gin.H{
			"success":  true,
			"message":  "Data imported successfully",
			"filename": header.Filename,
			"status":   200,
		})
	}
}

// @Summary Convert guest to registered user
// @Description Convert a guest user to a registered user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.GuestToRegisteredRequest true "Registration data"
// @Success 200 {object} map[string]interface{} "Returns JWT token and user data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /auth/guest/upgrade [post]
func ConvertGuestToRegistered(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GuestToRegisteredRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Guest to Registered Conversion Failed",
				Message: "Guest to Registered Conversion Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get guest ID from context
		guestID, exists := c.Get("guest_id")
		if !exists {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "guest ID not found in context",
				"status": 400,
			})
			return
		}

		resp, err := s.ConvertGuestToRegistered(guestID.(string), &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get transaction limit status
// @Description Get the current transaction limit status for the user
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Returns transaction limit status"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /auth/limit-status [get]
func GetTransactionLimitStatus(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		// Get transaction limit status
		status, err := s.GetTransactionLimitStatus(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   status,
			"status": 200,
		})
	}
}

func AppleCallback(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// c.JSON(200, gin.H{
		// 	"data":   resp,
		// 	"status": 200,
		// })

	}
}

// @Summary Delete user account
// @Description Soft delete the current user's account
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "Success message"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /auth/delete-account [delete]
func DeleteUserAccount(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		// Soft delete the user account
		if err := s.SoftDeleteUser(userID.String()); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Account deleted successfully",
			"status":  200,
		})
	}
}

// ChangePassword handles password change requests
func ChangePassword(s auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.ChangePasswordRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid request format",
				"message": "Geçersiz istek formatı",
				"status":  400,
			})
			return
		}

		// Get user ID from JWT token
		userID := state.GetCurrentUserID(c)

		// Change password
		if err := s.ChangePassword(userID.String(), req.CurrentPassword, req.NewPassword); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   err.Error(),
				"message": "Şifre değiştirilemedi: " + err.Error(),
				"status":  400,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Şifre başarıyla değiştirildi",
			"status":  200,
		})
	}
}
