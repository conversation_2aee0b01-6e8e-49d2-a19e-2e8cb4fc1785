package routes

import (
	"net/http"
	"strconv"

	"github.com/nocytech/butce360/pkg/database"
	"github.com/nocytech/butce360/pkg/entities"
	"github.com/nocytech/butce360/pkg/fin_notebooklog"
	"github.com/nocytech/butce360/pkg/middleware"
	"github.com/nocytech/butce360/pkg/state"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CategoryRoutes sets up category-related routes
func CategoryRoutes(router *gin.RouterGroup) {
	categories := router.Group("/categories")
	categories.Use(middleware.Authorized())

	categories.GET("", GetCategories)
	categories.GET("/:id", GetCategoryByID)
	categories.POST("", CreateCategory)
	categories.PUT("/:id", UpdateCategory)
	categories.DELETE("/:id", DeleteCategory)
	categories.GET("/:id/transactions", GetCategoryTransactions)
}

// GetCategories retrieves all categories for the authenticated user
func GetCategories(c *gin.Context) {
	userID := state.GetCurrentUserID(c)
	if userID == uuid.Nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var categories []entities.Category
	if err := database.DBClient().Where("user_id = ?", userID.String()).Find(&categories).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Get Categories Failed",
			Message: "Failed to fetch categories: " + err.Error(),
			Entity:  "category",
			Type:    "error",
			Ip:      c.ClientIP(),
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"categories": categories,
		"status":     200,
	})
}

// GetCategoryByID retrieves a specific category by ID
func GetCategoryByID(c *gin.Context) {
	userID := state.GetCurrentUserID(c)
	if userID == uuid.Nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	categoryID := c.Param("id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID is required"})
		return
	}

	var category entities.Category
	if err := database.DBClient().Where("id = ? AND user_id = ?", categoryID, userID.String()).First(&category).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"category": category,
		"status":   200,
	})
}

// CreateCategory creates a new category
func CreateCategory(c *gin.Context) {
	userID := state.GetCurrentUserID(c)
	if userID == uuid.Nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req struct {
		Name        string `json:"name" binding:"required"`
		Type        string `json:"type" binding:"required,oneof=income expense"`
		Icon        string `json:"icon"`
		Color       string `json:"color"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	category := entities.Category{
		Base: entities.Base{
			ID: uuid.New(),
		},
		Name:   req.Name,
		Type:   req.Type,
		Icon:   req.Icon,
		Color:  req.Color,
		UserID: userID,
	}

	if err := database.DBClient().Create(&category).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Create Category Failed",
			Message: "Failed to create category: " + err.Error(),
			Entity:  "category",
			Type:    "error",
			Ip:      c.ClientIP(),
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create category"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"category": category,
		"status":   201,
	})
}

// UpdateCategory updates an existing category
func UpdateCategory(c *gin.Context) {
	userID := state.GetCurrentUserID(c)
	if userID == uuid.Nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	categoryID := c.Param("id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID is required"})
		return
	}

	var req struct {
		Name        string `json:"name"`
		Type        string `json:"type" binding:"omitempty,oneof=income expense"`
		Icon        string `json:"icon"`
		Color       string `json:"color"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	var category entities.Category
	if err := database.DBClient().Where("id = ? AND user_id = ?", categoryID, userID.String()).First(&category).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Update fields if provided
	if req.Name != "" {
		category.Name = req.Name
	}
	if req.Type != "" {
		category.Type = req.Type
	}
	if req.Icon != "" {
		category.Icon = req.Icon
	}
	if req.Color != "" {
		category.Color = req.Color
	}

	if err := database.DBClient().Save(&category).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Update Category Failed",
			Message: "Failed to update category: " + err.Error(),
			Entity:  "category",
			Type:    "error",
			Ip:      c.ClientIP(),
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update category"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"category": category,
		"status":   200,
	})
}

// DeleteCategory deletes a category
func DeleteCategory(c *gin.Context) {
	userID := state.GetCurrentUserID(c)
	if userID == uuid.Nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	categoryID := c.Param("id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID is required"})
		return
	}

	var category entities.Category
	if err := database.DBClient().Where("id = ? AND user_id = ?", categoryID, userID.String()).First(&category).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Check if category has transactions
	var transactionCount int64
	database.DBClient().Model(&entities.Transaction{}).Where("category_id = ?", categoryID).Count(&transactionCount)

	if transactionCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Cannot delete category with existing transactions",
		})
		return
	}

	if err := database.DBClient().Delete(&category).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Delete Category Failed",
			Message: "Failed to delete category: " + err.Error(),
			Entity:  "category",
			Type:    "error",
			Ip:      c.ClientIP(),
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete category"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Category deleted successfully",
		"status":  200,
	})
}

// GetCategoryTransactions retrieves transactions for a specific category
func GetCategoryTransactions(c *gin.Context) {
	userID := state.GetCurrentUserID(c)
	if userID == uuid.Nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	categoryID := c.Param("id")
	if categoryID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Category ID is required"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset := (page - 1) * limit

	var transactions []entities.Transaction
	var total int64

	// Count total transactions
	database.DBClient().Model(&entities.Transaction{}).
		Where("category_id = ? AND user_id = ?", categoryID, userID.String()).
		Count(&total)

	// Get paginated transactions
	if err := database.DBClient().
		Where("category_id = ? AND user_id = ?", categoryID, userID.String()).
		Order("date DESC").
		Limit(limit).
		Offset(offset).
		Find(&transactions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch transactions"})
		return
	}

	totalPages := (int(total) + limit - 1) / limit

	c.JSON(http.StatusOK, gin.H{
		"transactions": transactions,
		"total":        total,
		"page":         page,
		"totalPages":   totalPages,
		"status":       200,
	})
}
