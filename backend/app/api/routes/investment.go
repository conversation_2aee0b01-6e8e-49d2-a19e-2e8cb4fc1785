package routes

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nocytech/butce360/pkg/domains/auth"
	"github.com/nocytech/butce360/pkg/domains/investment"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/middleware"
	"github.com/nocytech/butce360/pkg/state"
)

func InvestmentRoutes(r *gin.RouterGroup, s investment.Service, authService auth.Service) {
	g := r.Group("/investment")
	g.Use(middleware.Authorized())
	g.Use(middleware.RequiresPremium(authService)) // Premium required for all investment features

	g.POST("/simulate", SimulateInvestment(s))
	g.POST("/whatif", WhatIfSimulation(s))
	g.GET("/history", GetUserSimulations(s))
	g.DELETE("/history/:id", DeleteSimulation(s))
	g.POST("/historical-series", FetchHistoricalSeries(s))
}

// @Summary Simulate investment
// @Description Calculate how much an investment would be worth today
// @Tags investment
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.InvestmentSimulateRequest true "Investment simulation data"
// @Success 200 {object} map[string]interface{} "Returns investment simulation results"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /investment/simulate [post]
func SimulateInvestment(s investment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.InvestmentSimulateRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.SimulateInvestment(userID.String(), &req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   resp,
			"status": http.StatusOK,
		})
	}
}

// @Summary What-if investment simulation
// @Description Calculate what an investment would be worth if made on a specific date
// @Tags investment
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.InvestmentWhatIfRequest true "What-if simulation data"
// @Success 200 {object} map[string]interface{} "Returns what-if simulation results"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /investment/whatif [post]
func WhatIfSimulation(s investment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.InvestmentWhatIfRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.WhatIfSimulation(userID.String(), &req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   resp,
			"status": http.StatusOK,
		})
	}
}

// @Summary Get user investment simulations
// @Description Get user's investment simulation history
// @Tags investment
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} map[string]interface{} "Returns list of investment simulations"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /investment/history [get]
func GetUserSimulations(s investment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get pagination parameters
		page := 1
		limit := 10

		if pageParam := c.Query("page"); pageParam != "" {
			if p, err := parseIntParam(pageParam); err == nil && p > 0 {
				page = p
			}
		}

		if limitParam := c.Query("limit"); limitParam != "" {
			if l, err := parseIntParam(limitParam); err == nil && l > 0 && l <= 100 {
				limit = l
			}
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetUserSimulations(userID.String(), page, limit)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   resp,
			"status": http.StatusOK,
		})
	}
}

// @Summary Delete a simulation
// @Description Delete a user investment simulation by ID
// @Tags investment
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Simulation ID"
// @Success 200 {object} map[string]interface{} "Deleted"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /investment/history/{id} [delete]
func DeleteSimulation(s investment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "id is required", "status": http.StatusBadRequest})
			return
		}
		userID := state.GetCurrentUserID(c)
		if err := s.DeleteSimulation(userID.String(), id); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error(), "status": http.StatusBadRequest})
			return
		}
		c.JSON(http.StatusOK, gin.H{"data": "deleted", "status": http.StatusOK})
	}
}

// @Summary Fetch full daily price history
// @Description Fetch earliest-available daily prices to today for given assets. Uses Yahoo for non-crypto, Binance for crypto.
// @Tags investment
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.HistoricalSeriesRequest true "Assets to fetch"
// @Success 200 {object} map[string]interface{} "Returns historical series for assets"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /investment/historical-series [post]
func FetchHistoricalSeries(s investment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.HistoricalSeriesRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.FetchHistoricalSeries(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":   resp,
			"status": http.StatusOK,
		})
	}
}

// Helper function to parse integer parameters
func parseIntParam(param string) (int, error) {
	var result int
	if _, err := fmt.Sscanf(param, "%d", &result); err != nil {
		return 0, err
	}
	return result, nil
}
