app:
  name: fin_notebook
  port: 8008
  host:
  jwt_issuer: "fin_notebook"
  jwt_secret: "secret"
  jwt_expire: 24
  client_id: xxx
  onesignal_api_key: 123456
  force_update_key: xxx
google_oauth:
  client_id: "102312339260-euuf2l3bnlvf708n3504m2qk3gm7ss94.apps.googleusercontent.com"
  client_secret: "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"
  redirect_url: "http://localhost:8008/api/v1/auth/google/callback"
apple_signin:
  key: "ea2mdIxnmMFLfQzlcAP1E2lUwJnT1AbA"
database:
  host: localhost
  port: 5438
  user: fin_notebook-user
  pass: fin_notebook-pass
  name: fin_notebook
cloudinary:
  name: xxx
  api_key: xxx
  api_secret: xxx
  api_folder: xxx
allows:
  methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS
  headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  origins:
    - http://localhost:8008
    - https://app.butce360.com
    - https://butce360.com
