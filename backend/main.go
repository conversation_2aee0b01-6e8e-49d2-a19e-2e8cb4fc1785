package main

import (
	"github.com/nocytech/butce360/app/cmd"
)

// @title Gelir & Gider Takip Uygulaması API
// @version 1.0
// @description RESTful API for Income & Expense Tracking Application
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.nocytech.com/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1
// @schemes http https

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and the JWT token.

func main() {
	// Define command-line flags

	// Start the application
	cmd.StartApp()
}
