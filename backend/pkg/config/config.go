package config

import (
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
	"gopkg.in/yaml.v2"
)

// -------------------- Struct Definitions --------------------

type Config struct {
	App         App         `yaml:"app"`
	Redis       Redis       `yaml:"redis"`
	Database    Database    `yaml:"database"`
	Cloudinary  Cloudinary  `yaml:"cloudinary"`
	Allows      Allows      `yaml:"allows"`
	Whatsapp    Whatsapp    `yaml:"whatsapp"`
	GoogleOAuth GoogleOAuth `yaml:"google_oauth"`
	AppleSignIn AppleSignIn `yaml:"apple_signin"`
}

type App struct {
	Name            string `yaml:"name"`
	Port            string `yaml:"port"`
	Host            string `yaml:"host"`
	BaseUrl         string `yaml:"base_url"`
	JwtIssuer       string `yaml:"jwt_issuer"`
	JwtSecret       string `yaml:"jwt_secret"`
	JwtExpire       int    `yaml:"jwt_expire"`
	ClientID        string `yaml:"client_id"`
	OneSignalAPIKey string `yaml:"onesignal_api_key"`
	OneSignalAPPID  string `yaml:"onesignal_app_id"`
	ForceUpdateKey  string `yaml:"force_update_key"`
}

type Redis struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	Pass string `yaml:"pass"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Cloudinary struct {
	Name      string `yaml:"name"`
	APIKey    string `yaml:"api_key"`
	APISecret string `yaml:"api_secret"`
	APIFolder string `yaml:"api_folder"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

type Whatsapp struct {
	ApiKey string `yaml:"api_key"`
}

type GoogleOAuth struct {
	ClientID     string `yaml:"client_id"`
	ClientSecret string `yaml:"client_secret"`
	RedirectURL  string `yaml:"redirect_url"`
}

type AppleSignIn struct {
	Key string `yaml:"key"`
}

// -------------------- Global --------------------

var configs *Config

// -------------------- Load .env --------------------

func LoadEnv() {
	err := godotenv.Load()
	if err != nil {
		log.Println("No .env file found, skipping...")
	}
}

// -------------------- Read Config --------------------

func ReadValue() *Config {
	if configs != nil {
		return configs
	}

	LoadEnv()

	// Fallback için yaml oku
	var yamlConfig Config
	filename, err := filepath.Abs("./config.yaml")
	if err == nil {
		yamlFile, err := os.ReadFile(filename)
		if err == nil {
			yaml.Unmarshal(yamlFile, &yamlConfig)
		}
	}

	// Env öncelikli config
	configs = &Config{
		App: App{
			Name:            getEnv("APP_NAME", yamlConfig.App.Name),
			Port:            getEnv("APP_PORT", yamlConfig.App.Port),
			Host:            getEnv("APP_HOST", yamlConfig.App.Host),
			BaseUrl:         getEnv("APP_BASE_URL", yamlConfig.App.BaseUrl),
			JwtIssuer:       getEnv("APP_JWT_ISSUER", yamlConfig.App.JwtIssuer),
			JwtSecret:       getEnv("APP_JWT_SECRET", yamlConfig.App.JwtSecret),
			JwtExpire:       getEnvInt("APP_JWT_EXPIRE", yamlConfig.App.JwtExpire),
			ClientID:        getEnv("APP_CLIENT_ID", yamlConfig.App.ClientID),
			OneSignalAPIKey: getEnv("APP_ONESIGNAL_API_KEY", yamlConfig.App.OneSignalAPIKey),
			OneSignalAPPID:  getEnv("APP_ONESIGNAL_APP_ID", yamlConfig.App.OneSignalAPPID),
			ForceUpdateKey:  getEnv("APP_FORCE_UPDATE_KEY", yamlConfig.App.ForceUpdateKey),
		},
		Redis: Redis{
			Host: getEnv("REDIS_HOST", yamlConfig.Redis.Host),
			Port: getEnv("REDIS_PORT", yamlConfig.Redis.Port),
			Pass: getEnv("REDIS_PASS", yamlConfig.Redis.Pass),
		},
		Database: Database{
			Host: getEnv("DB_HOST", yamlConfig.Database.Host),
			Port: getEnv("DB_PORT", yamlConfig.Database.Port),
			User: getEnv("DB_USER", yamlConfig.Database.User),
			Pass: getEnv("DB_PASS", yamlConfig.Database.Pass),
			Name: getEnv("DB_NAME", yamlConfig.Database.Name),
		},
		Cloudinary: Cloudinary{
			Name:      getEnv("CLOUDINARY_NAME", yamlConfig.Cloudinary.Name),
			APIKey:    getEnv("CLOUDINARY_API_KEY", yamlConfig.Cloudinary.APIKey),
			APISecret: getEnv("CLOUDINARY_API_SECRET", yamlConfig.Cloudinary.APISecret),
			APIFolder: getEnv("CLOUDINARY_API_FOLDER", yamlConfig.Cloudinary.APIFolder),
		},
		Allows: Allows{
			Methods: getEnvSlice("ALLOWS_METHODS", yamlConfig.Allows.Methods),
			Origins: getEnvSlice("ALLOWS_ORIGINS", yamlConfig.Allows.Origins),
			Headers: getEnvSlice("ALLOWS_HEADERS", yamlConfig.Allows.Headers),
		},
		Whatsapp: Whatsapp{
			ApiKey: getEnv("WHATSAPP_API_KEY", yamlConfig.Whatsapp.ApiKey),
		},
		GoogleOAuth: GoogleOAuth{
			ClientID:     getEnv("GOOGLE_CLIENT_ID", yamlConfig.GoogleOAuth.ClientID),
			ClientSecret: getEnv("GOOGLE_CLIENT_SECRET", yamlConfig.GoogleOAuth.ClientSecret),
			RedirectURL:  getEnv("GOOGLE_REDIRECT_URL", yamlConfig.GoogleOAuth.RedirectURL),
		},
		AppleSignIn: AppleSignIn{
			Key: getEnv("APPLE_SIGNIN_KEY", yamlConfig.AppleSignIn.Key),
		},
	}

	return configs
}

// -------------------- Helper Functions --------------------

func getEnv(key string, fallback string) string {
	if val := os.Getenv(key); val != "" {
		return val
	}
	return fallback
}

func getEnvInt(key string, fallback int) int {
	if val := os.Getenv(key); val != "" {
		if i, err := strconv.Atoi(val); err == nil {
			return i
		}
	}
	return fallback
}

func getEnvSlice(key string, fallback []string) []string {
	if val := os.Getenv(key); val != "" {
		return strings.Split(val, ",")
	}
	return fallback
}
