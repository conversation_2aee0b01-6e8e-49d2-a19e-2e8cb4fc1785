package controllers

import (
	"net/http"

	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/services"
	"github.com/gin-gonic/gin"
)

type OAuthController struct {
	oauthService *services.OAuthService
}

func NewOAuthController(oauthService *services.OAuthService) *OAuthController {
	return &OAuthController{
		oauthService: oauthService,
	}
}

// Google OAuth Login
// @Summary Google OAuth Login
// @Description Authenticate user with Google OAuth
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dtos.GoogleOAuthRequest true "Google OAuth Request"
// @Success 200 {object} dtos.OAuthResponse
// @Failure 400 {object} dtos.ErrorResponse
// @Failure 401 {object} dtos.ErrorResponse
// @Router /auth/google [post]
func (c *OAuthController) GoogleLogin(ctx *gin.Context) {
	var req dtos.GoogleOAuthRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"Error":   "Invalid request format",
			"Message": err.Error(),
		})
		return
	}

	response, err := c.oauthService.AuthenticateWithGoogle(&req)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, response)
		return
	}

	if !response.Success {
		ctx.JSON(http.StatusUnauthorized, response)
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// Apple OAuth Login
// @Summary Apple OAuth Login
// @Description Authenticate user with Apple OAuth
// @Tags auth
// @Accept json
// @Produce json
// @Param request body dtos.AppleOAuthRequest true "Apple OAuth Request"
// @Success 200 {object} dtos.OAuthResponse
// @Failure 400 {object} dtos.ErrorResponse
// @Failure 401 {object} dtos.ErrorResponse
// @Router /auth/apple [post]
func (c *OAuthController) AppleLogin(ctx *gin.Context) {
	var req dtos.AppleOAuthRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"Error":   "Invalid request format",
			"Message": err.Error(),
		})
		return
	}

	response, err := c.oauthService.AuthenticateWithApple(&req)
	if err != nil {
		ctx.JSON(http.StatusUnauthorized, response)
		return
	}

	if !response.Success {
		ctx.JSON(http.StatusUnauthorized, response)
		return
	}

	ctx.JSON(http.StatusOK, response)
}
