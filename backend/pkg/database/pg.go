package database

import (
	"fmt"
	"log"
	"os"

	"github.com/nocytech/butce360/pkg/config"
	"github.com/nocytech/butce360/pkg/entities"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	db  *gorm.DB
	err error
	//client_once sync.Once
)

func InitDB(dbc config.Database) {

	// Print database configuration for debugging
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Europe/Istanbul", dbc.Host, dbc.Port, dbc.User, dbc.Pass, dbc.Name)
	db, err = gorm.Open(
		postgres.New(
			postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true, // daha az kaynak
			},
		),
		&gorm.Config{
			Logger: logger.New(log.New(os.Stdout, "[gorm] ", log.LstdFlags), logger.Config{
				SlowThreshold:             0,
				LogLevel:                  logger.Warn,
				IgnoreRecordNotFoundError: true,
				Colorful:                  false,
			}),
		},
	)
	if err != nil {
		panic(err)
	}
	fmt.Println("Successfully connected to the database!")

	if err := db.AutoMigrate(
		&entities.Log{},
		&entities.Version{},
		&entities.User{},
		&entities.Transaction{},
		&entities.Category{},
		&entities.Account{},
		&entities.Budget{},
		&entities.RecurringTransaction{},
		&entities.TokenBlacklist{},
		&entities.BankStatementEntry{},
		&entities.InvestmentSimulation{},
		&entities.DailyPrice{},
		&entities.Subscription{},
	); err != nil {
		log.Printf("[migrate] AutoMigrate error: %v", err)
	}

	// Ensure critical columns exist for DailyPrice even if AutoMigrate was partially applied
	ensureDailyPriceSchema(db)

	SeedData(db)

}

func ensureDailyPriceSchema(db *gorm.DB) {
	// Add columns if missing (safe, idempotent)
	// Some deployments may have NOT NULL constraints; run simple IF NOT EXISTS to avoid errors
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS symbol TEXT")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS asset TEXT")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS date TIMESTAMP WITH TIME ZONE")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS price DOUBLE PRECISION")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS open DOUBLE PRECISION")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS high DOUBLE PRECISION")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS low DOUBLE PRECISION")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS close DOUBLE PRECISION")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS volume DOUBLE PRECISION")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS currency TEXT")
	db.Exec("ALTER TABLE daily_prices ADD COLUMN IF NOT EXISTS source TEXT")
	// Indexes (unique on symbol+date)
	db.Exec("CREATE UNIQUE INDEX IF NOT EXISTS uq_symbol_date ON daily_prices(symbol, date)")
}

func DBClient() *gorm.DB {
	if db == nil {
		log.Panic("Postgres is not initialized. Call InitDB first.")
	}
	return db
}
