package account

import (
	"errors"

	"github.com/nocytech/butce360/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	Create(account *entities.Account) error
	FindByID(id uuid.UUID) (*entities.Account, error)
	Update(account *entities.Account) error
	Delete(id uuid.UUID) error
	FindAll(userID uuid.UUID) ([]entities.Account, error)
	UpdateBalance(id uuid.UUID, amount float64) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(account *entities.Account) error {
	return r.db.Create(account).Error
}

func (r *repository) FindByID(id uuid.UUID) (*entities.Account, error) {
	var account entities.Account
	if err := r.db.Where("id = ?", id).First(&account).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("account not found")
		}
		return nil, err
	}
	return &account, nil
}

func (r *repository) Update(account *entities.Account) error {
	return r.db.Save(account).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	// Check if account is used in transactions
	var count int64
	if err := r.db.Model(&entities.Transaction{}).Where("account_id = ?", id).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("account is used in transactions")
	}

	return r.db.Delete(&entities.Account{}, "id = ?", id).Error
}

func (r *repository) FindAll(userID uuid.UUID) ([]entities.Account, error) {
	var accounts []entities.Account
	if err := r.db.Where("user_id = ?", userID).Order("name ASC").Find(&accounts).Error; err != nil {
		return nil, err
	}
	return accounts, nil
}

func (r *repository) UpdateBalance(id uuid.UUID, amount float64) error {
	return r.db.Model(&entities.Account{}).Where("id = ?", id).
		UpdateColumn("balance", gorm.Expr("balance + ?", amount)).Error
}
