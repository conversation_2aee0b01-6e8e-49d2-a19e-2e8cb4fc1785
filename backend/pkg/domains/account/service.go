package account

import (
	"errors"

	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
	"github.com/google/uuid"
)

type Service interface {
	CreateAccount(userID string, req *dtos.AccountRequest) (*dtos.AccountResponse, error)
	GetAccountByID(id string) (*dtos.AccountResponse, error)
	UpdateAccount(id string, req *dtos.AccountRequest) (*dtos.AccountResponse, error)
	DeleteAccount(id string) error
	GetAllAccounts(userID string) ([]dtos.AccountResponse, error)
	UpdateBalance(id string, amount float64) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateAccount(userID string, req *dtos.AccountRequest) (*dtos.AccountResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Create account entity
	account := &entities.Account{
		Name:     req.Name,
		Type:     req.Type,
		Balance:  req.Balance,
		Currency: req.Currency,
		UserID:   userUUID,
	}

	// Save account
	if err := s.repository.Create(account); err != nil {
		return nil, err
	}

	// Convert to response
	return s.toAccountResponse(account), nil
}

func (s *service) GetAccountByID(id string) (*dtos.AccountResponse, error) {
	// Parse UUID
	accountUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid account ID")
	}

	// Get account
	account, err := s.repository.FindByID(accountUUID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toAccountResponse(account), nil
}

func (s *service) UpdateAccount(id string, req *dtos.AccountRequest) (*dtos.AccountResponse, error) {
	// Parse UUID
	accountUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid account ID")
	}

	// Get account
	account, err := s.repository.FindByID(accountUUID)
	if err != nil {
		return nil, err
	}

	// Update account fields
	if req.Name != "" {
		account.Name = req.Name
	}
	if req.Type != "" {
		account.Type = req.Type
	}
	if req.Currency != "" {
		account.Currency = req.Currency
	}
	// Only update balance if it's explicitly set
	if req.Balance != account.Balance {
		account.Balance = req.Balance
	}

	// Save account
	if err := s.repository.Update(account); err != nil {
		return nil, err
	}

	// Convert to response
	return s.toAccountResponse(account), nil
}

func (s *service) DeleteAccount(id string) error {
	// Parse UUID
	accountUUID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid account ID")
	}

	// Delete account
	return s.repository.Delete(accountUUID)
}

func (s *service) GetAllAccounts(userID string) ([]dtos.AccountResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Get accounts
	accounts, err := s.repository.FindAll(userUUID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	var response []dtos.AccountResponse
	for _, account := range accounts {
		response = append(response, *s.toAccountResponse(&account))
	}

	return response, nil
}

func (s *service) UpdateBalance(id string, amount float64) error {
	// Parse UUID
	accountUUID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid account ID")
	}

	// Update balance
	return s.repository.UpdateBalance(accountUUID, amount)
}

func (s *service) toAccountResponse(account *entities.Account) *dtos.AccountResponse {
	return &dtos.AccountResponse{
		ID:        account.ID.String(),
		Name:      account.Name,
		Type:      account.Type,
		Balance:   account.Balance,
		Currency:  account.Currency,
		CreatedAt: account.CreatedAt,
		UpdatedAt: account.UpdatedAt,
	}
}
