package category

import (
	"errors"

	"github.com/nocytech/butce360/pkg/entities"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	Create(category *entities.Category) error
	FindByID(id uuid.UUID) (*entities.Category, error)
	Update(category *entities.Category) error
	Delete(id uuid.UUID) error
	FindAll(userID uuid.UUID, categoryType string) ([]entities.Category, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(category *entities.Category) error {
	return r.db.Create(category).Error
}

func (r *repository) FindByID(id uuid.UUID) (*entities.Category, error) {
	var category entities.Category
	if err := r.db.Where("id = ?", id).First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("category not found")
		}
		return nil, err
	}
	return &category, nil
}

func (r *repository) Update(category *entities.Category) error {
	return r.db.Save(category).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	// Check if category is used in transactions
	var count int64
	if err := r.db.Model(&entities.Transaction{}).Where("category_id = ?", id).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("category is used in transactions")
	}

	return r.db.Delete(&entities.Category{}, "id = ?", id).Error
}

func (r *repository) FindAll(userID uuid.UUID, categoryType string) ([]entities.Category, error) {
	var categories []entities.Category
	query := r.db.Where("user_id = ?", userID)

	if categoryType != "" {
		query = query.Where("type = ?", categoryType)
	}

	if err := query.Order("name ASC").Find(&categories).Error; err != nil {
		return nil, err
	}

	return categories, nil
}
