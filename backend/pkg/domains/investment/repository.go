package investment

import (
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/entities"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Repository interface {
	Create(simulation *entities.InvestmentSimulation) error
	FindByID(id uuid.UUID) (*entities.InvestmentSimulation, error)
	FindByUserID(userID uuid.UUID, limit int, offset int) ([]entities.InvestmentSimulation, error)
	Update(simulation *entities.InvestmentSimulation) error
	Delete(id uuid.UUID) error
	GetTotalCountByUserID(userID uuid.UUID) (int64, error)
	// Cached price helpers (best-effort; fall back to external APIs if not found)
	GetCachedHistoricalPrice(asset string, date time.Time) (float64, bool, error)
	GetCachedCurrentPrice(asset string) (float64, bool, error)

	GetDailyPriceByDate(asset string, date time.Time) (float64, bool, error)
	GetDailyPriceExact(asset string, date time.Time) (float64, bool, error)
	UpsertDailyPrices(prices []entities.DailyPrice) error
	GetLatestDailyPriceDate(asset string) (time.Time, bool, error)
	GetAllDailyPrices(asset string) ([]entities.DailyPrice, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(simulation *entities.InvestmentSimulation) error {
	return r.db.Create(simulation).Error
}

func (r *repository) FindByID(id uuid.UUID) (*entities.InvestmentSimulation, error) {
	var simulation entities.InvestmentSimulation
	err := r.db.Where("id = ?", id).First(&simulation).Error
	if err != nil {
		return nil, err
	}
	return &simulation, nil
}

func (r *repository) FindByUserID(userID uuid.UUID, limit int, offset int) ([]entities.InvestmentSimulation, error) {
	var simulations []entities.InvestmentSimulation
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&simulations).Error
	if err != nil {
		return nil, err
	}
	return simulations, nil
}

func (r *repository) Update(simulation *entities.InvestmentSimulation) error {
	return r.db.Save(simulation).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.InvestmentSimulation{}, id).Error
}

func (r *repository) GetTotalCountByUserID(userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.Model(&entities.InvestmentSimulation{}).
		Where("user_id = ?", userID).
		Count(&count).Error
	return count, err
}

// GetCachedHistoricalPrice tries to reuse a previously stored price for an asset at a given date
func (r *repository) GetCachedHistoricalPrice(asset string, date time.Time) (float64, bool, error) {
	norm := strings.ToUpper(asset)

	// 1) Prefer simulations with PriceAtStart on the exact StartDate
	var sim entities.InvestmentSimulation
	err := r.db.Where("asset = ? AND DATE(start_date) = DATE(?) AND price_at_start > 0", norm, date).
		Order("created_at DESC").
		First(&sim).Error
	if err == nil {
		return sim.PriceAtStart, true, nil
	}
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, false, err
	}

	// 2) Fall back to what-if simulations with PriceThen for the same date
	sim = entities.InvestmentSimulation{}
	err = r.db.Where("asset = ? AND DATE(hypothetical_date) = DATE(?) AND price_then IS NOT NULL", norm, date).
		Order("created_at DESC").
		First(&sim).Error
	if err == nil && sim.PriceThen != nil && *sim.PriceThen > 0 {
		return *sim.PriceThen, true, nil
	}
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, false, err
	}

	return 0, false, nil
}

// GetCachedCurrentPrice tries to reuse a recently stored current price for an asset
func (r *repository) GetCachedCurrentPrice(asset string) (float64, bool, error) {
	norm := strings.ToUpper(asset)

	// 1) Try simulations with CurrentPrice
	var sim entities.InvestmentSimulation
	err := r.db.Where("asset = ? AND current_price > 0", norm).
		Order("updated_at DESC").
		First(&sim).Error
	if err == nil {
		return sim.CurrentPrice, true, nil
	}
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, false, err
	}

	// 2) Fall back to what-if simulations with PriceNow
	sim = entities.InvestmentSimulation{}
	err = r.db.Where("asset = ? AND price_now IS NOT NULL", norm).
		Order("updated_at DESC").
		First(&sim).Error
	if err == nil && sim.PriceNow != nil && *sim.PriceNow > 0 {
		return *sim.PriceNow, true, nil
	}
	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, false, err
	}

	return 0, false, nil
}

// GetDailyPriceByDate returns the stored close price for an asset on the given date.
// If no exact match exists (e.g., weekend/holiday), it returns the nearest prior date's price.
func (r *repository) GetDailyPriceByDate(asset string, date time.Time) (float64, bool, error) {
	norm := strings.ToUpper(asset)
	d := date.UTC()
	d = time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, time.UTC)

	var p entities.DailyPrice
	err := r.db.Where("symbol = ? AND date <= ?", norm, d).Order("date DESC").First(&p).Error
	if err == gorm.ErrRecordNotFound {
		return 0, false, nil
	}
	if err != nil {
		return 0, false, err
	}
	return p.Price, true, nil
}

// GetDailyPriceExact returns the stored close price only if an exact date row exists
func (r *repository) GetDailyPriceExact(asset string, date time.Time) (float64, bool, error) {
	norm := strings.ToUpper(asset)
	d := date.UTC()
	d = time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, time.UTC)

	var p entities.DailyPrice
	err := r.db.Where("symbol = ? AND date = ?", norm, d).First(&p).Error
	if err == gorm.ErrRecordNotFound {
		return 0, false, nil
	}
	if err != nil {
		return 0, false, err
	}
	return p.Price, true, nil
}

// UpsertDailyPrices inserts or updates a batch of daily prices by (asset,date) unique key
func (r *repository) UpsertDailyPrices(prices []entities.DailyPrice) error {
	if len(prices) == 0 {
		return nil
	}
	// Ensure symbol is uppercased and dates are UTC midnight
	for i := range prices {
		prices[i].Symbol = strings.ToUpper(prices[i].Symbol)
		d := prices[i].Date.UTC()
		prices[i].Date = time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, time.UTC)
	}

	// Use upsert on (symbol,date)
	return r.db.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "symbol"}, {Name: "date"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"asset", "price", "open", "high", "low", "close", "volume", "currency", "source", "updated_at",
		}),
	}).Create(&prices).Error
}

// GetLatestDailyPriceDate returns the most recent date stored for an asset
func (r *repository) GetLatestDailyPriceDate(asset string) (time.Time, bool, error) {
	var p entities.DailyPrice
	err := r.db.Where("symbol = ?", strings.ToUpper(asset)).Order("date DESC").First(&p).Error
	if err == gorm.ErrRecordNotFound {
		return time.Time{}, false, nil
	}
	if err != nil {
		return time.Time{}, false, err
	}
	return p.Date, true, nil
}

// GetAllDailyPrices returns all stored daily prices for an asset ordered by date asc
func (r *repository) GetAllDailyPrices(asset string) ([]entities.DailyPrice, error) {
	var items []entities.DailyPrice
	err := r.db.Where("symbol = ?", strings.ToUpper(asset)).Order("date ASC").Find(&items).Error
	return items, err
}
