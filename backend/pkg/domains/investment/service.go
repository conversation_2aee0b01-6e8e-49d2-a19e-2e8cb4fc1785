package investment

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
	xcharset "golang.org/x/net/html/charset"
)

var httpClient = &http.Client{Timeout: 15 * time.Second}
var priceSyncVerbose bool
var priceSyncFillWeekends bool

// itemAsString safely converts a mixed-type kline field to string
func itemAsString(item []interface{}, idx int) (string, bool) {
	if idx >= len(item) {
		return "", false
	}
	if s, ok := item[idx].(string); ok {
		return s, true
	}
	if f, ok := item[idx].(float64); ok {
		return fmt.Sprintf("%f", f), true
	}
	return "", false
}

type Service interface {
	SimulateInvestment(userID string, req *dtos.InvestmentSimulateRequest) (*dtos.InvestmentSimulateResponse, error)
	WhatIfSimulation(userID string, req *dtos.InvestmentWhatIfRequest) (*dtos.InvestmentWhatIfResponse, error)
	GetUserSimulations(userID string, page int, limit int) ([]dtos.InvestmentSimulateResponse, error)
	// FetchHistoricalSeries returns full daily price history for given assets
	FetchHistoricalSeries(req *dtos.HistoricalSeriesRequest) (*dtos.HistoricalSeriesResponse, error)
	// StartBackgroundPriceSync kicks off incremental filling of missing daily prices
	StartBackgroundPriceSync()
	// Delete a simulation by ID (must belong to user)
	DeleteSimulation(userID string, id string) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) SimulateInvestment(userID string, req *dtos.InvestmentSimulateRequest) (*dtos.InvestmentSimulateResponse, error) {
	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Parse start date
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, errors.New("invalid start date format, use YYYY-MM-DD")
	}

	// Get historical price for start date
	priceAtStart, err := s.getHistoricalPrice(req.Asset, startDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get historical price: %w", err)
	}

	// Get current price
	currentPrice, err := s.getCurrentPrice(req.Asset)
	if err != nil {
		return nil, fmt.Errorf("failed to get current price: %w", err)
	}

	// Create simulation entity
	simulation := &entities.InvestmentSimulation{
		UserID:         userUUID,
		Asset:          req.Asset,
		AmountInvested: req.Amount,
		StartDate:      startDate,
		PriceAtStart:   priceAtStart,
		CurrentPrice:   currentPrice,
		SimulationType: "simulate",
	}

	// Calculate investment metrics
	simulation.CalculateInvestment()

	// Save to database
	if err := s.repository.Create(simulation); err != nil {
		return nil, fmt.Errorf("failed to save simulation: %w", err)
	}

	// Return response
	return &dtos.InvestmentSimulateResponse{
		ID:                simulation.ID.String(),
		Asset:             simulation.Asset,
		AmountInvested:    simulation.AmountInvested,
		StartDate:         simulation.StartDate.Format("2006-01-02"),
		PriceAtStart:      simulation.PriceAtStart,
		CurrentPrice:      simulation.CurrentPrice,
		UnitsBought:       simulation.UnitsBought,
		CurrentValue:      simulation.CurrentValue,
		Profit:            simulation.Profit,
		GrowthRatePercent: simulation.GrowthRatePercent,
	}, nil
}

func (s *service) WhatIfSimulation(userID string, req *dtos.InvestmentWhatIfRequest) (*dtos.InvestmentWhatIfResponse, error) {
	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Parse hypothetical date
	hypotheticalDate, err := time.Parse("2006-01-02", req.HypotheticalDate)
	if err != nil {
		return nil, errors.New("invalid hypothetical date format, use YYYY-MM-DD")
	}

	// Get historical price for hypothetical date
	priceThen, err := s.getHistoricalPrice(req.Asset, hypotheticalDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get historical price: %w", err)
	}

	// Get current price
	priceNow, err := s.getCurrentPrice(req.Asset)
	if err != nil {
		return nil, fmt.Errorf("failed to get current price: %w", err)
	}

	// Create simulation entity (in-memory only, no database save)
	simulation := &entities.InvestmentSimulation{
		UserID:           userUUID,
		Asset:            req.Asset,
		AmountInvested:   req.Amount,
		StartDate:        hypotheticalDate,
		SimulationType:   "whatif",
		HypotheticalDate: &hypotheticalDate,
		PriceThen:        &priceThen,
		PriceNow:         &priceNow,
	}

	// Calculate what-if metrics
	simulation.CalculateWhatIf()

	// Return response without saving to database
	return &dtos.InvestmentWhatIfResponse{
		Asset:             simulation.Asset,
		HypotheticalDate:  simulation.HypotheticalDate.Format("2006-01-02"),
		AmountInvested:    simulation.AmountInvested,
		PriceThen:         *simulation.PriceThen,
		PriceNow:          *simulation.PriceNow,
		CurrentValue:      simulation.CurrentValue,
		Profit:            simulation.Profit,
		GrowthRatePercent: simulation.GrowthRatePercent,
	}, nil
}

func (s *service) GetUserSimulations(userID string, page int, limit int) ([]dtos.InvestmentSimulateResponse, error) {
	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get simulations from repository
	simulations, err := s.repository.FindByUserID(userUUID, limit, offset)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	var responses []dtos.InvestmentSimulateResponse
	for _, sim := range simulations {
		response := dtos.InvestmentSimulateResponse{
			ID:                sim.ID.String(),
			Asset:             sim.Asset,
			AmountInvested:    sim.AmountInvested,
			StartDate:         sim.StartDate.Format("2006-01-02"),
			PriceAtStart:      sim.PriceAtStart,
			CurrentPrice:      sim.CurrentPrice,
			UnitsBought:       sim.UnitsBought,
			CurrentValue:      sim.CurrentValue,
			Profit:            sim.Profit,
			GrowthRatePercent: sim.GrowthRatePercent,
		}
		responses = append(responses, response)
	}

	return responses, nil
}

func (s *service) DeleteSimulation(userID string, id string) error {
	uid, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}
	simID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid simulation ID")
	}
	sim, err := s.repository.FindByID(simID)
	if err != nil {
		return err
	}
	if sim.UserID != uid {
		return errors.New("forbidden")
	}
	return s.repository.Delete(simID)
}

// FetchHistoricalSeries returns full daily price history for given assets
func (s *service) FetchHistoricalSeries(req *dtos.HistoricalSeriesRequest) (*dtos.HistoricalSeriesResponse, error) {
	if req == nil || len(req.Assets) == 0 {
		return nil, errors.New("assets list is required")
	}

	var result []dtos.AssetHistory
	for _, asset := range req.Assets {
		a := strings.ToUpper(strings.TrimSpace(asset))
		if a == "" {
			continue
		}

		var prices []dtos.AssetPriceData

		if s.isCryptoAsset(a) {
			// Crypto incremental sync using Binance, then return from DB
			now := time.Now().UTC()
			last, ok, err := s.repository.GetLatestDailyPriceDate(a)
			if err != nil {
				return nil, err
			}
			var start time.Time
			if ok {
				start = last.AddDate(0, 0, 1)
			} else {
				start = time.Unix(0, 0).UTC()
			}
			if !start.After(now) {
				batch, berr := s.getBinanceDailyHistoryRange(a, start, now)
				if berr != nil {
					return nil, fmt.Errorf("failed binance fetch for %s: %w", a, berr)
				}
				if err := s.saveDailyPrices("BINANCE", batch); err != nil {
					return nil, err
				}
			}
			stored, derr := s.repository.GetAllDailyPrices(a)
			if derr != nil {
				return nil, derr
			}
			prices = make([]dtos.AssetPriceData, 0, len(stored))
			for _, p := range stored {
				prices = append(prices, dtos.AssetPriceData{Asset: p.Symbol, Price: p.Price, Date: p.Date, Currency: p.Currency})
			}
		} else if a == "USDTRY" || a == "EURTRY" {
			// TCMB incremental sync, then return from DB
			now := time.Now().UTC()
			last, ok, err := s.repository.GetLatestDailyPriceDate(a)
			if err != nil {
				return nil, err
			}
			var start time.Time
			if ok {
				start = last.AddDate(0, 0, 1)
			} else {
				start = time.Date(2002, 1, 2, 0, 0, 0, 0, time.UTC)
			}
			if !start.After(now) {
				if err := s.syncFXFromTCMB(a, start, now); err != nil {
					return nil, err
				}
			}
			stored, derr := s.repository.GetAllDailyPrices(a)
			if derr != nil {
				return nil, derr
			}
			prices = make([]dtos.AssetPriceData, 0, len(stored))
			for _, p := range stored {
				prices = append(prices, dtos.AssetPriceData{Asset: p.Symbol, Price: p.Price, Date: p.Date, Currency: p.Currency})
			}
		} else if a == "GOLD" {
			// GOLD via Stooq XAUUSD + TCMB USDTRY
			now := time.Now().UTC()
			last, ok, err := s.repository.GetLatestDailyPriceDate("GOLD")
			if err != nil {
				return nil, err
			}
			var start time.Time
			if ok {
				start = last.AddDate(0, 0, 1)
			} else {
				start = time.Date(2002, 1, 2, 0, 0, 0, 0, time.UTC)
			}
			if !start.After(now) {
				// ensure FX present
				_ = s.syncFXFromTCMB("USDTRY", start, now)
				if err := s.syncGoldFromStooqUsingTCMB(start, now); err != nil {
					return nil, err
				}
			}
			stored, derr := s.repository.GetAllDailyPrices("GOLD")
			if derr != nil {
				return nil, derr
			}
			prices = make([]dtos.AssetPriceData, 0, len(stored))
			for _, p := range stored {
				prices = append(prices, dtos.AssetPriceData{Asset: p.Symbol, Price: p.Price, Date: p.Date, Currency: p.Currency})
			}
		} else if a == "BIST100" {
			// BIST100 via Stooq ^xu100
			now := time.Now().UTC()
			last, ok, err := s.repository.GetLatestDailyPriceDate("BIST100")
			if err != nil {
				return nil, err
			}
			var start time.Time
			if ok {
				start = last.AddDate(0, 0, 1)
			} else {
				start = time.Date(2002, 1, 2, 0, 0, 0, 0, time.UTC)
			}
			if !start.After(now) {
				if err := s.syncBISTFromStooq(start, now); err != nil {
					return nil, err
				}
			}
			stored, derr := s.repository.GetAllDailyPrices("BIST100")
			if derr != nil {
				return nil, derr
			}
			prices = make([]dtos.AssetPriceData, 0, len(stored))
			for _, p := range stored {
				prices = append(prices, dtos.AssetPriceData{Asset: p.Symbol, Price: p.Price, Date: p.Date, Currency: p.Currency})
			}
		} else {
			// Yahoo incremental daily sync to avoid 429
			now := time.Now().UTC()
			last, ok, err := s.repository.GetLatestDailyPriceDate(a)
			if err != nil {
				return nil, err
			}
			var start time.Time
			if ok {
				start = last.AddDate(0, 0, 1)
			} else {
				first, ferr := s.getYahooFirstTradeDate(a)
				if ferr != nil {
					return nil, ferr
				}
				d := first.UTC()
				start = time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, time.UTC)
			}
			if !start.After(now) {
				batch, berr := s.fetchYahooDailyRange(a, start, now)
				if berr != nil {
					return nil, berr
				}
				if err := s.saveDailyPrices("YAHOO", batch); err != nil {
					return nil, err
				}
			}
			stored, derr := s.repository.GetAllDailyPrices(a)
			if derr != nil {
				return nil, derr
			}
			prices = make([]dtos.AssetPriceData, 0, len(stored))
			for _, p := range stored {
				prices = append(prices, dtos.AssetPriceData{Asset: p.Symbol, Price: p.Price, Date: p.Date, Currency: p.Currency})
			}
		}

		result = append(result, dtos.AssetHistory{Asset: a, Prices: prices})
	}

	return &dtos.HistoricalSeriesResponse{Assets: result}, nil
}

// StartBackgroundPriceSync launches a background goroutine to incrementally
// fetch and persist missing daily prices for a default set of assets.
func (s *service) StartBackgroundPriceSync() {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				//log.Printf("[price-sync] recovered from panic: %v", r)
			}
		}()
		// Verbose logging toggle (set PRICE_SYNC_VERBOSE=1 to enable per-day logs)
		if v := os.Getenv("PRICE_SYNC_VERBOSE"); strings.EqualFold(v, "1") || strings.EqualFold(v, "true") || strings.EqualFold(v, "yes") {
			priceSyncVerbose = true
		}
		if v := os.Getenv("PRICE_SYNC_FILL_WEEKENDS"); strings.EqualFold(v, "1") || strings.EqualFold(v, "true") || strings.EqualFold(v, "yes") {
			priceSyncFillWeekends = true
		}
		//log.Printf("[price-sync] background sync starting... verbose=%v fill_weekends=%v", priceSyncVerbose, priceSyncFillWeekends)
		// Initial backfill using TCMB and Stooq for FX and GOLD
		if err := s.backfillTCMBFX(); err != nil {
			//	log.Printf("[price-sync] TCMB FX backfill error: %v", err)
		}
		if err := s.backfillGoldFromStooqUsingTCMB(); err != nil {
			//	log.Printf("[price-sync] GOLD backfill error: %v", err)
		}
		assets := []string{
			// Crypto (Binance) first so we see quick inserts
			"BTC", "ETH", "ADA", "DOT", "LINK", "LTC", "XRP", "BCH", "BNB", "DOGE",
			// Non-crypto (Yahoo)
			"GOLD", "USDTRY", "EURTRY", "BIST100", "SPY", "QQQ",
		}
		// Also schedule a daily midnight sync for the previous day's data
		s.scheduleDailyMidnightSync(append([]string{}, assets...))
		// Run initial catch-up once at startup, then periodic refresh
		for {
			for _, a := range assets {
				if err := s.syncAssetIncremental(a); err != nil {
					//	log.Printf("[price-sync] %s sync error: %v", a, err)
				}
				// small pause between assets to be gentle on APIs
				time.Sleep(2 * time.Second)
			}
			// sleep before next round
			time.Sleep(6 * time.Hour)
		}
	}()
}

// scheduleDailyMidnightSync triggers an incremental sync shortly after local midnight
// to ensure the previous day's data is captured in the database.
func (s *service) scheduleDailyMidnightSync(assets []string) {
	go func() {
		for {
			now := time.Now().In(time.Local)
			// next local midnight
			nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, time.Local)
			// run a few minutes after midnight to allow data availability
			runAt := nextMidnight.Add(5 * time.Minute)
			time.Sleep(time.Until(runAt))

			log.Printf("[price-sync-cron] running daily sync at %s", time.Now().Format(time.RFC3339))
			for _, a := range assets {
				if err := s.syncAssetIncremental(a); err != nil {
					//			log.Printf("[price-sync-cron] %s sync error: %v", a, err)
				}
				time.Sleep(2 * time.Second)
			}
		}
	}()
}

// syncAssetIncremental fills missing prices for a single asset up to now
func (s *service) syncAssetIncremental(asset string) error {
	a := strings.ToUpper(strings.TrimSpace(asset))
	if a == "" {
		return nil
	}
	now := time.Now().UTC()
	last, ok, err := s.repository.GetLatestDailyPriceDate(a)

	if err != nil {
		//return err
	}
	var start time.Time
	if ok {
		start = last.AddDate(0, 0, 1)
	} else if s.isCryptoAsset(a) {
		start = time.Unix(0, 0).UTC()
	} else if a == "USDTRY" || a == "EURTRY" || a == "GOLD" || a == "BIST100" {
		start = time.Date(2002, 1, 2, 0, 0, 0, 0, time.UTC)
	}
	if s.isCryptoAsset(a) {
		if start.After(now) {
			return nil
		}
		batch, berr := s.getBinanceDailyHistoryRange(a, start, now)
		if berr != nil {
			return berr
		}
		return s.saveDailyPrices("BINANCE", batch)
	}
	// Non-crypto: use TCMB/Stooq for known assets; Yahoo for others
	if a == "USDTRY" || a == "EURTRY" {
		if start.After(now) {
			return nil
		}
		return s.syncFXFromTCMB(a, start, now)
	}
	if a == "GOLD" {
		if start.After(now) {
			return nil
		}
		return s.syncGoldFromStooqUsingTCMB(start, now)
	}
	if a == "BIST100" {
		if start.After(now) {
			return nil
		}
		return s.syncBISTFromStooq(start, now)
	}

	return nil
}

// getHistoricalPrice gets historical price for an asset on a specific date
func (s *service) getHistoricalPrice(asset string, date time.Time) (float64, error) {
	// 1) Prefer stored normalized daily price (exact or nearest prior date)
	if price, ok, err := s.repository.GetDailyPriceByDate(asset, date); err == nil && ok {
		return price, nil
	} else if err != nil {
		return 0, err
	}

	// 2) Try cached price from previous simulations (legacy fallback)
	if cached, ok, err := s.repository.GetCachedHistoricalPrice(asset, date); err == nil && ok {
		return cached, nil
	} else if err != nil {
		// Non-not-found error from DB; log by returning error up the stack
		return 0, err
	}

	// 3) For known non-crypto assets, avoid Yahoo and use internal sources
	upper := strings.ToUpper(strings.TrimSpace(asset))
	switch upper {
	case "USDTRY", "EURTRY":
		// fill just the target day via TCMB, then read from DB
		if err := s.syncFXFromTCMB(upper, date, date); err == nil {
			if price, ok, err2 := s.repository.GetDailyPriceByDate(upper, date); err2 == nil && ok {
				return price, nil
			}
		}
		return 0, fmt.Errorf("no %s price for %s", upper, date.Format("2006-01-02"))
	case "GOLD":
		// ensure USDTRY exists for that day, then compute GOLD via Stooq+TCMB
		_ = s.syncFXFromTCMB("USDTRY", date, date)
		if err := s.syncGoldFromStooqUsingTCMB(date, date); err == nil {
			if price, ok, err2 := s.repository.GetDailyPriceByDate("GOLD", date); err2 == nil && ok {
				return price, nil
			}
		}
		return 0, fmt.Errorf("no GOLD price for %s", date.Format("2006-01-02"))
	case "BIST100":
		if err := s.syncBISTFromStooq(date, date); err == nil {
			if price, ok, err2 := s.repository.GetDailyPriceByDate("BIST100", date); err2 == nil && ok {
				return price, nil
			}
		}
		return 0, fmt.Errorf("no BIST100 price for %s", date.Format("2006-01-02"))
	}

	// 4) Others: crypto → CoinGecko; non-crypto → Yahoo (kept for ETFs like SPY/QQQ)
	if s.isCryptoAsset(asset) {
		return s.getCryptoHistoricalPrice(asset, date)
	}
	return s.getYahooHistoricalPrice(asset, date)
}

// getCurrentPrice gets current price for an asset
func (s *service) getCurrentPrice(asset string) (float64, error) {
	// 0) Try today's daily price from DB (exact day)
	today := time.Now().UTC()
	if p, ok, err := s.repository.GetDailyPriceExact(asset, today); err == nil && ok {
		return p, nil
	} else if err != nil {
		return 0, err
	}

	// 1) Try cached current price from previous simulations
	if cached, ok, err := s.repository.GetCachedCurrentPrice(asset); err == nil && ok {
		return cached, nil
	} else if err != nil {
		return 0, err
	}

	// 2) Attempt incremental sync (same logic as background sync)
	if err := s.syncAssetIncremental(strings.ToUpper(asset)); err == nil {
		if p, ok, err2 := s.repository.GetDailyPriceExact(asset, today); err2 == nil && ok {
			return p, nil
		}
		// Ignore err2 here; will fallback to live fetch below
	}

	// 3) Fallback: for FX and GOLD use latest stored price; others use live sources
	upper := strings.ToUpper(asset)
	switch upper {
	case "USDTRY", "EURTRY", "GOLD":
		if p, ok, err := s.repository.GetDailyPriceByDate(asset, today); err == nil && ok {
			return p, nil
		} else if err != nil {
			return 0, err
		}
		return 0, fmt.Errorf("no stored price for %s", upper)
	default:
		if s.isCryptoAsset(asset) {
			return s.getCryptoCurrentPrice(asset)
		}
		return s.getYahooCurrentPrice(asset)
	}
}

// isCryptoAsset checks if the asset is a cryptocurrency
func (s *service) isCryptoAsset(asset string) bool {
	cryptoAssets := []string{"BTC", "ETH", "ADA", "DOT", "LINK", "LTC", "XRP", "BCH", "BNB", "DOGE"}
	for _, crypto := range cryptoAssets {
		if strings.ToUpper(asset) == crypto {
			return true
		}
	}
	return false
}

// getCryptoHistoricalPrice gets historical crypto price from CoinGecko
func (s *service) getCryptoHistoricalPrice(asset string, date time.Time) (float64, error) {
	// Map asset symbols to CoinGecko IDs
	assetMap := map[string]string{
		"BTC":  "bitcoin",
		"ETH":  "ethereum",
		"ADA":  "cardano",
		"DOT":  "polkadot",
		"LINK": "chainlink",
		"LTC":  "litecoin",
		"XRP":  "ripple",
		"BCH":  "bitcoin-cash",
		"BNB":  "binancecoin",
		"DOGE": "dogecoin",
	}

	coinID, exists := assetMap[strings.ToUpper(asset)]
	if !exists {
		return 0, fmt.Errorf("unsupported crypto asset: %s", asset)
	}

	// Format date for CoinGecko API (DD-MM-YYYY)
	dateStr := date.Format("02-01-2006")

	// CoinGecko API endpoint for historical data
	url := fmt.Sprintf("https://api.coingecko.com/api/v3/coins/%s/history?date=%s", coinID, dateStr)

	resp, err := httpClient.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch data from CoinGecko: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("CoinGecko API returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response body: %w", err)
	}

	var historyResp dtos.CoinGeckoHistoryResponse
	if err := json.Unmarshal(body, &historyResp); err != nil {
		return 0, fmt.Errorf("failed to parse CoinGecko response: %w", err)
	}

	// Get USD price
	price, exists := historyResp.MarketData.CurrentPrice["usd"]
	if !exists {
		return 0, fmt.Errorf("USD price not found in CoinGecko response")
	}

	return price, nil
}

// getCryptoCurrentPrice gets current crypto price from CoinGecko
func (s *service) getCryptoCurrentPrice(asset string) (float64, error) {
	// Map asset symbols to CoinGecko IDs
	assetMap := map[string]string{
		"BTC":  "bitcoin",
		"ETH":  "ethereum",
		"ADA":  "cardano",
		"DOT":  "polkadot",
		"LINK": "chainlink",
		"LTC":  "litecoin",
		"XRP":  "ripple",
		"BCH":  "bitcoin-cash",
		"BNB":  "binancecoin",
		"DOGE": "dogecoin",
	}

	coinID, exists := assetMap[strings.ToUpper(asset)]
	if !exists {
		return 0, fmt.Errorf("unsupported crypto asset: %s", asset)
	}

	// CoinGecko API endpoint for current price
	url := fmt.Sprintf("https://api.coingecko.com/api/v3/simple/price?ids=%s&vs_currencies=usd", coinID)

	resp, err := httpClient.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch data from CoinGecko: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("CoinGecko API returned status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response body: %w", err)
	}

	var priceResp dtos.CoinGeckoPriceResponse
	if err := json.Unmarshal(body, &priceResp); err != nil {
		return 0, fmt.Errorf("failed to parse CoinGecko response: %w", err)
	}

	// Get USD price
	coinData, exists := priceResp[coinID]
	if !exists {
		return 0, fmt.Errorf("coin data not found in CoinGecko response")
	}

	price, exists := coinData["usd"]
	if !exists {
		return 0, fmt.Errorf("USD price not found in CoinGecko response")
	}

	return price, nil
}

// getYahooHistoricalPrice gets historical price from Yahoo Finance
func (s *service) getYahooHistoricalPrice(asset string, date time.Time) (float64, error) {
	// GOLD: derive TRY/gram from XAUUSD and USDTRY for the specific date
	if strings.ToUpper(asset) == "GOLD" {
		const gramsPerOunce = 31.1034768
		pXAU, _, err1 := s.fetchYahooPriceForDate("XAUUSD=X", date)
		pUSDTRY, _, err2 := s.fetchYahooPriceForDate("USDTRY=X", date)
		if err1 == nil && err2 == nil && pXAU > 0 && pUSDTRY > 0 {
			return (pXAU * pUSDTRY) / gramsPerOunce, nil
		}
		// Fallback to direct TRY symbol if available
		sym := s.yahooSymbolForAsset("GOLD")
		if price, _, err := s.fetchYahooPriceForDate(sym, date); err == nil && price > 0 {
			return price, nil
		}
		return 0, fmt.Errorf("no gold price for %s", date.Format("2006-01-02"))
	}

	// Others: use resolved Yahoo symbol
	sym := s.yahooSymbolForAsset(asset)
	price, _, err := s.fetchYahooPriceForDate(sym, date)
	if err != nil || price <= 0 {
		return 0, fmt.Errorf("no price for %s on %s: %v", asset, date.Format("2006-01-02"), err)
	}
	return price, nil
}

// getYahooCurrentPrice gets current price from Yahoo Finance
func (s *service) getYahooCurrentPrice(asset string) (float64, error) {
	if strings.ToUpper(asset) == "GOLD" {
		// Use today's price derived from XAUUSD and USDTRY
		today := time.Now().UTC()
		p, err := s.getYahooHistoricalPrice("GOLD", today)
		if err == nil && p > 0 {
			return p, nil
		}
		// final fallback: direct TRY symbol
		sym := s.yahooSymbolForAsset("GOLD")
		if v, _, err2 := s.fetchYahooPriceForDate(sym, today); err2 == nil && v > 0 {
			return v, nil
		}
		return 0, fmt.Errorf("no current gold price")
	}
	// General case: query chart meta and last close for resolved symbol
	symbol := s.yahooSymbolForAsset(asset)
	url := fmt.Sprintf("https://query1.finance.yahoo.com/v8/finance/chart/%s", symbol)
	resp, err := httpClient.Get(url)
	if err != nil {
		return 0, fmt.Errorf("failed to fetch data from Yahoo Finance: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("Yahoo Finance API returned status: %d", resp.StatusCode)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("failed to read response body: %w", err)
	}
	var yahooResp dtos.YahooFinanceResponse
	if err := json.Unmarshal(body, &yahooResp); err != nil {
		return 0, fmt.Errorf("failed to parse Yahoo Finance response: %w", err)
	}
	if len(yahooResp.Chart.Result) == 0 {
		return 0, fmt.Errorf("no data found for asset %s", asset)
	}
	result := yahooResp.Chart.Result[0]
	if result.Meta.RegularMarketPrice > 0 {
		return result.Meta.RegularMarketPrice, nil
	}
	if len(result.Indicators.Quote) > 0 && len(result.Indicators.Quote[0].Close) > 0 {
		prices := result.Indicators.Quote[0].Close
		for i := len(prices) - 1; i >= 0; i-- {
			if prices[i] > 0 {
				return prices[i], nil
			}
		}
	}
	return 0, fmt.Errorf("no current price data found for asset %s", asset)
}

// yahooSymbolForAsset resolves app asset to Yahoo symbol
func (s *service) yahooSymbolForAsset(asset string) string {
	assetMap := map[string]string{
		// Prefer TRY-based where possible for local relevance
		"GOLD":    "XAUTRY=X", // Gold in TRY (fallbacks handled elsewhere)
		"XAUUSD":  "XAUUSD=X",
		"USDTRY":  "USDTRY=X", // USD/TRY exchange rate
		"USD":     "USDTRY=X",
		"EURTRY":  "EURTRY=X",
		"EUR":     "EURTRY=X",
		"BIST100": "XU100.IS", // BIST 100 index
		"SPY":     "SPY",      // S&P 500 ETF
		"QQQ":     "QQQ",      // NASDAQ ETF
	}

	symbol, exists := assetMap[strings.ToUpper(asset)]
	if !exists {
		symbol = strings.ToUpper(asset)
	}
	return symbol
}

// ---------------------- TCMB FX backfill (USDTRY, EURTRY) ----------------------

type tcmbCurrency struct {
	Kod          string `xml:"Kod,attr"`
	ForexBuying  string `xml:"ForexBuying"`
	ForexSelling string `xml:"ForexSelling"`
}
type tcmbTarihDate struct {
	Currency []tcmbCurrency `xml:"Currency"`
}

func (s *service) backfillTCMBFX() error {
	// For each symbol (USDTRY, EURTRY): from last stored+1 or baseline to today, day by day
	type fxDef struct{ code, asset string }
	defs := []fxDef{{code: "USD", asset: "USDTRY"}, {code: "EUR", asset: "EURTRY"}}
	today := time.Now().UTC()

	for _, d := range defs {
		last, ok, err := s.repository.GetLatestDailyPriceDate(d.asset)
		if err != nil {
			return err
		}
		start := time.Date(2002, 1, 2, 0, 0, 0, 0, time.UTC)
		if ok {
			start = last.AddDate(0, 0, 1)
		}
		if !start.Before(today) {
			continue
		}
		//	log.Printf("[price-sync] TCMB %s from %s", d.asset, start.Format("2006-01-02"))
		var batch []dtos.AssetPriceData
		day := start
		lastRate := 0.0
		for !day.After(today) {
			rate, err := s.fetchTCMBRate(day, d.code)
			if err == nil && rate > 0 {
				if priceSyncVerbose {
					log.Printf("[tcmb] %s %s=%.6f", day.Format("2006-01-02"), d.asset, rate)
				}
				batch = append(batch, dtos.AssetPriceData{Asset: d.asset, Price: rate, Close: rate, Date: day, Currency: "TRY"})
				lastRate = rate
			} else if err != nil {
				log.Printf("[tcmb] %s %s fetch error: %v", day.Format("2006-01-02"), d.asset, err)
			} else if priceSyncFillWeekends && lastRate > 0 {
				if priceSyncVerbose {
					log.Printf("[tcmb] %s %s fill-forward=%.6f", day.Format("2006-01-02"), d.asset, lastRate)
				}
				batch = append(batch, dtos.AssetPriceData{Asset: d.asset, Price: lastRate, Close: lastRate, Date: day, Currency: "TRY"})
			}
			if len(batch) >= 100 {
				if err := s.saveDailyPrices("TCMB", batch); err != nil {
					log.Printf("[tcmb] batch save error (%s): %v", d.asset, err)
				} else {
					log.Printf("[tcmb] batch saved %d rows for %s (through %s)", len(batch), d.asset, day.Format("2006-01-02"))
				}
				batch = batch[:0]
			}
			time.Sleep(120 * time.Millisecond)
			day = day.AddDate(0, 0, 1)
		}
		if len(batch) > 0 {
			if err := s.saveDailyPrices("TCMB", batch); err != nil {
				log.Printf("[tcmb] final batch save error (%s): %v", d.asset, err)
			} else {
				log.Printf("[tcmb] final batch saved %d rows for %s", len(batch), d.asset)
			}
		}
	}
	return nil
}

func (s *service) fetchTCMBRate(date time.Time, code string) (float64, error) {
	// Skip weekends quietly to reduce 404s; caller may fill-forward
	wd := date.Weekday()
	if wd == time.Saturday || wd == time.Sunday {
		if priceSyncVerbose {
			log.Printf("[tcmb] weekend %s", date.Format("2006-01-02"))
		}
		return 0, nil
	}
	y := date.Year()
	m := int(date.Month())
	d := date.Day()
	urlStr := fmt.Sprintf("https://www.tcmb.gov.tr/kurlar/%04d%02d/%02d%02d%04d.xml", y, m, d, m, y)
	if priceSyncVerbose {
		log.Printf("[tcmb] request url=%s code=%s", urlStr, code)
	}
	resp, err := httpClient.Get(urlStr)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("tcmb status %d", resp.StatusCode)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}
	// Use XML decoder with charset support (handles ISO-8859-9, etc.)
	dec := xml.NewDecoder(bytes.NewReader(body))
	dec.CharsetReader = func(cs string, r io.Reader) (io.Reader, error) {
		return xcharset.NewReaderLabel(cs, r)
	}
	var doc tcmbTarihDate
	if err := dec.Decode(&doc); err != nil {
		return 0, err
	}
	for _, c := range doc.Currency {
		if strings.EqualFold(c.Kod, code) {
			v := c.ForexSelling
			if strings.TrimSpace(v) == "" {
				v = c.ForexBuying
			}
			v = strings.ReplaceAll(v, ",", ".")
			f, err := strconv.ParseFloat(strings.TrimSpace(v), 64)
			if err != nil {
				return 0, err
			}
			if priceSyncVerbose {
				log.Printf("[tcmb] parsed %s %s=%s -> %.6f", date.Format("2006-01-02"), code, v, f)
			}
			return f, nil
		}
	}
	return 0, fmt.Errorf("code %s not found", code)
}

// syncFXFromTCMB incrementally fills USDTRY/EURTRY daily prices between start..end using TCMB
func (s *service) syncFXFromTCMB(asset string, start, end time.Time) error {
	a := strings.ToUpper(strings.TrimSpace(asset))
	var code string
	switch a {
	case "USDTRY":
		code = "USD"
	case "EURTRY":
		code = "EUR"
	default:
		return fmt.Errorf("unsupported TCMB FX asset: %s", a)
	}
	if end.Before(start) {
		return nil
	}
	var batch []dtos.AssetPriceData
	day := time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, time.UTC)
	lastRate := 0.0
	for !day.After(end) {
		rate, err := s.fetchTCMBRate(day, code)
		if err == nil && rate > 0 {
			if priceSyncVerbose {
				log.Printf("[tcmb-sync] %s %s=%.6f", day.Format("2006-01-02"), a, rate)
			}
			batch = append(batch, dtos.AssetPriceData{Asset: a, Price: rate, Close: rate, Date: day, Currency: "TRY"})
			lastRate = rate
		} else if priceSyncFillWeekends && lastRate > 0 {
			if priceSyncVerbose {
				log.Printf("[tcmb-sync] %s %s fill-forward=%.6f", day.Format("2006-01-02"), a, lastRate)
			}
			batch = append(batch, dtos.AssetPriceData{Asset: a, Price: lastRate, Close: lastRate, Date: day, Currency: "TRY"})
		}
		if len(batch) >= 100 {
			if err := s.saveDailyPrices("TCMB", batch); err != nil {
				log.Printf("[tcmb-sync] batch save error (%s): %v", a, err)
			}
			batch = batch[:0]
		}
		time.Sleep(120 * time.Millisecond)
		day = day.AddDate(0, 0, 1)
	}
	if len(batch) > 0 {
		if err := s.saveDailyPrices("TCMB", batch); err != nil {
			return err
		}
	}
	return nil
}

// syncGoldFromStooqUsingTCMB fills GOLD daily prices for range start..end using Stooq XAUUSD and TCMB USDTRY
func (s *service) syncGoldFromStooqUsingTCMB(start, end time.Time) error {
	if end.Before(start) {
		return nil
	}
	// Load USDTRY from DB for alignment
	usdtryItems, err := s.repository.GetAllDailyPrices("USDTRY")
	if err != nil {
		return err
	}
	usdtryMap := make(map[string]float64, len(usdtryItems))
	for _, p := range usdtryItems {
		k := p.Date.UTC().Format("2006-01-02")
		if p.Price > 0 {
			usdtryMap[k] = p.Price
		}
	}
	stooq, err := s.fetchStooqCSV("xauusd")
	if err != nil {
		return err
	}
	const gramsPerOunce = 31.1034768
	var batch []dtos.AssetPriceData
	for _, rec := range stooq {
		if rec.t.Before(start) || rec.t.After(end) {
			continue
		}
		k := rec.t.UTC().Format("2006-01-02")
		usd, ok := usdtryMap[k]
		if !ok || rec.close <= 0 || usd <= 0 {
			continue
		}
		priceTRYPerGram := (rec.close * usd) / gramsPerOunce
		batch = append(batch, dtos.AssetPriceData{Asset: "GOLD", Price: priceTRYPerGram, Close: priceTRYPerGram, Date: rec.t, Currency: "TRY"})
		if len(batch) >= 200 {
			if err := s.saveDailyPrices("STOOQ", batch); err != nil {
				log.Printf("[gold-sync] batch save error: %v", err)
			}
			batch = batch[:0]
		}
	}
	if len(batch) > 0 {
		if err := s.saveDailyPrices("STOOQ", batch); err != nil {
			return err
		}
	}
	return nil
}

// syncBISTFromStooq fills BIST100 (XU100) daily prices using Stooq CSV (^xu100)
func (s *service) syncBISTFromStooq(start, end time.Time) error {
	if end.Before(start) {
		return nil
	}
	rows, err := s.fetchStooqCSV("^xu100")
	if err != nil {
		return err
	}
	var batch []dtos.AssetPriceData
	for _, rec := range rows {
		if rec.t.Before(start) || rec.t.After(end) {
			continue
		}
		if rec.close <= 0 {
			continue
		}
		batch = append(batch, dtos.AssetPriceData{Asset: "BIST100", Price: rec.close, Close: rec.close, Date: rec.t, Currency: "TRY"})
		if len(batch) >= 500 {
			if err := s.saveDailyPrices("STOOQ", batch); err != nil {
				return err
			}
			batch = batch[:0]
		}
	}
	if len(batch) > 0 {
		if err := s.saveDailyPrices("STOOQ", batch); err != nil {
			return err
		}
	}
	return nil
}

// ---------------------- Gold from Stooq (XAUUSD) + TCMB USDTRY ----------------------

func (s *service) backfillGoldFromStooqUsingTCMB() error {
	// Determine date range for GOLD
	today := time.Now().UTC()
	last, ok, err := s.repository.GetLatestDailyPriceDate("GOLD")
	if err != nil {
		return err
	}
	start := time.Date(2002, 1, 2, 0, 0, 0, 0, time.UTC)
	if ok {
		start = last.AddDate(0, 0, 1)
	}
	if !start.Before(today) {
		return nil
	}
	// Load USDTRY from DB for alignment
	usdtryItems, err := s.repository.GetAllDailyPrices("USDTRY")
	if err != nil {
		return err
	}
	usdtryMap := make(map[string]float64, len(usdtryItems))
	for _, p := range usdtryItems {
		k := p.Date.UTC().Format("2006-01-02")
		if p.Price > 0 {
			usdtryMap[k] = p.Price
		}
	}
	log.Printf("[gold] loaded USDTRY from DB: %d days", len(usdtryMap))
	// Fetch Stooq XAUUSD CSV and compute
	stooq, err := s.fetchStooqCSV("xauusd")
	if err != nil {
		return err
	}
	log.Printf("[gold] fetched XAUUSD rows: %d", len(stooq))
	const gramsPerOunce = 31.1034768
	var batch []dtos.AssetPriceData
	for _, rec := range stooq {
		if rec.t.Before(start) || rec.t.After(today) {
			continue
		}
		k := rec.t.UTC().Format("2006-01-02")
		usd, ok := usdtryMap[k]
		if !ok || rec.close <= 0 || usd <= 0 {
			if priceSyncVerbose {
				log.Printf("[gold] skip %s xauusd=%.4f usdtry=%.4f ok=%v", k, rec.close, usd, ok)
			}
			continue
		}
		priceTRYPerGram := (rec.close * usd) / gramsPerOunce
		if priceSyncVerbose {
			log.Printf("[gold] %s xauusd=%.4f usdtry=%.4f try/g=%.4f", k, rec.close, usd, priceTRYPerGram)
		}
		batch = append(batch, dtos.AssetPriceData{Asset: "GOLD", Price: priceTRYPerGram, Close: priceTRYPerGram, Date: rec.t, Currency: "TRY"})
		if len(batch) >= 200 {
			if err := s.saveDailyPrices("STOOQ", batch); err != nil {
				log.Printf("[gold] batch save error: %v", err)
			} else {
				log.Printf("[gold] batch saved: %d rows (through %s)", len(batch), rec.t.Format("2006-01-02"))
			}
			batch = batch[:0]
		}
	}
	if len(batch) > 0 {
		if err := s.saveDailyPrices("STOOQ", batch); err != nil {
			log.Printf("[gold] final batch save error: %v", err)
		} else {
			log.Printf("[gold] final batch saved: %d rows", len(batch))
		}
	}
	return nil
}

type stooqRow struct {
	t     time.Time
	close float64
}

func (s *service) fetchStooqCSV(symbol string) ([]stooqRow, error) {
	urlStr := fmt.Sprintf("https://stooq.com/q/d/l/?s=%s&i=d", url.PathEscape(symbol))
	if priceSyncVerbose {
		log.Printf("[stooq] request %s", urlStr)
	}
	resp, err := httpClient.Get(urlStr)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("stooq status %d: %s", resp.StatusCode, strings.TrimSpace(string(b)))
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if priceSyncVerbose {
		log.Printf("[stooq] response bytes=%d", len(body))
	}
	// CSV: Date,Open,High,Low,Close,Volume
	lines := strings.Split(string(body), "\n")
	if len(lines) > 0 && strings.HasPrefix(strings.ToLower(lines[0]), "date,") {
		lines = lines[1:]
	}
	var out []stooqRow
	for _, ln := range lines {
		ln = strings.TrimSpace(ln)
		if ln == "" {
			continue
		}
		parts := strings.Split(ln, ",")
		if len(parts) < 5 {
			continue
		}
		dt := parts[0]
		cl := parts[4]
		t, err := time.Parse("2006-01-02", dt)
		if err != nil {
			continue
		}
		v, err := strconv.ParseFloat(cl, 64)
		if err != nil || v <= 0 {
			continue
		}
		out = append(out, stooqRow{t: t, close: v})
	}
	return out, nil
}

// getYahooFirstTradeDate returns earliest available date from Yahoo meta
func (s *service) getYahooFirstTradeDate(asset string) (time.Time, error) {
	symbol := s.yahooSymbolForAsset(asset)
	// A lightweight call that includes meta
	u := fmt.Sprintf("https://query1.finance.yahoo.com/v8/finance/chart/%s?interval=1d&range=1d", url.PathEscape(symbol))
	resp, err := httpClient.Get(u)
	if err != nil {
		return time.Time{}, fmt.Errorf("yahoo meta fetch failed: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return time.Time{}, fmt.Errorf("yahoo meta status: %d", resp.StatusCode)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to read yahoo meta: %w", err)
	}
	var y dtos.YahooFinanceResponse
	if err := json.Unmarshal(body, &y); err != nil {
		return time.Time{}, fmt.Errorf("failed to parse yahoo meta: %w", err)
	}
	if len(y.Chart.Result) == 0 {
		return time.Time{}, fmt.Errorf("no yahoo meta result for %s", asset)
	}
	ts := y.Chart.Result[0].Meta.FirstTradeDate
	if ts <= 0 {
		// fallback baseline
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC), nil
	}
	return time.Unix(ts, 0).UTC(), nil
}

// fetchYahooPriceForDate queries a single day window; retries on 429
func (s *service) fetchYahooPriceForDate(symbol string, date time.Time) (float64, string, error) {
	start := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC).Unix()
	end := time.Date(date.Year(), date.Month(), date.Day()+1, 0, 0, 0, 0, time.UTC).Unix()
	u := fmt.Sprintf("https://query1.finance.yahoo.com/v8/finance/chart/%s?period1=%d&period2=%d&interval=1d", url.PathEscape(symbol), start, end)

	var lastErr error
	backoff := 400 * time.Millisecond
	for attempt := 0; attempt < 5; attempt++ {
		resp, err := httpClient.Get(u)
		if err != nil {
			lastErr = err
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		body, _ := io.ReadAll(resp.Body)
		status := resp.StatusCode
		resp.Body.Close()
		if status == http.StatusTooManyRequests {
			lastErr = fmt.Errorf("yahoo 429")
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		if status != http.StatusOK {
			lastErr = fmt.Errorf("yahoo status %d", status)
			break
		}
		var y dtos.YahooFinanceResponse
		if err := json.Unmarshal(body, &y); err != nil {
			lastErr = err
			break
		}
		if len(y.Chart.Result) == 0 {
			return 0, "", fmt.Errorf("no data")
		}
		r := y.Chart.Result[0]
		currency := r.Meta.Currency
		if len(r.Indicators.Quote) == 0 || len(r.Indicators.Quote[0].Close) == 0 {
			return 0, currency, fmt.Errorf("no close")
		}
		p := r.Indicators.Quote[0].Close[0]
		if p <= 0 {
			return 0, currency, fmt.Errorf("invalid price")
		}
		return p, currency, nil
	}
	if lastErr == nil {
		lastErr = fmt.Errorf("unknown yahoo error")
	}
	return 0, "", lastErr
}

// fetchYahooDailyRange retrieves day-by-day close prices within [start, end]
func (s *service) fetchYahooDailyRange(asset string, start, end time.Time) ([]dtos.AssetPriceData, error) {
	// Special handling for GOLD: derive gram-TRY from XAUUSD and USDTRY if needed
	if strings.ToUpper(asset) == "GOLD" {
		return s.getGoldTRYFromXAUUSD(start, end)
	}
	symbol := s.yahooSymbolForAsset(asset)
	// normalize dates
	sDay := time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, time.UTC)
	eDay := time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, time.UTC)
	if eDay.Before(sDay) {
		return nil, nil
	}
	var out []dtos.AssetPriceData
	day := sDay
	for !day.After(eDay) {
		o, h, l, c, vol, curr, err := s.fetchYahooOHLCForDate(symbol, day)
		if err == nil && c > 0 {
			out = append(out, dtos.AssetPriceData{
				Asset:    strings.ToUpper(asset),
				Price:    c,
				Open:     o,
				High:     h,
				Low:      l,
				Close:    c,
				Volume:   vol,
				Date:     day,
				Currency: curr,
			})
		}
		// tiny delay to reduce rate limit risk (tunable)
		time.Sleep(800 * time.Millisecond)
		day = day.AddDate(0, 0, 1)
	}
	return out, nil
}

// getGoldTRYFromXAUUSD builds daily GOLD prices in TRY per gram using XAUUSD (USD/oz) and USDTRY
func (s *service) getGoldTRYFromXAUUSD(start, end time.Time) ([]dtos.AssetPriceData, error) {
	// Fetch both series in one call each to minimize rate limits
	ts1, xauusd, _, err := s.getYahooCloses("XAUUSD=X", start, end)
	if err != nil || len(ts1) == 0 {
		return nil, fmt.Errorf("failed to fetch XAUUSD: %w", err)
	}
	ts2, usdtry, _, err := s.getYahooCloses("USDTRY=X", start, end)
	if err != nil || len(ts2) == 0 {
		return nil, fmt.Errorf("failed to fetch USDTRY: %w", err)
	}

	// Map date->value for alignment
	m1 := make(map[string]float64, len(ts1))
	for i, t := range ts1 {
		if i < len(xauusd) && xauusd[i] > 0 {
			d := time.Unix(t, 0).UTC()
			key := d.Format("2006-01-02")
			m1[key] = xauusd[i]
		}
	}
	m2 := make(map[string]float64, len(ts2))
	for i, t := range ts2 {
		if i < len(usdtry) && usdtry[i] > 0 {
			d := time.Unix(t, 0).UTC()
			key := d.Format("2006-01-02")
			m2[key] = usdtry[i]
		}
	}

	// Iterate days between start..end and compute when both available
	sDay := time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, time.UTC)
	eDay := time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, time.UTC)
	if eDay.Before(sDay) {
		return nil, nil
	}
	const gramsPerOunce = 31.1034768
	var out []dtos.AssetPriceData
	day := sDay
	for !day.After(eDay) {
		key := day.Format("2006-01-02")
		pOunceUSD, ok1 := m1[key]
		usdTry, ok2 := m2[key]
		if ok1 && ok2 && pOunceUSD > 0 && usdTry > 0 {
			priceTRYPerGram := (pOunceUSD * usdTry) / gramsPerOunce
			out = append(out, dtos.AssetPriceData{
				Asset:    "GOLD",
				Price:    priceTRYPerGram,
				Open:     0,
				High:     0,
				Low:      0,
				Close:    priceTRYPerGram,
				Volume:   0,
				Date:     day,
				Currency: "TRY",
			})
		}
		day = day.AddDate(0, 0, 1)
	}
	return out, nil
}

// getYahooCloses fetches daily close series for a symbol between start and end
func (s *service) getYahooCloses(symbol string, start, end time.Time) ([]int64, []float64, string, error) {
	sDay := time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, time.UTC)
	eDay := time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, time.UTC)
	period1 := sDay.Unix()
	period2 := eDay.AddDate(0, 0, 1).Unix()
	u := fmt.Sprintf("https://query1.finance.yahoo.com/v8/finance/chart/%s?period1=%d&period2=%d&interval=1d", url.PathEscape(symbol), period1, period2)
	var lastErr error
	backoff := 400 * time.Millisecond
	for attempt := 0; attempt < 5; attempt++ {
		resp, err := httpClient.Get(u)
		if err != nil {
			lastErr = err
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		body, _ := io.ReadAll(resp.Body)
		status := resp.StatusCode
		resp.Body.Close()
		if status == http.StatusTooManyRequests {
			lastErr = fmt.Errorf("yahoo 429")
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		if status != http.StatusOK {
			lastErr = fmt.Errorf("yahoo status %d", status)
			break
		}
		var y dtos.YahooFinanceResponse
		if err := json.Unmarshal(body, &y); err != nil {
			lastErr = err
			break
		}
		if len(y.Chart.Result) == 0 {
			return nil, nil, "", fmt.Errorf("no data")
		}
		r := y.Chart.Result[0]
		if len(r.Indicators.Quote) == 0 || len(r.Indicators.Quote[0].Close) == 0 {
			return nil, nil, r.Meta.Currency, fmt.Errorf("no close")
		}
		return r.Timestamp, r.Indicators.Quote[0].Close, r.Meta.Currency, nil
	}
	if lastErr == nil {
		lastErr = fmt.Errorf("unknown yahoo error")
	}
	return nil, nil, "", lastErr
}

// fetchYahooOHLCForDate returns OHLC(+volume) for a specific date via Yahoo
func (s *service) fetchYahooOHLCForDate(symbol string, date time.Time) (float64, float64, float64, float64, float64, string, error) {
	start := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, time.UTC).Unix()
	end := time.Date(date.Year(), date.Month(), date.Day()+1, 0, 0, 0, 0, time.UTC).Unix()
	u := fmt.Sprintf("https://query1.finance.yahoo.com/v8/finance/chart/%s?period1=%d&period2=%d&interval=1d", url.PathEscape(symbol), start, end)
	var lastErr error
	backoff := 400 * time.Millisecond
	for attempt := 0; attempt < 5; attempt++ {
		resp, err := httpClient.Get(u)
		if err != nil {
			lastErr = err
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		body, _ := io.ReadAll(resp.Body)
		status := resp.StatusCode
		resp.Body.Close()
		if status == http.StatusTooManyRequests {
			lastErr = fmt.Errorf("yahoo 429")
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		if status != http.StatusOK {
			lastErr = fmt.Errorf("yahoo status %d", status)
			break
		}
		var y dtos.YahooFinanceResponse
		if err := json.Unmarshal(body, &y); err != nil {
			lastErr = err
			break
		}
		if len(y.Chart.Result) == 0 {
			return 0, 0, 0, 0, 0, "", fmt.Errorf("no data")
		}
		r := y.Chart.Result[0]
		curr := r.Meta.Currency
		if len(r.Indicators.Quote) == 0 || len(r.Indicators.Quote[0].Close) == 0 {
			return 0, 0, 0, 0, 0, curr, fmt.Errorf("no quote")
		}
		q := r.Indicators.Quote[0]
		// Use available values; Yahoo often returns arrays of length 1
		var o, h, l, c, v float64
		if len(q.Open) > 0 {
			o = q.Open[0]
		}
		if len(q.High) > 0 {
			h = q.High[0]
		}
		if len(q.Low) > 0 {
			l = q.Low[0]
		}
		if len(q.Close) > 0 {
			c = q.Close[0]
		}
		if len(q.Volume) > 0 {
			v = float64(q.Volume[0])
		}
		if c <= 0 {
			return 0, 0, 0, 0, 0, curr, fmt.Errorf("invalid close")
		}
		if o <= 0 {
			o = c
		}
		if h <= 0 {
			h = c
		}
		if l <= 0 {
			l = c
		}
		return o, h, l, c, v, curr, nil
	}
	if lastErr == nil {
		lastErr = fmt.Errorf("unknown yahoo error")
	}
	return 0, 0, 0, 0, 0, "", lastErr
}

// (legacy helpers removed; direct values are used since Yahoo arrays are []float64)

// saveDailyPrices persists daily prices to DB via repository upsert
func (s *service) saveDailyPrices(source string, items []dtos.AssetPriceData) error {
	if len(items) == 0 {
		return nil
	}
	batch := make([]entities.DailyPrice, 0, len(items))
	for _, it := range items {
		d := it.Date.UTC()
		d = time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, time.UTC)
		// Ensure OHLC set; fallback to Price when missing
		o := it.Open
		h := it.High
		l := it.Low
		c := it.Close
		if c == 0 {
			c = it.Price
		}
		if o == 0 {
			o = c
		}
		if h == 0 {
			h = c
		}
		if l == 0 {
			l = c
		}
		batch = append(batch, entities.DailyPrice{
			Symbol:   strings.ToUpper(it.Asset),
			Asset:    strings.ToUpper(it.Asset),
			Date:     d,
			Price:    c,
			Open:     o,
			High:     h,
			Low:      l,
			Close:    c,
			Volume:   it.Volume,
			Currency: it.Currency,
			Source:   source,
		})
	}
	return s.repository.UpsertDailyPrices(batch)
}

// getYahooFullDailyHistory fetches full daily close prices from earliest available to now
func (s *service) getYahooFullDailyHistory(asset string) ([]dtos.AssetPriceData, error) {
	fmt.Println("Fetching full history for", asset)
	symbol := s.yahooSymbolForAsset(asset)

	// Use range=max to get earliest available data
	u := fmt.Sprintf("https://query1.finance.yahoo.com/v8/finance/chart/%s?interval=1d&range=max", url.PathEscape(symbol))
	var body []byte
	var err error
	backoff := 500 * time.Millisecond
	for attempt := 0; attempt < 5; attempt++ {
		resp, reqErr := httpClient.Get(u)
		if reqErr != nil {
			err = reqErr
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		b, readErr := io.ReadAll(resp.Body)
		status := resp.StatusCode
		resp.Body.Close()
		if status == http.StatusTooManyRequests {
			// backoff and retry
			err = fmt.Errorf("yahoo 429")
			time.Sleep(backoff)
			backoff *= 2
			continue
		}
		if status != http.StatusOK {
			err = fmt.Errorf("Yahoo Finance API returned status: %d", status)
			break
		}
		if readErr != nil {
			err = readErr
			break
		}
		body = b
		err = nil
		break
	}
	if err != nil {
		return nil, err
	}

	// Define a local struct for series; align types with YahooQuote
	type ySeriesQuote struct {
		Open   []float64 `json:"open"`
		High   []float64 `json:"high"`
		Low    []float64 `json:"low"`
		Close  []float64 `json:"close"`
		Volume []int64   `json:"volume"`
	}
	type ySeriesResult struct {
		Meta struct {
			Currency string `json:"currency"`
		} `json:"meta"`
		Timestamp  []int64 `json:"timestamp"`
		Indicators struct {
			Quote []ySeriesQuote `json:"quote"`
		} `json:"indicators"`
	}
	var yResp struct {
		Chart struct {
			Result []ySeriesResult `json:"result"`
			Error  interface{}     `json:"error"`
		} `json:"chart"`
	}

	if err := json.Unmarshal(body, &yResp); err != nil {
		return nil, fmt.Errorf("failed to parse Yahoo response: %w", err)
	}
	if len(yResp.Chart.Result) == 0 {
		return nil, fmt.Errorf("no Yahoo data for %s", asset)
	}
	r := yResp.Chart.Result[0]
	if len(r.Indicators.Quote) == 0 {
		return nil, fmt.Errorf("no Yahoo quotes for %s", asset)
	}
	quote := r.Indicators.Quote[0]
	closes := quote.Close
	stamps := r.Timestamp
	if len(stamps) == 0 || len(closes) == 0 {
		return nil, fmt.Errorf("empty Yahoo series for %s", asset)
	}

	currency := r.Meta.Currency
	if currency == "" {
		currency = "USD"
	}

	var out []dtos.AssetPriceData
	n := len(stamps)
	// Close array typically matches timestamps length; guard against mismatch
	for i := 0; i < n && i < len(closes); i++ {
		if closes[i] <= 0 {
			continue
		}
		// Pull OHLC where available; otherwise fallback to close
		o := closes[i]
		h := closes[i]
		l := closes[i]
		if i < len(quote.Open) && quote.Open[i] > 0 {
			o = quote.Open[i]
		}
		if i < len(quote.High) && quote.High[i] > 0 {
			h = quote.High[i]
		}
		if i < len(quote.Low) && quote.Low[i] > 0 {
			l = quote.Low[i]
		}
		vol := 0.0
		if i < len(quote.Volume) && quote.Volume[i] > 0 {
			vol = float64(quote.Volume[i])
		}
		out = append(out, dtos.AssetPriceData{
			Asset:    strings.ToUpper(asset),
			Price:    closes[i],
			Open:     o,
			High:     h,
			Low:      l,
			Close:    closes[i],
			Volume:   vol,
			Date:     time.Unix(stamps[i], 0).UTC(),
			Currency: currency,
		})
	}
	return out, nil
}

// getBinanceFullDailyHistory fetches full daily klines from Binance for crypto assets
func (s *service) getBinanceFullDailyHistory(asset string) ([]dtos.AssetPriceData, error) {
	symMap := map[string]string{
		"BTC":  "BTCUSDT",
		"ETH":  "ETHUSDT",
		"ADA":  "ADAUSDT",
		"DOT":  "DOTUSDT",
		"LINK": "LINKUSDT",
		"LTC":  "LTCUSDT",
		"XRP":  "XRPUSDT",
		"BCH":  "BCHUSDT",
		"BNB":  "BNBUSDT",
		"DOGE": "DOGEUSDT",
	}
	pair, ok := symMap[strings.ToUpper(asset)]
	if !ok {
		return nil, fmt.Errorf("unsupported crypto asset for Binance: %s", asset)
	}

	const (
		endpoint  = "https://api.binance.com/api/v3/klines"
		interval  = "1d"
		limit     = 1000
		dayMillis = int64(24 * time.Hour / time.Millisecond)
	)

	var out []dtos.AssetPriceData
	start := int64(0) // from earliest
	nowMs := time.Now().UnixMilli()

	for {
		q := url.Values{}
		q.Set("symbol", pair)
		q.Set("interval", interval)
		q.Set("limit", strconv.Itoa(limit))
		if start > 0 {
			q.Set("startTime", strconv.FormatInt(start, 10))
		} else {
			// Explicit 0 to ensure earliest
			q.Set("startTime", "0")
		}
		// Set endTime to now to avoid future candles
		q.Set("endTime", strconv.FormatInt(nowMs, 10))

		urlStr := endpoint + "?" + q.Encode()
		resp, err := httpClient.Get(urlStr)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch Binance klines: %w", err)
		}
		if resp.StatusCode != http.StatusOK {
			// Read body for error context
			b, _ := io.ReadAll(resp.Body)
			resp.Body.Close()
			return nil, fmt.Errorf("binance status %d: %s", resp.StatusCode, strings.TrimSpace(string(b)))
		}
		b, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			return nil, fmt.Errorf("failed to read Binance response: %w", err)
		}

		var data [][]interface{}
		if err := json.Unmarshal(b, &data); err != nil {
			return nil, fmt.Errorf("failed to parse Binance klines: %w", err)
		}
		if len(data) == 0 {
			break
		}

		// Each item: [ openTime, open, high, low, close, volume, closeTime, ... ]
		for _, item := range data {
			if len(item) < 6 {
				continue
			}
			// openTime
			openTime, ok := item[0].(float64)
			if !ok {
				continue
			}
			// open/high/low/close as strings per Binance API
			openStr, _ := itemAsString(item, 1)
			highStr, _ := itemAsString(item, 2)
			lowStr, _ := itemAsString(item, 3)
			closeStr, ok := item[4].(string)
			if !ok {
				// sometimes number; try fallback
				if num, ok2 := item[4].(float64); ok2 {
					closeStr = fmt.Sprintf("%f", num)
				} else {
					continue
				}
			}
			openVal, _ := strconv.ParseFloat(openStr, 64)
			highVal, _ := strconv.ParseFloat(highStr, 64)
			lowVal, _ := strconv.ParseFloat(lowStr, 64)
			closeVal, err := strconv.ParseFloat(closeStr, 64)
			if err != nil || closeVal <= 0 {
				continue
			}
			vol := 0.0
			if len(item) > 5 {
				if vStr, ok := item[5].(string); ok {
					if v, e := strconv.ParseFloat(vStr, 64); e == nil {
						vol = v
					}
				} else if vNum, ok := item[5].(float64); ok {
					vol = vNum
				}
			}
			out = append(out, dtos.AssetPriceData{
				Asset:    strings.ToUpper(asset),
				Price:    closeVal,
				Open:     openVal,
				High:     highVal,
				Low:      lowVal,
				Close:    closeVal,
				Volume:   vol,
				Date:     time.UnixMilli(int64(openTime)).UTC(),
				Currency: "USDT",
			})
		}

		// Prepare next page start time
		last := data[len(data)-1]
		if len(last) < 1 {
			break
		}
		lastOpen, ok := last[0].(float64)
		if !ok {
			break
		}
		nextStart := int64(lastOpen) + dayMillis
		if nextStart >= nowMs {
			break
		}
		start = nextStart

		// Small delay to be polite; not strictly necessary, keep minimal
		time.Sleep(50 * time.Millisecond)
	}

	return out, nil
}

// getBinanceDailyHistoryRange fetches daily closes between start and end (inclusive)
func (s *service) getBinanceDailyHistoryRange(asset string, start, end time.Time) ([]dtos.AssetPriceData, error) {
	symMap := map[string]string{
		"BTC":  "BTCUSDT",
		"ETH":  "ETHUSDT",
		"ADA":  "ADAUSDT",
		"DOT":  "DOTUSDT",
		"LINK": "LINKUSDT",
		"LTC":  "LTCUSDT",
		"XRP":  "XRPUSDT",
		"BCH":  "BCHUSDT",
		"BNB":  "BNBUSDT",
		"DOGE": "DOGEUSDT",
	}
	pair, ok := symMap[strings.ToUpper(asset)]
	if !ok {
		return nil, fmt.Errorf("unsupported crypto asset for Binance: %s", asset)
	}
	const (
		endpoint = "https://api.binance.com/api/v3/klines"
		interval = "1d"
		limit    = 1000
	)
	startMs := start.UTC().UnixMilli()
	endMs := end.UTC().UnixMilli()
	if endMs <= startMs {
		return nil, nil
	}
	var out []dtos.AssetPriceData
	next := startMs
	for next < endMs {
		q := url.Values{}
		q.Set("symbol", pair)
		q.Set("interval", interval)
		q.Set("limit", strconv.Itoa(limit))
		q.Set("startTime", strconv.FormatInt(next, 10))
		q.Set("endTime", strconv.FormatInt(endMs, 10))
		urlStr := endpoint + "?" + q.Encode()
		resp, err := httpClient.Get(urlStr)
		if err != nil {
			return nil, fmt.Errorf("binance fetch failed: %w", err)
		}
		if resp.StatusCode != http.StatusOK {
			b, _ := io.ReadAll(resp.Body)
			resp.Body.Close()
			return nil, fmt.Errorf("binance status %d: %s", resp.StatusCode, strings.TrimSpace(string(b)))
		}
		b, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			return nil, err
		}
		var data [][]interface{}
		if err := json.Unmarshal(b, &data); err != nil {
			return nil, err
		}
		if len(data) == 0 {
			break
		}
		for _, item := range data {
			if len(item) < 5 {
				continue
			}
			openTime, ok := item[0].(float64)
			if !ok {
				continue
			}
			// open/high/low/close
			openStr, _ := itemAsString(item, 1)
			highStr, _ := itemAsString(item, 2)
			lowStr, _ := itemAsString(item, 3)
			closeStr, _ := itemAsString(item, 4)
			openVal, _ := strconv.ParseFloat(openStr, 64)
			highVal, _ := strconv.ParseFloat(highStr, 64)
			lowVal, _ := strconv.ParseFloat(lowStr, 64)
			closeVal, err := strconv.ParseFloat(closeStr, 64)
			if err != nil || closeVal <= 0 {
				continue
			}
			vol := 0.0
			if len(item) > 5 {
				if vStr, ok := item[5].(string); ok {
					if v, e := strconv.ParseFloat(vStr, 64); e == nil {
						vol = v
					}
				} else if vNum, ok := item[5].(float64); ok {
					vol = vNum
				}
			}
			out = append(out, dtos.AssetPriceData{
				Asset:    strings.ToUpper(asset),
				Price:    closeVal,
				Open:     openVal,
				High:     highVal,
				Low:      lowVal,
				Close:    closeVal,
				Volume:   vol,
				Date:     time.UnixMilli(int64(openTime)).UTC(),
				Currency: "USDT",
			})
		}
		// move window
		last := data[len(data)-1]
		if open, ok := last[0].(float64); ok {
			next = int64(open) + int64(24*time.Hour/time.Millisecond)
		} else {
			break
		}
		time.Sleep(50 * time.Millisecond)
	}
	return out, nil
}
