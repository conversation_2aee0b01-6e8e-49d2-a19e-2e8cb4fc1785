package recurring

import (
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/domains/account"
	"github.com/nocytech/butce360/pkg/domains/category"
	"github.com/nocytech/butce360/pkg/domains/transaction"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
)

type Service interface {
	CreateRecurringTransaction(userID string, req *dtos.RecurringTransactionRequest) (*dtos.RecurringTransactionResponse, error)
	GetRecurringTransactionByID(id string) (*dtos.RecurringTransactionResponse, error)
	UpdateRecurringTransaction(id string, req *dtos.RecurringTransactionRequest) (*dtos.RecurringTransactionResponse, error)
	DeleteRecurringTransaction(id string) error
	GetAllRecurringTransactions(userID string) ([]dtos.RecurringTransactionResponse, error)
	ProcessDueRecurringTransactions() error
}

type service struct {
	repository         Repository
	categoryService    category.Service
	accountService     account.Service
	transactionService transaction.Service // can be nil during initialization
}

func NewService(r Repository, cs category.Service, as account.Service, ts transaction.Service) Service {
	return &service{
		repository:         r,
		categoryService:    cs,
		accountService:     as,
		transactionService: ts,
	}
}

func (s *service) CreateRecurringTransaction(userID string, req *dtos.RecurringTransactionRequest) (*dtos.RecurringTransactionResponse, error) {
	// Parse UUIDs
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Handle optional category ID
	var categoryUUID uuid.UUID
	if req.CategoryID != "" {
		var err error
		categoryUUID, err = uuid.Parse(req.CategoryID)
		if err != nil {
			return nil, errors.New("invalid category ID")
		}
	} else {
		categoryUUID = uuid.Nil
	}

	// Handle optional account ID
	var accountUUID uuid.UUID
	if req.AccountID != "" {
		var err error
		accountUUID, err = uuid.Parse(req.AccountID)
		if err != nil {
			return nil, errors.New("invalid account ID")
		}
	} else {
		accountUUID = uuid.Nil
	}

	// Parse start date - use today if not provided
	var startDate time.Time
	if req.StartDate != "" {
		var err error
		startDate, err = time.Parse("2006-01-02", req.StartDate)
		if err != nil {
			return nil, errors.New("invalid start date format, expected YYYY-MM-DD")
		}
	} else {
		startDate = time.Now()
	}

	// Parse end date if provided
	var endDate *time.Time
	if req.EndDate != nil && *req.EndDate != "" {
		parsedEndDate, err := time.Parse("2006-01-02", *req.EndDate)
		if err != nil {
			return nil, errors.New("invalid end date format, expected YYYY-MM-DD")
		}
		endDate = &parsedEndDate
	}

	// Set default values if not provided
	title := req.Title
	if title == "" {
		title = "Tekrarlı İşlem"
	}

	transactionType := req.Type
	if transactionType == "" {
		transactionType = "expense"
	}

	currency := req.Currency
	if currency == "" {
		currency = "TRY"
	}

	interval := req.Interval
	if interval == "" {
		interval = "monthly"
	}

	paymentMethod := req.PaymentMethod
	if paymentMethod == "" {
		paymentMethod = "cash"
	}

	// Create recurring transaction entity
	recurring := &entities.RecurringTransaction{
		Title:         title,
		Type:          transactionType,
		Amount:        req.Amount,
		Currency:      currency,
		Interval:      interval,
		StartDate:     startDate,
		EndDate:       endDate,
		CategoryID:    categoryUUID,
		PaymentMethod: paymentMethod,
		AccountID:     accountUUID,
		Note:          req.Note,
		UserID:        userUUID,
	}

	// Save recurring transaction
	if err := s.repository.Create(recurring); err != nil {
		return nil, err
	}

	// Create future transactions for one year
	if err := s.createFutureTransactions(recurring); err != nil {
		fmt.Printf("Warning: Failed to create future transactions: %v\n", err)
	}

	// Get recurring transaction with relations
	savedRecurring, err := s.repository.FindByID(recurring.ID)
	if err != nil {
		return nil, err
	}

	return s.toRecurringTransactionResponse(savedRecurring), nil
}

func (s *service) GetRecurringTransactionByID(id string) (*dtos.RecurringTransactionResponse, error) {
	// Parse UUID
	recurringUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid recurring transaction ID")
	}

	// Get recurring transaction
	recurring, err := s.repository.FindByID(recurringUUID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toRecurringTransactionResponse(recurring), nil
}

func (s *service) UpdateRecurringTransaction(id string, req *dtos.RecurringTransactionRequest) (*dtos.RecurringTransactionResponse, error) {
	// Parse UUID
	recurringUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid recurring transaction ID")
	}

	// Get recurring transaction
	recurring, err := s.repository.FindByID(recurringUUID)
	if err != nil {
		return nil, err
	}

	// Update recurring transaction fields
	if req.Title != "" {
		recurring.Title = req.Title
	}
	if req.Type != "" {
		recurring.Type = req.Type
	}
	if req.Amount > 0 {
		recurring.Amount = req.Amount
	}
	if req.Currency != "" {
		recurring.Currency = req.Currency
	}
	if req.Interval != "" {
		recurring.Interval = req.Interval
	}
	if req.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", req.StartDate)
		if err != nil {
			return nil, errors.New("invalid start date format, expected YYYY-MM-DD")
		}
		recurring.StartDate = startDate
	}

	// Parse end date if provided
	if req.EndDate != nil && *req.EndDate != "" {
		parsedEndDate, err := time.Parse("2006-01-02", *req.EndDate)
		if err != nil {
			return nil, errors.New("invalid end date format, expected YYYY-MM-DD")
		}
		recurring.EndDate = &parsedEndDate
	} else if req.EndDate != nil && *req.EndDate == "" {
		recurring.EndDate = nil
	}
	if req.CategoryID != "" {
		categoryUUID, err := uuid.Parse(req.CategoryID)
		if err != nil {
			return nil, errors.New("invalid category ID")
		}
		recurring.CategoryID = categoryUUID
	}
	if req.PaymentMethod != "" {
		recurring.PaymentMethod = req.PaymentMethod
	}
	if req.AccountID != "" {
		accountUUID, err := uuid.Parse(req.AccountID)
		if err != nil {
			return nil, errors.New("invalid account ID")
		}
		recurring.AccountID = accountUUID
	}
	if req.Note != "" {
		recurring.Note = req.Note
	}

	// Save recurring transaction
	if err := s.repository.Update(recurring); err != nil {
		return nil, err
	}

	// Delete existing future transactions
	if s.transactionService != nil {
		s.transactionService.UpdateRecurringTransactions(id, &dtos.TransactionRequest{}, false)
	}

	// Recreate future transactions with new data
	if err := s.createFutureTransactions(recurring); err != nil {
		fmt.Printf("Warning: Failed to recreate future transactions: %v\n", err)
	}

	// Get updated recurring transaction
	updatedRecurring, err := s.repository.FindByID(recurring.ID)
	if err != nil {
		return nil, err
	}

	return s.toRecurringTransactionResponse(updatedRecurring), nil
}

func (s *service) DeleteRecurringTransaction(id string) error {
	// Parse UUID
	recurringUUID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid recurring transaction ID")
	}

	// Delete future transactions first
	if s.transactionService != nil {
		s.transactionService.UpdateRecurringTransactions(id, &dtos.TransactionRequest{}, false)
	}

	// Delete recurring transaction
	return s.repository.Delete(recurringUUID)
}

func (s *service) GetAllRecurringTransactions(userID string) ([]dtos.RecurringTransactionResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Get recurring transactions
	recurrings, err := s.repository.FindAll(userUUID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	var response []dtos.RecurringTransactionResponse
	for _, recurring := range recurrings {
		response = append(response, *s.toRecurringTransactionResponse(&recurring))
	}

	return response, nil
}

func (s *service) ProcessDueRecurringTransactions() error {
	// Get today's date
	today := time.Now()

	// Get due recurring transactions
	dueRecurrings, err := s.repository.FindDueRecurringTransactions(today)
	if err != nil {
		return err
	}

	// Process each due recurring transaction
	for _, recurring := range dueRecurrings {
		// Create transaction from recurring transaction
		transactionReq := &dtos.TransactionRequest{
			Title:           recurring.Title,
			Type:            recurring.Type, // Use the type from recurring transaction
			Amount:          recurring.Amount,
			Currency:        recurring.Currency,
			CategoryID:      recurring.CategoryID.String(),
			PaymentMethod:   recurring.PaymentMethod,
			AccountID:       recurring.AccountID.String(),
			Note:            recurring.Note + " (Recurring)",
			TransactionDate: today,
			Location:        "",
		}

		// Create transaction
		if s.transactionService != nil {
			_, err := s.transactionService.CreateTransaction(recurring.UserID.String(), transactionReq)
			if err != nil {
				// Log error but continue processing other recurring transactions
				continue
			}
		}
	}

	return nil
}

func (s *service) toRecurringTransactionResponse(recurring *entities.RecurringTransaction) *dtos.RecurringTransactionResponse {
	// Get category separately
	category, err := s.categoryService.GetCategoryByID(recurring.CategoryID.String())
	if err != nil {
		// If category not found, use empty category
		category = &dtos.CategoryResponse{
			ID:   recurring.CategoryID.String(),
			Name: "Unknown Category",
			Type: "expense",
			Icon: "help",
		}
	}

	// Get account separately
	account, err := s.accountService.GetAccountByID(recurring.AccountID.String())
	if err != nil {
		// If account not found, use empty account
		account = &dtos.AccountResponse{
			ID:       recurring.AccountID.String(),
			Name:     "Unknown Account",
			Type:     "cash",
			Balance:  0,
			Currency: "TRY",
		}
	}

	return &dtos.RecurringTransactionResponse{
		ID:         recurring.ID.String(),
		Title:      recurring.Title,
		Type:       recurring.Type,
		Amount:     recurring.Amount,
		Currency:   recurring.Currency,
		Interval:   recurring.Interval,
		StartDate:  recurring.StartDate,
		EndDate:    recurring.EndDate,
		CategoryID: recurring.CategoryID.String(),
		Category: dtos.CategoryDTO{
			ID:   category.ID,
			Name: category.Name,
			Type: category.Type,
			Icon: category.Icon,
		},
		PaymentMethod: recurring.PaymentMethod,
		AccountID:     recurring.AccountID.String(),
		Account: dtos.AccountDTO{
			ID:       account.ID,
			Name:     account.Name,
			Type:     account.Type,
			Balance:  account.Balance,
			Currency: account.Currency,
		},
		Note:      recurring.Note,
		CreatedAt: recurring.CreatedAt,
		UpdatedAt: recurring.UpdatedAt,
	}
}

func (s *service) createFutureTransactions(recurring *entities.RecurringTransaction) error {
	now := time.Now()
	endDate := now.AddDate(1, 0, 0) // One year from now

	if recurring.EndDate != nil && recurring.EndDate.Before(endDate) {
		endDate = *recurring.EndDate
	}

	currentDate := recurring.StartDate
	for currentDate.Before(endDate) || currentDate.Equal(endDate) {
		if s.isDue(*recurring, currentDate) && currentDate.After(now) {
			transactionReq := &dtos.TransactionRequest{
				Title:           recurring.Title,
				Type:            recurring.Type,
				Amount:          recurring.Amount,
				Currency:        recurring.Currency,
				CategoryID:      recurring.CategoryID.String(),
				PaymentMethod:   recurring.PaymentMethod,
				AccountID:       recurring.AccountID.String(),
				Note:            recurring.Note + " (Tekrarlı)",
				TransactionDate: currentDate,
			}

			if s.transactionService != nil {
				transaction, err := s.transactionService.CreateTransaction(recurring.UserID.String(), transactionReq)
				if err != nil {
					fmt.Printf("Failed to create future transaction for %v: %v\n", currentDate, err)
				} else {
					// Mark as recurring transaction
					s.markAsRecurringTransaction(transaction.ID, recurring.ID.String())
				}
			}
		}
		currentDate = s.getNextDate(currentDate, recurring.Interval)
	}
	return nil
}

func (s *service) markAsRecurringTransaction(transactionID, recurringID string) {
	// This would need to be implemented in transaction service
	// For now, we'll skip this implementation
}

func (s *service) getNextDate(date time.Time, interval string) time.Time {
	switch interval {
	case "daily":
		return date.AddDate(0, 0, 1)
	case "weekly":
		return date.AddDate(0, 0, 7)
	case "monthly":
		return date.AddDate(0, 1, 0)
	case "yearly":
		return date.AddDate(1, 0, 0)
	default:
		return date.AddDate(0, 0, 1)
	}
}

func (s *service) isDue(recurring entities.RecurringTransaction, date time.Time) bool {
	switch recurring.Interval {
	case "daily":
		return true
	case "weekly":
		return date.Weekday() == recurring.StartDate.Weekday()
	case "monthly":
		return date.Day() == recurring.StartDate.Day()
	case "yearly":
		return date.Month() == recurring.StartDate.Month() && date.Day() == recurring.StartDate.Day()
	default:
		return false
	}
}
