package subscription

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	CreateSubscription(subscription *entities.Subscription) error
	GetSubscriptionByUserID(userID string) (*entities.Subscription, error)
	GetSubscriptionByOriginalTransactionID(originalTransactionID string) (*entities.Subscription, error)
	UpdateSubscription(subscription *entities.Subscription) error
	GetActiveSubscriptionByUserID(userID string) (*entities.Subscription, error)
	GetActiveSubscriptionsByUserID(userID string) ([]*entities.Subscription, error)
	DeactivateExpiredSubscriptions() error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateSubscription(subscription *entities.Subscription) error {
	subscription.ID = uuid.New()
	subscription.CreatedAt = time.Now()
	subscription.UpdatedAt = time.Now()

	return r.db.Create(subscription).Error
}

func (r *repository) GetSubscriptionByUserID(userID string) (*entities.Subscription, error) {
	var subscription entities.Subscription
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		First(&subscription).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &subscription, nil
}

func (r *repository) GetSubscriptionByOriginalTransactionID(originalTransactionID string) (*entities.Subscription, error) {
	var subscription entities.Subscription
	err := r.db.Where("original_transaction_id = ?", originalTransactionID).
		First(&subscription).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &subscription, nil
}

func (r *repository) UpdateSubscription(subscription *entities.Subscription) error {
	subscription.UpdatedAt = time.Now()
	return r.db.Save(subscription).Error
}

func (r *repository) GetActiveSubscriptionByUserID(userID string) (*entities.Subscription, error) {
	var subscription entities.Subscription
	err := r.db.Where("user_id = ? AND is_active = ? AND expires_date > ?",
		userID, true, time.Now()).
		Order("expires_date DESC").
		First(&subscription).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &subscription, nil
}

// GetActiveSubscriptionsByUserID gets all active subscriptions for a user
func (r *repository) GetActiveSubscriptionsByUserID(userID string) ([]*entities.Subscription, error) {
	var subscriptions []*entities.Subscription
	err := r.db.Where("user_id = ? AND is_active = ? AND expires_date > ?", userID, true, time.Now()).
		Find(&subscriptions).Error
	if err != nil {
		return nil, err
	}
	return subscriptions, nil
}

func (r *repository) DeactivateExpiredSubscriptions() error {
	return r.db.Model(&entities.Subscription{}).
		Where("is_active = ? AND expires_date < ?", true, time.Now()).
		Update("is_active", false).Error
}
