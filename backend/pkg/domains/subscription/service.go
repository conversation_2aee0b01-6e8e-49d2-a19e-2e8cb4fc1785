package subscription

import (
	"fmt"
	"time"

	"github.com/nocytech/butce360/pkg/domains/auth"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
)

type Service interface {
	ProcessRevenueCatWebhook(webhook *dtos.RevenueCatWebhookRequest) error
	GetUserSubscriptionStatus(userID string) (*dtos.SubscriptionStatusResponse, error)
	ActivateSubscription(userID, productID, originalTransactionID string, expiresDate time.Time, revenueCatCustomerID string) error
	DeactivateSubscription(userID string) error
	SyncUserPremiumStatus(userID string) error
}

type service struct {
	repository  Repository
	authService auth.Service
}

func NewService(repository Repository, authService auth.Service) Service {
	return &service{
		repository:  repository,
		authService: authService,
	}
}

func (s *service) ProcessRevenueCatWebhook(webhook *dtos.RevenueCatWebhookRequest) error {
	// Find user by RevenueCat customer ID
	user, err := s.authService.GetUserByRevenueCatID(webhook.Event.AppUserID)
	if err != nil {
		return fmt.Errorf("user not found for RevenueCat ID %s: %w", webhook.Event.AppUserID, err)
	}

	switch webhook.Event.Type {
	case "INITIAL_PURCHASE", "RENEWAL":
		return s.handlePurchaseEvent(user, webhook)
	case "CANCELLATION", "EXPIRATION":
		return s.handleCancellationEvent(user, webhook)
	case "BILLING_ISSUE":
		return s.handleBillingIssue(user, webhook)
	default:
		// Log unknown webhook type but don't fail
		fmt.Printf("Unknown webhook type: %s\n", webhook.Event.Type)
		return nil
	}
}

func (s *service) handlePurchaseEvent(user *entities.User, webhook *dtos.RevenueCatWebhookRequest) error {
	// Extract subscription data from webhook
	productID := webhook.Event.ProductID
	originalTransactionID := webhook.Event.OriginalTransactionID
	expiresDate := webhook.Event.ExpirationDate

	// Check if subscription already exists
	existingSubscription, err := s.repository.GetSubscriptionByOriginalTransactionID(originalTransactionID)
	if err != nil {
		return err
	}

	if existingSubscription != nil {
		// Update existing subscription
		existingSubscription.UpdateFromWebhook(webhook.Event.Type, expiresDate, true, true)
		existingSubscription.TransactionID = webhook.Event.TransactionID
		err = s.repository.UpdateSubscription(existingSubscription)
	} else {
		// Create new subscription
		subscription := &entities.Subscription{
			UserID:                user.ID.String(),
			RevenueCatCustomerID:  webhook.Event.AppUserID,
			ProductID:             productID,
			OriginalTransactionID: originalTransactionID,
			TransactionID:         webhook.Event.TransactionID,
			PurchaseDate:          webhook.Event.PurchaseDate,
			ExpiresDate:           expiresDate,
			IsActive:              true,
			AutoRenewStatus:       true,
			Environment:           webhook.Event.Environment,
			Store:                 webhook.Event.Store,
			LastWebhookType:       webhook.Event.Type,
			LastWebhookDate:       time.Now(),
		}
		err = s.repository.CreateSubscription(subscription)
	}

	if err != nil {
		return err
	}

	// Update user's premium status
	return s.SyncUserPremiumStatus(user.ID.String())
}

func (s *service) handleCancellationEvent(user *entities.User, webhook *dtos.RevenueCatWebhookRequest) error {
	subscription, err := s.repository.GetSubscriptionByOriginalTransactionID(webhook.Event.OriginalTransactionID)
	if err != nil {
		return err
	}

	if subscription != nil {
		subscription.UpdateFromWebhook(webhook.Event.Type, webhook.Event.ExpirationDate, false, false)
		err = s.repository.UpdateSubscription(subscription)
		if err != nil {
			return err
		}
	}

	// Update user's premium status
	return s.SyncUserPremiumStatus(user.ID.String())
}

func (s *service) handleBillingIssue(user *entities.User, webhook *dtos.RevenueCatWebhookRequest) error {
	subscription, err := s.repository.GetSubscriptionByOriginalTransactionID(webhook.Event.OriginalTransactionID)
	if err != nil {
		return err
	}

	if subscription != nil {
		subscription.UpdateFromWebhook(webhook.Event.Type, webhook.Event.ExpirationDate, false, subscription.AutoRenewStatus)
		err = s.repository.UpdateSubscription(subscription)
		if err != nil {
			return err
		}
	}

	// Update user's premium status
	return s.SyncUserPremiumStatus(user.ID.String())
}

func (s *service) GetUserSubscriptionStatus(userID string) (*dtos.SubscriptionStatusResponse, error) {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		// If user not found, return default free status instead of error
		return &dtos.SubscriptionStatusResponse{
			IsPremium:     false,
			Plan:          "free",
			ExpiresAt:     nil,
			RemainingDays: 0,
		}, nil
	}

	subscription, err := s.repository.GetActiveSubscriptionByUserID(userID)
	if err != nil {
		// If subscription query fails, return user's current premium status
		return &dtos.SubscriptionStatusResponse{
			IsPremium:     user.IsPremium(),
			Plan:          user.Plan,
			ExpiresAt:     user.PremiumExpiresAt,
			RemainingDays: 0,
		}, nil
	}

	response := &dtos.SubscriptionStatusResponse{
		IsPremium:     user.IsPremium(),
		Plan:          user.Plan,
		ExpiresAt:     user.PremiumExpiresAt,
		RemainingDays: user.GetPremiumRemainingDays(),
	}

	if subscription != nil {
		response.ProductID = subscription.ProductID
		response.AutoRenewStatus = subscription.AutoRenewStatus
		response.Store = subscription.Store
	}

	return response, nil
}

func (s *service) ActivateSubscription(userID, productID, originalTransactionID string, expiresDate time.Time, revenueCatCustomerID string) error {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		return err
	}

	// Create or update subscription
	existingSubscription, err := s.repository.GetSubscriptionByOriginalTransactionID(originalTransactionID)
	if err != nil {
		return err
	}

	if existingSubscription != nil {
		existingSubscription.ExpiresDate = expiresDate
		existingSubscription.IsActive = true
		existingSubscription.AutoRenewStatus = true
		err = s.repository.UpdateSubscription(existingSubscription)
	} else {
		subscription := &entities.Subscription{
			UserID:                userID,
			RevenueCatCustomerID:  revenueCatCustomerID,
			ProductID:             productID,
			OriginalTransactionID: originalTransactionID,
			PurchaseDate:          time.Now(),
			ExpiresDate:           expiresDate,
			IsActive:              true,
			AutoRenewStatus:       true,
			Environment:           "PRODUCTION", // Default to production
			Store:                 "APP_STORE",  // Default to App Store
		}
		err = s.repository.CreateSubscription(subscription)
	}

	if err != nil {
		return err
	}

	// Update user's premium status
	user.ActivatePremium(expiresDate, revenueCatCustomerID)
	return s.authService.UpdateUser(user)
}

func (s *service) DeactivateSubscription(userID string) error {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		return err
	}

	// Deactivate user's premium
	user.DeactivatePremium()
	return s.authService.UpdateUser(user)
}

func (s *service) SyncUserPremiumStatus(userID string) error {
	user, err := s.authService.GetUserByID(userID)
	if err != nil {
		return err
	}

	activeSubscription, err := s.repository.GetActiveSubscriptionByUserID(userID)
	if err != nil {
		return err
	}

	if activeSubscription != nil && activeSubscription.IsValidAndActive() {
		// User has active subscription
		user.ActivatePremium(activeSubscription.ExpiresDate, activeSubscription.RevenueCatCustomerID)
	} else {
		// No active subscription
		user.DeactivatePremium()
	}

	return s.authService.UpdateUser(user)
}
