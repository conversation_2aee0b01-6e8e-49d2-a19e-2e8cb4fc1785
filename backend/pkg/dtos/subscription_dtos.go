package dtos

import "time"

// RevenueCat Webhook DTOs
type RevenueCatWebhookRequest struct {
	APIVersion string                 `json:"api_version"`
	Event      RevenueCatWebhookEvent `json:"event"`
}

type RevenueCatWebhookEvent struct {
	Type                  string    `json:"type"`
	AppUserID             string    `json:"app_user_id"`
	ProductID             string    `json:"product_id"`
	TransactionID         string    `json:"transaction_id"`
	OriginalTransactionID string    `json:"original_transaction_id"`
	PurchaseDate          time.Time `json:"purchase_date"`
	ExpirationDate        time.Time `json:"expiration_date"`
	Environment           string    `json:"environment"`
	Store                 string    `json:"store"`
	AutoRenewStatus       bool      `json:"auto_renew_status"`
}

// Subscription Status Response
type SubscriptionStatusResponse struct {
	IsPremium       bool       `json:"is_premium"`
	Plan            string     `json:"plan"`
	ExpiresAt       *time.Time `json:"expires_at"`
	RemainingDays   int        `json:"remaining_days"`
	ProductID       string     `json:"product_id,omitempty"`
	AutoRenewStatus bool       `json:"auto_renew_status,omitempty"`
	Store           string     `json:"store,omitempty"`
}

// Manual Subscription Activation Request (for testing or manual activation)
type ActivateSubscriptionRequest struct {
	UserID                string    `json:"user_id" binding:"required"`
	ProductID             string    `json:"product_id" binding:"required"`
	OriginalTransactionID string    `json:"original_transaction_id" binding:"required"`
	ExpiresDate           time.Time `json:"expires_date" binding:"required"`
	RevenueCatCustomerID  string    `json:"revenuecat_customer_id"`
}

// RevenueCat Purchase Notification (from frontend)
type RevenueCatPurchaseNotification struct {
	CustomerInfo          any       `json:"customer_info"`
	ProductID             string    `json:"product_id"`
	OriginalTransactionID string    `json:"original_transaction_id"`
	TransactionID         string    `json:"transaction_id"`
	PurchaseDate          time.Time `json:"purchase_date"`
	ExpirationDate        time.Time `json:"expiration_date"`
}
