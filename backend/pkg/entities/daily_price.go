package entities

import "time"

// DailyPrice stores a normalized daily close price per asset and date
type DailyPrice struct {
	Base
	// Primary symbol column in DB (e.g., BTC, XAUTRY=X, USDTRY=X)
	Symbol string `gorm:"column:symbol;not null;index:uq_symbol_date,unique" json:"symbol"`
	// Legacy 'asset' column kept for compatibility with existing NOT NULL schemas
	Asset    string    ` json:"asset"`
	Date     time.Time `gorm:"not null;index:uq_symbol_date,unique" json:"date"` // normalized to 00:00:00 UTC
	Price    float64   `gorm:"not null;default:0" json:"price"`                  // usually equals Close
	Open     float64   `gorm:"column:open;not null;default:0" json:"open"`
	High     float64   `gorm:"column:high;not null;default:0" json:"high"`
	Low      float64   `gorm:"column:low;not null;default:0" json:"low"`
	Close    float64   `gorm:"column:close;not null;default:0" json:"close"`
	Volume   float64   `gorm:"column:volume;not null;default:0" json:"volume"`
	Currency string    `gorm:"size:8" json:"currency"`
	Source   string    `gorm:"size:16" json:"source"` // e.g., YAHOO, BINANCE
}

// TableName overrides the table name used by GORM
func (DailyPrice) TableName() string { return "daily_prices" }
