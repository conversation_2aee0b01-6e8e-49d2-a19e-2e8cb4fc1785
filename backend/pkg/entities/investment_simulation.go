package entities

import (
	"time"

	"github.com/google/uuid"
)

// InvestmentSimulation represents an investment simulation record
type InvestmentSimulation struct {
	Base
	UserID              uuid.UUID `json:"user_id" gorm:"type:uuid;not null"`
	Asset               string    `json:"asset" gorm:"not null"`                // BTC, ETH, GOLD, USDTRY, BIST100, etc.
	AmountInvested      float64   `json:"amount_invested" gorm:"not null"`      // Original investment amount
	StartDate           time.Time `json:"start_date" gorm:"not null"`           // Investment start date
	PriceAtStart        float64   `json:"price_at_start" gorm:"not null"`       // Asset price at start date
	CurrentPrice        float64   `json:"current_price"`                        // Current asset price
	UnitsBought         float64   `json:"units_bought" gorm:"not null"`         // Number of units bought
	CurrentValue        float64   `json:"current_value"`                        // Current value of investment
	Profit              float64   `json:"profit"`                               // Profit/loss amount
	GrowthRatePercent   float64   `json:"growth_rate_percent"`                  // Growth rate percentage
	SimulationType      string    `json:"simulation_type" gorm:"not null"`      // "simulate" or "whatif"
	HypotheticalDate    *time.Time `json:"hypothetical_date"`                   // For whatif simulations
	PriceThen           *float64   `json:"price_then"`                          // Price at hypothetical date
	PriceNow            *float64   `json:"price_now"`                           // Current price for whatif
}

// CalculateInvestment calculates the investment metrics
func (i *InvestmentSimulation) CalculateInvestment() {
	// Calculate units bought
	i.UnitsBought = i.AmountInvested / i.PriceAtStart
	
	// Calculate current value
	if i.CurrentPrice > 0 {
		i.CurrentValue = i.UnitsBought * i.CurrentPrice
	}
	
	// Calculate profit/loss
	i.Profit = i.CurrentValue - i.AmountInvested
	
	// Calculate growth rate percentage
	if i.AmountInvested > 0 {
		i.GrowthRatePercent = (i.Profit / i.AmountInvested) * 100
	}
}

// CalculateWhatIf calculates the what-if simulation metrics
func (i *InvestmentSimulation) CalculateWhatIf() {
	if i.PriceThen == nil || i.PriceNow == nil {
		return
	}
	
	// Calculate units that would have been bought
	i.UnitsBought = i.AmountInvested / *i.PriceThen
	
	// Calculate current value
	i.CurrentValue = i.UnitsBought * *i.PriceNow
	
	// Calculate profit/loss
	i.Profit = i.CurrentValue - i.AmountInvested
	
	// Calculate growth rate percentage
	if i.AmountInvested > 0 {
		i.GrowthRatePercent = (i.Profit / i.AmountInvested) * 100
	}
}
