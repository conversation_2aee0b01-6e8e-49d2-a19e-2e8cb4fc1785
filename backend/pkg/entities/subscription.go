package entities

import (
	"time"
)

// Subscription represents a user's premium subscription
type Subscription struct {
	Base
	UserID                string    `json:"user_id" gorm:"not null;index"`
	RevenueCatCustomerID  string    `json:"revenuecat_customer_id" gorm:"index"`
	ProductID             string    `json:"product_id"`                                    // com.butce360.app.one.month, com.butce360.app.one.year
	OriginalTransactionID string    `json:"original_transaction_id" gorm:"unique;index"` // RevenueCat original transaction ID
	TransactionID         string    `json:"transaction_id"`                              // Current transaction ID
	PurchaseDate          time.Time `json:"purchase_date"`
	ExpiresDate           time.Time `json:"expires_date"`
	IsActive              bool      `json:"is_active" gorm:"default:false"`
	AutoRenewStatus       bool      `json:"auto_renew_status" gorm:"default:true"`
	Environment           string    `json:"environment"` // "SANDBOX" or "PRODUCTION"
	Store                 string    `json:"store"`       // "APP_STORE" or "PLAY_STORE"
	
	// Webhook tracking
	LastWebhookType string    `json:"last_webhook_type"`
	LastWebhookDate time.Time `json:"last_webhook_date"`
	
	// Relations
	User User `json:"user" gorm:"foreignKey:UserID;references:ID"`
}

// IsExpired checks if the subscription has expired
func (s *Subscription) IsExpired() bool {
	return time.Now().After(s.ExpiresDate)
}

// IsValidAndActive checks if subscription is both active and not expired
func (s *Subscription) IsValidAndActive() bool {
	return s.IsActive && !s.IsExpired()
}

// GetRemainingDays returns the number of days remaining in the subscription
func (s *Subscription) GetRemainingDays() int {
	if s.IsExpired() {
		return 0
	}
	duration := s.ExpiresDate.Sub(time.Now())
	return int(duration.Hours() / 24)
}

// UpdateFromWebhook updates subscription data from RevenueCat webhook
func (s *Subscription) UpdateFromWebhook(webhookType string, expiresDate time.Time, isActive bool, autoRenewStatus bool) {
	s.ExpiresDate = expiresDate
	s.IsActive = isActive
	s.AutoRenewStatus = autoRenewStatus
	s.LastWebhookType = webhookType
	s.LastWebhookDate = time.Now()
}
