package entities

import (
	"time"

	"github.com/google/uuid"
)

type Transaction struct {
	Base
	Title                  string     `json:"title"`
	Type                   string     `json:"type"`
	Amount                 float64    `json:"amount"`
	Currency               string     `json:"currency"`
	CategoryID             uuid.UUID  `json:"category_id"`
	PaymentMethod          string     `json:"payment_method"`
	AccountID              uuid.UUID  `json:"account_id"`
	Note                   string     `json:"note"`
	TransactionDate        time.Time  `json:"transaction_date"`
	Location               string     `json:"location"`
	UserID                 uuid.UUID  `json:"user_id"`
	RecurringTransactionID *uuid.UUID `json:"recurring_transaction_id,omitempty"`
	IsFromRecurring        bool       `json:"is_from_recurring"`
}
