package entities

import (
	"time"
)

type User struct {
	Base
	Username        string  `json:"username"`
	Email           string  `json:"email" gorm:"unique"`
	Password        string  `json:"password"`
	Name            string  `json:"name"`
	LastLogin       string  `json:"last_login"`
	Status          string  `json:"status"`                        // "active", "inactive", "banned"
	GoogleID        *string `json:"google_id"`                     // Google OAuth ID
	AppleID         *string `json:"apple_id"`                      // Apple OAuth ID
	GoogleAuth      *string `json:"google_auth" gorm:"type:jsonb"` // Google OAuth data
	AppleAuth       *string `json:"apple_auth" gorm:"type:jsonb"`  // Apple OAuth data
	OAuthProvider   *string `json:"oauth_provider"`                // "google", "apple", "email"
	ProfileImageURL *string `json:"profile_image_url"`             // Profile image from OAuth

	// Guest User Fields
	IsGuest          bool   `json:"is_guest" gorm:"default:false"`      // Whether user is a guest
	GuestID          string `json:"guest_id"`                           // UUID for guest identification
	Plan             string `json:"plan" gorm:"default:'free'"`         // "guest", "free", "premium"
	TransactionLimit int    `json:"transaction_limit" gorm:"default:0"` // Transaction limit for current plan
	TransactionCount int    `json:"transaction_count" gorm:"default:0"` // Current transaction count

	// Premium Subscription Fields
	PremiumExpiresAt *time.Time `json:"premium_expires_at"` // When premium subscription expires
	RevenueCatID     *string    `json:"revenuecat_id"`      // RevenueCat customer ID
}

// Helper methods for guest user management
func (u *User) IsGuestUser() bool {
	return u.IsGuest
}

func (u *User) CanCreateTransaction() bool {
	if !u.IsGuest {
		return true // Registered users have unlimited transactions
	}
	return u.TransactionCount < u.TransactionLimit
}

func (u *User) GetRemainingTransactions() int {
	if !u.IsGuest {
		return -1 // Unlimited for registered users
	}
	remaining := u.TransactionLimit - u.TransactionCount
	if remaining < 0 {
		return 0
	}
	return remaining
}

func (u *User) IncrementTransactionCount() {
	u.TransactionCount++
}

func (u *User) ConvertToRegistered() {
	u.IsGuest = false
	u.Plan = "free"
	u.TransactionLimit = 0 // Unlimited for registered users
	u.GuestID = ""         // Clear guest ID
}

// Premium subscription methods
func (u *User) IsPremium() bool {
	if u.Plan != "premium" {
		return false
	}
	if u.PremiumExpiresAt == nil {
		return false
	}
	return time.Now().Before(*u.PremiumExpiresAt)
}

func (u *User) HasValidPremium() bool {
	return u.IsPremium()
}

func (u *User) CanAccessPremiumFeatures() bool {
	// Only authenticated (non-guest) users with valid premium can access premium features
	return !u.IsGuest && u.HasValidPremium()
}

func (u *User) GetPremiumRemainingDays() int {
	if !u.IsPremium() {
		return 0
	}
	duration := u.PremiumExpiresAt.Sub(time.Now())
	days := int(duration.Hours() / 24)
	if days < 0 {
		return 0
	}
	return days
}

func (u *User) ActivatePremium(expiresAt time.Time, revenueCatID string) {
	u.Plan = "premium"
	u.PremiumExpiresAt = &expiresAt
	if revenueCatID != "" {
		u.RevenueCatID = &revenueCatID
	}
}

func (u *User) DeactivatePremium() {
	u.Plan = "free"
	u.PremiumExpiresAt = nil
}
