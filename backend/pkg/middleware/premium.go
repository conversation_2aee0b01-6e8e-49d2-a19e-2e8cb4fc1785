package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/domains/auth"
	"github.com/nocytech/butce360/pkg/state"
)

// RequiresPremium middleware checks if user has active premium subscription
func RequiresPremium(authService auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by Authorized middleware)
		userID := state.GetCurrentUserID(c)
		if userID == uuid.Nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error":   "Unauthorized",
				"message": "User not authenticated",
				"status":  http.StatusUnauthorized,
			})
			return
		}

		// Get user from database
		user, err := authService.GetUserByID(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Internal server error",
				"message": "Failed to get user information",
				"status":  http.StatusInternalServerError,
			})
			return
		}

		// Check if user is guest
		if user.IsGuest {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":   "Login required",
				"message": "Bu özellik için giriş yapmanız gerekiyor",
				"status":  http.StatusForbidden,
				"code":    "LOGIN_REQUIRED",
			})
			return
		}

		// Check if user has premium access
		if !user.CanAccessPremiumFeatures() {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":      "Premium subscription required",
				"message":    "Bu özellik premium abonelik gerektirir",
				"status":     http.StatusForbidden,
				"code":       "PREMIUM_REQUIRED",
				"user_plan":  user.Plan,
				"is_premium": user.IsPremium(),
			})
			return
		}

		// User has premium access, continue
		c.Next()
	}
}

// RequiresLogin middleware checks if user is authenticated (not guest)
func RequiresLogin(authService auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user ID from context (set by Authorized middleware)
		userID := state.GetCurrentUserID(c)
		if userID == uuid.Nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error":   "Unauthorized",
				"message": "User not authenticated",
				"status":  http.StatusUnauthorized,
			})
			return
		}

		// Get user from database
		user, err := authService.GetUserByID(userID.String())
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"error":   "Internal server error",
				"message": "Failed to get user information",
				"status":  http.StatusInternalServerError,
			})
			return
		}

		// Check if user is guest
		if user.IsGuest {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":   "Login required",
				"message": "Bu özellik için giriş yapmanız gerekiyor",
				"status":  http.StatusForbidden,
				"code":    "LOGIN_REQUIRED",
			})
			return
		}

		// User is authenticated, continue
		c.Next()
	}
}
