package utils

import (
	"context"

	"github.com/nocytech/butce360/pkg/config"
	"github.com/cloudinary/cloudinary-go"
	"github.com/cloudinary/cloudinary-go/api/uploader"
	"github.com/go-playground/validator/v10"
)

func CloudinaryImageUpload(ctx context.Context, file interface{}) (*uploader.UploadResult, error) {
	var validate = validator.New()
	if err := validate.Struct(file); err != nil {
		return nil, err
	}
	cld, err := cloudinary.NewFromParams(
		config.ReadValue().Cloudinary.Name,
		config.ReadValue().Cloudinary.APIKey,
		config.ReadValue().Cloudinary.APISecret,
	)
	if err != nil {
		return nil, err
	}
	uploadParam, err := cld.Upload.Upload(ctx, file, uploader.UploadParams{Folder: config.ReadValue().Cloudinary.APIFolder})
	if err != nil {
		return nil, err
	}
	return uploadParam, nil
}

func CloudinaryRemoveImage(ctx context.Context, publicID string) error {
	cld, err := cloudinary.NewFromParams(
		config.ReadValue().Cloudinary.Name,
		config.ReadValue().Cloudinary.APIKey,
		config.ReadValue().Cloudinary.APISecret,
	)
	if err != nil {
		return err
	}
	_, err = cld.Upload.Destroy(ctx, uploader.DestroyParams{
		PublicID: publicID,
	})
	if err != nil {
		return err
	}
	return nil
}
