package com.butce360;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;

public class GoogleSignInManagerModule extends ReactContextBaseJavaModule {
    private GoogleSignInModule googleSignInModule;

    public GoogleSignInManagerModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.googleSignInModule = new GoogleSignInModule(reactContext);
    }

    @Override
    public String getName() {
        return "GoogleSignInManager";
    }

    @ReactMethod
    public void configure(Promise promise) {
        googleSignInModule.configure(promise);
    }

    @ReactMethod
    public void signIn(Promise promise) {
        googleSignInModule.signIn(promise);
    }

    @ReactMethod
    public void signOut(Promise promise) {
        googleSignInModule.signOut(promise);
    }

    @ReactMethod
    public void isSignedIn(Promise promise) {
        googleSignInModule.isSignedIn(promise);
    }
}
