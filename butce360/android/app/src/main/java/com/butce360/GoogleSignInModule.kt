package com.butce360

import android.app.Activity
import android.content.Intent
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task

class GoogleSignInModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext), ActivityEventListener {

    private var googleSignInClient: GoogleSignInClient? = null
    private var signInPromise: Promise? = null
    
    companion object {
        private const val RC_SIGN_IN = 9001
        private const val MODULE_NAME = "GoogleSignInManager"
    }

    init {
        reactContext.addActivityEventListener(this)
    }

    override fun getName(): String {
        return MODULE_NAME
    }

    @ReactMethod
    fun configure(promise: Promise) {
        try {
            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestIdToken("************-euuf2l3bnlvf708n3504m2qk3gm7ss94.apps.googleusercontent.com")
                .requestEmail()
                .build()

            googleSignInClient = GoogleSignIn.getClient(reactApplicationContext, gso)
            
            val result = Arguments.createMap()
            result.putBoolean("success", true)
            promise.resolve(result)
        } catch (e: Exception) {
            promise.reject("CONFIG_ERROR", "Failed to configure Google Sign-In: ${e.message}", e)
        }
    }

    @ReactMethod
    fun signIn(promise: Promise) {
        if (googleSignInClient == null) {
            promise.reject("NOT_CONFIGURED", "Google Sign-In not configured. Call configure() first.", null)
            return
        }

        val currentActivity = currentActivity
        if (currentActivity == null) {
            promise.reject("NO_ACTIVITY", "No current activity available", null)
            return
        }

        signInPromise = promise
        val signInIntent = googleSignInClient!!.signInIntent
        currentActivity.startActivityForResult(signInIntent, RC_SIGN_IN)
    }

    @ReactMethod
    fun signOut(promise: Promise) {
        if (googleSignInClient == null) {
            promise.reject("NOT_CONFIGURED", "Google Sign-In not configured", null)
            return
        }

        googleSignInClient!!.signOut()
            .addOnCompleteListener {
                val result = Arguments.createMap()
                result.putBoolean("success", true)
                promise.resolve(result)
            }
    }

    @ReactMethod
    fun isSignedIn(promise: Promise) {
        val account = GoogleSignIn.getLastSignedInAccount(reactApplicationContext)
        val result = Arguments.createMap()
        result.putBoolean("isSignedIn", account != null)
        promise.resolve(result)
    }

    override fun onActivityResult(activity: Activity?, requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == RC_SIGN_IN) {
            val task = GoogleSignIn.getSignedInAccountFromIntent(data)
            handleSignInResult(task)
        }
    }

    override fun onNewIntent(intent: Intent?) {
        // Not needed for Google Sign-In
    }

    private fun handleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            val account = completedTask.getResult(ApiException::class.java)
            
            val result = Arguments.createMap()
            result.putBoolean("success", true)
            result.putString("userID", account.id)
            result.putString("email", account.email)
            result.putString("name", account.displayName)
            result.putString("givenName", account.givenName)
            result.putString("familyName", account.familyName)
            
            account.photoUrl?.let {
                result.putString("imageUrl", it.toString())
            }
            
            account.idToken?.let {
                result.putString("idToken", it)
            }
            
            // Note: Access token is not directly available in Google Sign-In for Android
            // You would need to use GoogleSignInAccount.getServerAuthCode() for server-side auth
            account.serverAuthCode?.let {
                result.putString("serverAuthCode", it)
            }

            signInPromise?.resolve(result)
        } catch (e: ApiException) {
            val errorMessage = when (e.statusCode) {
                12501 -> "User canceled sign-in"
                12502 -> "Sign-in currently in progress"
                else -> "Sign-in failed: ${e.message}"
            }
            signInPromise?.reject("SIGN_IN_ERROR", errorMessage, e)
        } finally {
            signInPromise = null
        }
    }
}
