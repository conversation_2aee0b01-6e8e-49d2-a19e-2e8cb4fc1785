# Apple Sign-In Troubleshooting Guide

## Error 1000 - Configuration Error

The `com.apple.AuthenticationServices.AuthorizationError error 1000` typically indicates a configuration issue with Apple Sign-In.

### Common Causes and Solutions:

#### 1. Bundle ID Mismatch
**Problem**: The bundle ID in your app doesn't match what's configured in Apple Developer Console.
**Solution**: 
- Verify bundle ID in Xcode matches Apple Developer Console
- Current bundle ID: `com.butce360.app`
- Check Apple Developer Console > Certificates, Identifiers & Profiles > Identifiers

#### 2. Missing or Incorrect Entitlements
**Problem**: Sign in with Apple capability isn't properly configured.
**Solution**:
- Ensure both Debug and Release entitlements have Apple Sign-In capability
- Files should contain:
```xml
<key>com.apple.developer.applesignin</key>
<array>
    <string>Default</string>
</array>
```

#### 3. Team ID Issues
**Problem**: Team ID doesn't match your Apple Developer account.
**Solution**:
- Current Team ID: `ZQBNU94H5R`
- Verify this matches your Apple Developer account

#### 4. Apple Developer Console Configuration
**Problem**: Apple Sign-In not enabled for your App ID.
**Solution**:
1. Go to Apple Developer Console
2. Navigate to Certificates, Identifiers & Profiles
3. Select your App ID (`com.butce360.app`)
4. Ensure "Sign In with Apple" capability is enabled
5. Configure the capability if needed

#### 5. Provisioning Profile Issues
**Problem**: Provisioning profile doesn't include Apple Sign-In capability.
**Solution**:
1. Regenerate provisioning profiles in Apple Developer Console
2. Download and install new profiles
3. Clean and rebuild the project

### Testing Steps:

1. **Test on Real Device**: Apple Sign-In doesn't work reliably in simulators
2. **Check Network**: Ensure stable internet connection
3. **Clean Build**: Clean Xcode project and rebuild
4. **Restart Device**: Sometimes helps with authentication services

### Debug Information:

The app now includes better error handling with specific error codes:
- 1000: Configuration error
- 1001: User canceled
- 1002: Invalid request
- 1003: Not available
- 1004: Failed

### Implementation Details:

Current implementation uses `@invertase/react-native-apple-authentication` library with proper error handling and availability checks.

### If Issues Persist:

1. Check Apple System Status for Sign-In services
2. Verify Apple Developer account is in good standing
3. Contact Apple Developer Support if configuration appears correct
