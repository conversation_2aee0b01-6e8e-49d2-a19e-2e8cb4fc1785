import Foundation
import AuthenticationServices
import React

@objc(AppleSignInManager)
class AppleSignInManager: NSObject, RCTBridgeModule {
    
    static func moduleName() -> String! {
        return "AppleSignInManager"
    }
    
    static func requiresMainQueueSetup() -> <PERSON><PERSON> {
        return true
    }
    
    @objc
    func signIn(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        
        guard #available(iOS 13.0, *) else {
            reject("UNSUPPORTED", "Apple Sign-In is only available on iOS 13+", nil)
            return
        }
        
        DispatchQueue.main.async {
            let request = ASAuthorizationAppleIDProvider().createRequest()
            request.requestedScopes = [.fullName, .email]
            
            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            authorizationController.delegate = AppleSignInDelegate(resolve: resolve, reject: reject)
            authorizationController.presentationContextProvider = AppleSignInPresentationContextProvider()
            authorizationController.performRequests()
        }
    }
    
    @objc
    func isAvailable(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        print("[AppleSignInManager] Checking availability...")

        if #available(iOS 13.0, *) {
            print("[AppleSignInManager] iOS 13+ detected")

            #if targetEnvironment(simulator)
            print("[AppleSignInManager] Running on simulator - Apple Sign-In available")
            resolve(true)
            #else
            print("[AppleSignInManager] Running on device - Apple Sign-In available")
            resolve(true)
            #endif
        } else {
            print("[AppleSignInManager] iOS version < 13.0")
            resolve(false)
        }
    }
}

@available(iOS 13.0, *)
class AppleSignInDelegate: NSObject, ASAuthorizationControllerDelegate {
    private let resolve: RCTPromiseResolveBlock
    private let reject: RCTPromiseRejectBlock
    
    init(resolve: @escaping RCTPromiseResolveBlock, reject: @escaping RCTPromiseRejectBlock) {
        self.resolve = resolve
        self.reject = reject
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            let userIdentifier = appleIDCredential.user
            let fullName = appleIDCredential.fullName
            let email = appleIDCredential.email
            let identityToken = appleIDCredential.identityToken
            let authorizationCode = appleIDCredential.authorizationCode
            
            var result: [String: Any] = [
                "userIdentifier": userIdentifier,
                "success": true
            ]
            
            if let fullName = fullName {
                var nameComponents: [String: String] = [:]
                if let givenName = fullName.givenName {
                    nameComponents["givenName"] = givenName
                }
                if let familyName = fullName.familyName {
                    nameComponents["familyName"] = familyName
                }
                result["fullName"] = nameComponents
            }
            
            if let email = email {
                result["email"] = email
            }
            
            if let identityToken = identityToken,
               let tokenString = String(data: identityToken, encoding: .utf8) {
                result["identityToken"] = tokenString
            }
            
            if let authorizationCode = authorizationCode,
               let codeString = String(data: authorizationCode, encoding: .utf8) {
                result["authorizationCode"] = codeString
            }
            
            resolve(result)
        } else {
            reject("INVALID_CREDENTIAL", "Invalid Apple ID credential", nil)
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
                reject("USER_CANCELED", "User canceled Apple Sign-In", nil)
            case .failed:
                reject("SIGN_IN_FAILED", "Apple Sign-In failed", error)
            case .invalidResponse:
                reject("INVALID_RESPONSE", "Invalid response from Apple", error)
            case .notHandled:
                reject("NOT_HANDLED", "Apple Sign-In not handled", error)
            case .unknown:
                reject("UNKNOWN_ERROR", "Unknown Apple Sign-In error", error)
            @unknown default:
                reject("UNKNOWN_ERROR", "Unknown Apple Sign-In error", error)
            }
        } else {
            reject("SIGN_IN_ERROR", "Apple Sign-In error", error)
        }
    }
}

@available(iOS 13.0, *)
class AppleSignInPresentationContextProvider: NSObject, ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        return UIApplication.shared.windows.first { $0.isKeyWindow } ?? UIWindow()
    }
}
