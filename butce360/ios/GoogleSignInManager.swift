import Foundation
import GoogleSignIn
import React

@objc(GoogleSignInManager)
class GoogleSignInManager: NSObject, RCTBridgeModule {
    
    static func moduleName() -> String! {
        return "GoogleSignInManager"
    }
    
    static func requiresMainQueueSetup() -> <PERSON><PERSON> {
        return true
    }
    
    @objc
    func configure(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        guard let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path),
              let clientId = plist["CLIENT_ID"] as? String else {
            reject("CONFIG_ERROR", "GoogleService-Info.plist not found or CLIENT_ID missing", nil)
            return
        }
        
        guard let configuration = GIDConfiguration(clientID: clientId) else {
            reject("CONFIG_ERROR", "Failed to create Google Sign-In configuration", nil)
            return
        }
        
        GIDSignIn.sharedInstance.configuration = configuration
        resolve(["success": true])
    }
    
    @objc
    func signIn(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        
        DispatchQueue.main.async {
            guard let presentingViewController = UIApplication.shared.windows.first?.rootViewController else {
                reject("NO_VIEW_CONTROLLER", "No presenting view controller found", nil)
                return
            }
            
            GIDSignIn.sharedInstance.signIn(withPresenting: presentingViewController) { result, error in
                if let error = error {
                    reject("SIGN_IN_ERROR", "Google Sign-In failed", error)
                    return
                }
                
                guard let result = result,
                      let user = result.user else {
                    reject("NO_USER", "No user data received", nil)
                    return
                }
                
                let profile = user.profile
                var userData: [String: Any] = [
                    "success": true,
                    "userID": user.userID ?? "",
                ]
                
                if let profile = profile {
                    userData["email"] = profile.email
                    userData["name"] = profile.name
                    userData["givenName"] = profile.givenName
                    userData["familyName"] = profile.familyName
                    
                    if let imageUrl = profile.imageURL(withDimension: 120) {
                        userData["imageUrl"] = imageUrl.absoluteString
                    }
                }
                
                // Get ID Token
                if let idToken = user.idToken?.tokenString {
                    userData["idToken"] = idToken
                }
                
                // Get Access Token
                if let accessToken = user.accessToken.tokenString {
                    userData["accessToken"] = accessToken
                }
                
                resolve(userData)
            }
        }
    }
    
    @objc
    func signOut(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        GIDSignIn.sharedInstance.signOut()
        resolve(["success": true])
    }
    
    @objc
    func isSignedIn(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        let isSignedIn = GIDSignIn.sharedInstance.currentUser != nil
        resolve(["isSignedIn": isSignedIn])
    }
}
