PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - fast_float (8.0.0)
  - FBLazyVector (0.81.0)
  - fmt (11.0.2)
  - glog (0.3.5)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - hermes-engine (0.81.0):
    - hermes-engine/Pre-built (= 0.81.0)
  - hermes-engine/Pre-built (0.81.0)
  - PromisesObjC (2.4.0)
  - PurchasesHybridCommon (17.3.0):
    - RevenueCat (= 5.36.0)
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.81.0)
  - RCTRequired (0.81.0)
  - RCTTypeSafety (0.81.0):
    - FBLazyVector (= 0.81.0)
    - RCTRequired (= 0.81.0)
    - React-Core (= 0.81.0)
  - React (0.81.0):
    - React-Core (= 0.81.0)
    - React-Core/DevSupport (= 0.81.0)
    - React-Core/RCTWebSocket (= 0.81.0)
    - React-RCTActionSheet (= 0.81.0)
    - React-RCTAnimation (= 0.81.0)
    - React-RCTBlob (= 0.81.0)
    - React-RCTImage (= 0.81.0)
    - React-RCTLinking (= 0.81.0)
    - React-RCTNetwork (= 0.81.0)
    - React-RCTSettings (= 0.81.0)
    - React-RCTText (= 0.81.0)
    - React-RCTVibration (= 0.81.0)
  - React-callinvoker (0.81.0)
  - React-Core (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.81.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/CoreModulesHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/Default (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/DevSupport (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.81.0)
    - React-Core/RCTWebSocket (= 0.81.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTAnimationHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTBlobHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTImageHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTLinkingHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTNetworkHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTSettingsHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTTextHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTVibrationHeaders (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTWebSocket (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.81.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-CoreModules (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety (= 0.81.0)
    - React-Core/CoreModulesHeaders (= 0.81.0)
    - React-jsi (= 0.81.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.81.0)
    - React-runtimeexecutor
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.81.0)
    - React-debug (= 0.81.0)
    - React-jsi (= 0.81.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-logger (= 0.81.0)
    - React-perflogger (= 0.81.0)
    - React-runtimeexecutor
    - React-timing (= 0.81.0)
    - SocketRocket
  - React-debug (0.81.0)
  - React-defaultsnativemodule (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
    - SocketRocket
  - React-domnativemodule (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Fabric
    - React-Fabric/bridging
    - React-FabricComponents
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.81.0)
    - React-Fabric/attributedstring (= 0.81.0)
    - React-Fabric/bridging (= 0.81.0)
    - React-Fabric/componentregistry (= 0.81.0)
    - React-Fabric/componentregistrynative (= 0.81.0)
    - React-Fabric/components (= 0.81.0)
    - React-Fabric/consistency (= 0.81.0)
    - React-Fabric/core (= 0.81.0)
    - React-Fabric/dom (= 0.81.0)
    - React-Fabric/imagemanager (= 0.81.0)
    - React-Fabric/leakchecker (= 0.81.0)
    - React-Fabric/mounting (= 0.81.0)
    - React-Fabric/observers (= 0.81.0)
    - React-Fabric/scheduler (= 0.81.0)
    - React-Fabric/telemetry (= 0.81.0)
    - React-Fabric/templateprocessor (= 0.81.0)
    - React-Fabric/uimanager (= 0.81.0)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/animations (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/attributedstring (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/bridging (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistry (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistrynative (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.81.0)
    - React-Fabric/components/root (= 0.81.0)
    - React-Fabric/components/scrollview (= 0.81.0)
    - React-Fabric/components/view (= 0.81.0)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/legacyviewmanagerinterop (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/root (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/scrollview (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/view (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric/consistency (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/core (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/dom (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/imagemanager (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/leakchecker (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/mounting (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.81.0)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers/events (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/scheduler (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/telemetry (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/templateprocessor (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.81.0)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager/consistency (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-FabricComponents (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.81.0)
    - React-FabricComponents/textlayoutmanager (= 0.81.0)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.81.0)
    - React-FabricComponents/components/iostextinput (= 0.81.0)
    - React-FabricComponents/components/modal (= 0.81.0)
    - React-FabricComponents/components/rncore (= 0.81.0)
    - React-FabricComponents/components/safeareaview (= 0.81.0)
    - React-FabricComponents/components/scrollview (= 0.81.0)
    - React-FabricComponents/components/text (= 0.81.0)
    - React-FabricComponents/components/textinput (= 0.81.0)
    - React-FabricComponents/components/unimplementedview (= 0.81.0)
    - React-FabricComponents/components/virtualview (= 0.81.0)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/iostextinput (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/modal (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/rncore (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/safeareaview (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/scrollview (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/text (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/textinput (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/virtualview (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricImage (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired (= 0.81.0)
    - RCTTypeSafety (= 0.81.0)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.81.0)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - SocketRocket
    - Yoga
  - React-featureflags (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-featureflagsnativemodule (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-graphics (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-utils
    - SocketRocket
  - React-hermes (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.81.0)
    - React-jsi
    - React-jsiexecutor (= 0.81.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.0)
    - React-runtimeexecutor
    - SocketRocket
  - React-idlecallbacksnativemodule (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-ImageManager (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
    - SocketRocket
  - React-jserrorhandler (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
    - SocketRocket
  - React-jsi (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsiexecutor (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.81.0)
    - React-jsi (= 0.81.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.0)
    - React-runtimeexecutor
    - SocketRocket
  - React-jsinspector (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-perflogger (= 0.81.0)
    - React-runtimeexecutor
    - SocketRocket
  - React-jsinspectorcdp (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsinspectornetwork (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsinspectorcdp
    - React-performancetimeline
    - React-timing
    - SocketRocket
  - React-jsinspectortracing (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-oscompat
    - React-timing
    - SocketRocket
  - React-jsitooling (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.81.0)
    - React-jsi (= 0.81.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-runtimeexecutor
    - SocketRocket
  - React-jsitracing (0.81.0):
    - React-jsi
  - React-logger (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-Mapbuffer (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-microtasksnativemodule (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - react-native-blob-util (0.22.2):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-document-picker (9.3.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-html-to-pdf (0.12.0):
    - React-Core
  - react-native-safe-area-context (5.6.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.6.1)
    - react-native-safe-area-context/fabric (= 5.6.1)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/common (5.6.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/fabric (5.6.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-NativeModulesApple (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-featureflags
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-oscompat (0.81.0)
  - React-perflogger (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-performancetimeline (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
    - SocketRocket
  - React-RCTActionSheet (0.81.0):
    - React-Core/RCTActionSheetHeaders (= 0.81.0)
  - React-RCTAnimation (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-featureflags
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTAppDelegate (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon
    - SocketRocket
  - React-RCTBlob (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTFabric (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-RCTFBReactNativeSpec (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec/components (= 0.81.0)
    - ReactCommon
    - SocketRocket
  - React-RCTFBReactNativeSpec/components (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - SocketRocket
    - Yoga
  - React-RCTImage (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTLinking (0.81.0):
    - React-Core/RCTLinkingHeaders (= 0.81.0)
    - React-jsi (= 0.81.0)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.81.0)
  - React-RCTNetwork (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTRuntime (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - SocketRocket
  - React-RCTSettings (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTText (0.81.0):
    - React-Core/RCTTextHeaders (= 0.81.0)
    - Yoga
  - React-RCTVibration (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-rendererconsistency (0.81.0)
  - React-renderercss (0.81.0):
    - React-debug
    - React-utils
  - React-rendererdebug (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-RuntimeApple (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-RuntimeCore (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-runtimeexecutor (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - React-featureflags
    - React-jsi (= 0.81.0)
    - React-utils
    - SocketRocket
  - React-RuntimeHermes (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-utils
    - SocketRocket
  - React-runtimescheduler (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
    - SocketRocket
  - React-timing (0.81.0)
  - React-utils (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - React-jsi (= 0.81.0)
    - SocketRocket
  - ReactAppDependencyProvider (0.81.0):
    - ReactCodegen
  - ReactCodegen (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - ReactCommon (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - ReactCommon/turbomodule (= 0.81.0)
    - SocketRocket
  - ReactCommon/turbomodule (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.81.0)
    - React-cxxreact (= 0.81.0)
    - React-jsi (= 0.81.0)
    - React-logger (= 0.81.0)
    - React-perflogger (= 0.81.0)
    - ReactCommon/turbomodule/bridging (= 0.81.0)
    - ReactCommon/turbomodule/core (= 0.81.0)
    - SocketRocket
  - ReactCommon/turbomodule/bridging (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.81.0)
    - React-cxxreact (= 0.81.0)
    - React-jsi (= 0.81.0)
    - React-logger (= 0.81.0)
    - React-perflogger (= 0.81.0)
    - SocketRocket
  - ReactCommon/turbomodule/core (0.81.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.81.0)
    - React-cxxreact (= 0.81.0)
    - React-debug (= 0.81.0)
    - React-featureflags (= 0.81.0)
    - React-jsi (= 0.81.0)
    - React-logger (= 0.81.0)
    - React-perflogger (= 0.81.0)
    - React-utils (= 0.81.0)
    - SocketRocket
  - RevenueCat (5.36.0)
  - RNAppleAuthentication (2.4.1):
    - React-Core
  - RNCAsyncStorage (2.2.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNDateTimePicker (8.4.4):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNFS (2.20.0):
    - React-Core
  - RNGoogleSignin (15.0.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - GoogleSignIn (~> 8.0)
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNPurchases (9.3.0):
    - PurchasesHybridCommon (= 17.3.0)
    - React-Core
  - RNScreens (4.15.4):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.15.4)
    - SocketRocket
    - Yoga
  - RNScreens/common (4.15.4):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNSVG (15.12.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNSVG/common (= 15.12.1)
    - SocketRocket
    - Yoga
  - RNSVG/common (15.12.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNVectorIcons (10.3.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleSignIn (~> 8.0)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectorcdp (from `../node_modules/react-native/ReactCommon/jsinspector-modern/cdp`)
  - React-jsinspectornetwork (from `../node_modules/react-native/ReactCommon/jsinspector-modern/network`)
  - React-jsinspectortracing (from `../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-blob-util (from `../node_modules/react-native-blob-util`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-html-to-pdf (from `../node_modules/react-native-html-to-pdf`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNAppleAuthentication (from `../node_modules/@invertase/react-native-apple-authentication`)"
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNFS (from `../node_modules/react-native-fs`)
  - "RNGoogleSignin (from `../node_modules/@react-native-google-signin/google-signin`)"
  - RNPurchases (from `../node_modules/react-native-purchases`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - SocketRocket (~> 0.7.1)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC
    - PurchasesHybridCommon
    - RevenueCat
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-07-07-RNv0.81.0-e0fc67142ec0763c6b6153ca2bf96df815539782
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectorcdp:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/cdp"
  React-jsinspectornetwork:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/network"
  React-jsinspectortracing:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-blob-util:
    :path: "../node_modules/react-native-blob-util"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-html-to-pdf:
    :path: "../node_modules/react-native-html-to-pdf"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNAppleAuthentication:
    :path: "../node_modules/@invertase/react-native-apple-authentication"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGoogleSignin:
    :path: "../node_modules/@react-native-google-signin/google-signin"
  RNPurchases:
    :path: "../node_modules/react-native-purchases"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  fast_float: b32c788ed9c6a8c584d114d0047beda9664e7cc6
  FBLazyVector: a867936a67af0d09c37935a1b900a1a3c795b6d1
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: 5683914934d5b6e4240e497e0f4a3b42d1854183
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  hermes-engine: e7491a2038f2618c8cd444ed411a6deb350a3742
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PurchasesHybridCommon: 61759dbf7a02b085e4904d8def867d6a89252cb0
  RCT-Folly: 846fda9475e61ec7bcbf8a3fe81edfcaeb090669
  RCTDeprecation: 0735ab4f6b3ec93a7f98187b5da74d7916e2cf4c
  RCTRequired: 8fcc7801bfc433072287b0f24a662e2816e89d0c
  RCTTypeSafety: 2b2be515d6b968bcba7a68c4179d8199bd8c9b58
  React: 1000c0e96d8fb9fbdaf13f7d31d0b09db3cbb4ac
  React-callinvoker: 7e52661bfaf5d8881a9cee049792627a00001fbe
  React-Core: 949b436ddfe76cf47ac96375152de2f3506a8421
  React-CoreModules: 0f27580d0d82d430fa4f2cf4d970b6ad1120d63a
  React-cxxreact: 48754f11f47a29ea4800cbdd694c10f874a26b9b
  React-debug: 7a23d96f709f437c5e08973d6e06d0a54dd180a1
  React-defaultsnativemodule: 569d9222a701ed3dc60a60b2ce066b5bd88da059
  React-domnativemodule: 34474bda3973bfd0ca2ea9f1b3db20db5d504cc7
  React-Fabric: 45c3e9b112075451e592f0e008cabd4b82575355
  React-FabricComponents: a428f23938c27a073baacc069d484b3478df85f3
  React-FabricImage: 4375129ba8a26e8a7074af1c2468870fb8aab723
  React-featureflags: ed973a134993f3be204d0b2d385d386603c9a0af
  React-featureflagsnativemodule: aa3e1dc86bc185344d4875e7cb40cce0bd28de76
  React-graphics: b5b8709a8216075bb6a5f9e7bb68881212d924ee
  React-hermes: c543ffa2866304c582bdcb135c184e0f776f0d0b
  React-idlecallbacksnativemodule: f19c4060b12fffc3ad33ce5de190338751b462ef
  React-ImageManager: ecaf317aa5dff5eebba178b0813ef998c62547ea
  React-jserrorhandler: 92eea1ee4f8c56b466b34e0065def59805e5d3a9
  React-jsi: 7336786a4a14c473d104e6b37df935620d218fcd
  React-jsiexecutor: 7c750f5b63fbc071d0f0e56e86f1a1589914f7b1
  React-jsinspector: da5f336c1aa174a05885d061559a92e1d07b8a80
  React-jsinspectorcdp: 0e807e4c2dc8ae8a07f0a6bfe50377f442079ba3
  React-jsinspectornetwork: 3399384f2b6b70b287d8b9675452af4cec21dc65
  React-jsinspectortracing: 030af0e9dca9a4eaa1d0ba258c7bd859fb90f61d
  React-jsitooling: f8ed67814b17ebb124c48fccdf587ee1e02f16f4
  React-jsitracing: 5cf6b84d46a4653895e30956a0ce3a315244c10a
  React-logger: 04ce9229cb57db2c2a8164eaec1105f89da7fb22
  React-Mapbuffer: e402e7a0535b2213c50727553621480fe8cd8ade
  React-microtasksnativemodule: a63ce5595016996a9bac1f10c70a7a7fe6506649
  react-native-blob-util: e2162ce4757849682559754bca954b65dc7eeb2f
  react-native-document-picker: dcf5e57f3bcae61bf8384a1c6f90896f81e17941
  react-native-html-to-pdf: 7a49e6c58ac5221bcc093027b195f4b214f27a9d
  react-native-safe-area-context: c6e2edd1c1da07bdce287fa9d9e60c5f7b514616
  React-NativeModulesApple: b3766e1f87b08064ebc459b9e1538da2447ca874
  React-oscompat: 34f3d3c06cadcbc470bc4509c717fb9b919eaa8b
  React-perflogger: a1edb025fd5d44f61bf09307e248f7608d7b2dcf
  React-performancetimeline: 1f86dc9782e3fe78727c5fbb3e2178b9fd1aa6fd
  React-RCTActionSheet: 550c9c6c2e7dcd85a51954dc08e2f3837a148e7c
  React-RCTAnimation: 19d4bb6d2190983d1354b096b7b65dbd591924da
  React-RCTAppDelegate: 6c71d16eef920831a312ff363355fc3b99c02a98
  React-RCTBlob: b81a0cffe1a083bcf9d8aa9f27f4d37864579e90
  React-RCTFabric: 01005d2fa799bba6e21aae18820498f56fe0be5f
  React-RCTFBReactNativeSpec: 5adb84a81c4ed7a1f2661835d166e4b2c4320cd4
  React-RCTImage: 607e5e373fb56d72417464bd82e8046af81ab502
  React-RCTLinking: 301434c7bf1100458be5a3866326ba33491e3687
  React-RCTNetwork: a118a47bd123ac96c9877e04f5731a1d6545aba5
  React-RCTRuntime: 85fdbf469fe8a12c4db6c836731b190efc33d11d
  React-RCTSettings: 5a5aa2cf9ac40f7a8897cc0f9d945ac803886604
  React-RCTText: e6e00bee9847a8af1218079b73c8bfed16c75b8d
  React-RCTVibration: 5a05fa0ef05ee73d074a3314e57586afc969f1ba
  React-rendererconsistency: c2cb23365f4a7b511893748fe8cad1830bbae637
  React-renderercss: 0c1472d6572c05e493aee476598c3ed6234b6c33
  React-rendererdebug: d6335da9730fa5a151537aa976a16d48de6135e2
  React-RuntimeApple: 5684c2a5d8768e5728a5817c21e5dba798d54c58
  React-RuntimeCore: 52428a1b48fb3c50ddf4dd5eee494486e4ecffc6
  React-runtimeexecutor: 1b4e99e5c27d2cb8bdeca9773ff5f1a8eac7709c
  React-RuntimeHermes: a688639233a3ea44b4f8e4d448f51943d7e00815
  React-runtimescheduler: b833f0fc8c788329a497e93f55ce30508f56307a
  React-timing: 25e8229ad1cf6874e9f0711515213cb2bc322215
  React-utils: 068cec677032ba78ca0700f2dcbe6d08a0939647
  ReactAppDependencyProvider: c91900fa724baee992f01c05eeb4c9e01a807f78
  ReactCodegen: a55799cae416c387aeaae3aabc1bc0289ac19cee
  ReactCommon: 116d6ee71679243698620d8cd9a9042541e44aa6
  RevenueCat: 1b6e3f4582356a549048dac2cd21410cad67d51b
  RNAppleAuthentication: c3dddf5918126c9aae85dc2e2ce9fb87835e9e04
  RNCAsyncStorage: 29f0230e1a25f36c20b05f65e2eb8958d6526e82
  RNDateTimePicker: cda4c045beca864cebb3209ef9cc4094f974864c
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGoogleSignin: 3196b01a94355f39731ffa4575ff3e2052022196
  RNPurchases: 42a126948a0d80340a4c381ea95a691b5f587d05
  RNScreens: db22525a8ed56bb87ab038b8f03a050bf40e6ed8
  RNSVG: 6f39605a4c4d200b11435c35bd077553c6b5963a
  RNVectorIcons: 791f13226ec4a3fd13062eda9e892159f0981fae
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: 00013dd9cde63a2d98e8002fcc4f5ddb66c10782

PODFILE CHECKSUM: e472e8b9321cb67301878f0c3323cae8e26153a3

COCOAPODS: 1.16.2
