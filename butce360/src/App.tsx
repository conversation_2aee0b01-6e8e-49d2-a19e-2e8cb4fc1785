import React, { useEffect, useState } from 'react';
import { View, StatusBar, Text, StyleSheet } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useThemedColors, useThemeState } from './hooks/useThemedStyles';
import { useAuth } from './hooks/useAuth';

// Screens
import WelcomePage from './screens/Welcome/WelcomePage';
import LoginScreen from './screens/Auth/LoginScreen';
import RegisterScreen from './screens/Auth/RegisterScreen';
import HomeScreen from './screens/Home/HomeScreen';
import TransactionsScreen from './screens/Transactions/TransactionsScreen';
import BudgetScreen from './screens/Budget/BudgetScreen';
import ReportsScreen from './screens/Reports/ReportsScreen';
import ProfileScreen from './screens/Profile/ProfileScreen';
import SettingsScreen from './screens/Settings/SettingsScreen';
import AboutScreen from './screens/About/AboutScreen';
import HelpScreen from './screens/Help/HelpScreen';
import MenuStackNavigator from './navigation/MenuStack';

// RevenueCat Provider
import { RevenueCatProvider } from './providers/RevenueCatProvider';

// Stack type
export type RootStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  Home: undefined;
  Transactions: undefined;
  Budget: undefined;
  Reports: undefined;
  Profile: undefined;
  Settings: undefined;
  About: undefined;
  Help: undefined;
  Menu: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const App: React.FC = () => {
  const [isReady, setIsReady] = useState(false);
  const colors = useThemedColors();
  const { isDark } = useThemeState();
  const { state: authState } = useAuth();

  useEffect(() => {
    setIsReady(true);
  }, []);

  if (!isReady) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background.primary }]}>
        <Text style={[styles.loadingText, { color: colors.text.primary }]}>Yükleniyor...</Text>
      </View>
    );
  }

  return (
    <RevenueCatProvider>
      <NavigationContainer>
        <StatusBar
          barStyle={isDark ? 'light-content' : 'dark-content'}
          backgroundColor={colors.background.primary}
        />
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          {!authState.isAuthenticated ? (
            <>
              <Stack.Screen name="Welcome" component={WelcomePage} />
              <Stack.Screen name="Login" component={LoginScreen} />
              <Stack.Screen name="Register" component={RegisterScreen} />
            </>
          ) : (
            <>
              <Stack.Screen name="Menu" component={MenuStackNavigator} />
              <Stack.Screen name="Home" component={HomeScreen} />
              <Stack.Screen name="Transactions" component={TransactionsScreen} />
              <Stack.Screen name="Budget" component={BudgetScreen} />
              <Stack.Screen name="Reports" component={ReportsScreen} />
              <Stack.Screen name="Profile" component={ProfileScreen} />
              <Stack.Screen name="Settings" component={SettingsScreen} />
              <Stack.Screen name="About" component={AboutScreen} />
              <Stack.Screen name="Help" component={HelpScreen} />
            </>
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </RevenueCatProvider>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#000',
  },
});

export default App;
