import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { useThemedColors } from "../../hooks/useThemedStyles";
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  inputStyle?: ViewStyle;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'small' | 'medium' | 'large';
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  variant = 'default',
  size = 'medium',
  secureTextEntry,
  ...props
}) => {
  const colors = useThemedColors();
  const [isFocused, setIsFocused] = useState(false);
  const styles = createStyles(colors);
  const [isSecure, setIsSecure] = useState(secureTextEntry);

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: spacing.cardRadius,
      flexDirection: 'row',
      alignItems: 'center',
    };

    // Size styles
    switch (size) {
      case 'small':
        baseStyle.minHeight = 40;
        baseStyle.paddingHorizontal = spacing.md;
        break;
      case 'large':
        baseStyle.minHeight = 56;
        baseStyle.paddingHorizontal = spacing.lg;
        break;
      default:
        baseStyle.minHeight = 48;
        baseStyle.paddingHorizontal = spacing.inputPaddingHorizontal;
    }

    // Variant styles
    switch (variant) {
      case 'filled':
        baseStyle.backgroundColor = colors.background.secondary;
        break;
      case 'outlined':
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = error 
          ? colors.error[500] 
          : isFocused 
            ? colors.primary[500] 
            : colors.border.primary;
        baseStyle.backgroundColor = colors.surface.primary;
        break;
      default:
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = error 
          ? colors.error[500] 
          : isFocused 
            ? colors.primary[500] 
            : colors.border.primary;
        baseStyle.backgroundColor = colors.surface.primary;
    }

    return baseStyle;
  };

  const getInputStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      flex: 1,
      color: colors.text.primary,
    };

    // Size styles
    switch (size) {
      case 'small':
        baseStyle.fontSize = 14;
        break;
      case 'large':
        baseStyle.fontSize = 18;
        break;
      default:
        baseStyle.fontSize = 16;
    }

    return baseStyle;
  };

  const handleSecureToggle = () => {
    setIsSecure(!isSecure);
  };

  const renderIcon = (icon: string, onPress?: () => void) => (
    <TouchableOpacity
      onPress={onPress}
      disabled={!onPress}
      style={styles.iconContainer}
    >
      <Text style={styles.icon}>{icon}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={containerStyle}>
      {label && (
        <Text style={[
          styles.label,
          error && styles.errorLabel,
          isFocused && styles.focusedLabel
        ]}>
          {label}
        </Text>
      )}
      
      <View style={[getContainerStyle(), inputStyle]}>
        {leftIcon && renderIcon(leftIcon)}
        
        <TextInput
          {...props}
          style={[getInputStyle(), props.style]}
          secureTextEntry={isSecure}
          onFocus={(e) => {
            setIsFocused(true);
            props.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            props.onBlur?.(e);
          }}
          placeholderTextColor={colors.text.tertiary}
        />
        
        {secureTextEntry && (
          renderIcon(isSecure ? '👁️' : '🙈', handleSecureToggle)
        )}
        
        {rightIcon && !secureTextEntry && renderIcon(rightIcon, onRightIconPress)}
      </View>
      
      {(error || helperText) && (
        <Text style={[
          styles.helperText,
          error && styles.errorText
        ]}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  label: {
    ...typography.styles.inputLabel,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  errorLabel: {
    color: colors.error[500],
  },
  focusedLabel: {
    color: colors.primary[500],
  },
  container: {
    marginBottom: spacing.md,
  },
  iconContainer: {
    padding: spacing.sm,
  },
  icon: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  helperText: {
    ...typography.styles.caption,
    color: colors.text.secondary,
    marginTop: spacing.xs,
  },
  errorText: {
    ...typography.styles.caption,
    color: colors.error[500],
    marginTop: spacing.xs,
  },
});



export default Input;
