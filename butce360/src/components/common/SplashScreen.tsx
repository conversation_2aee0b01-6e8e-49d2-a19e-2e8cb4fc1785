import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  // Dimensions, // Not used
} from 'react-native';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Logo from './Logo';

// const { width, height } = Dimensions.get('window'); // Not used

interface SplashScreenProps {
  onFinish?: () => void;
  duration?: number;
}

const SplashScreen: React.FC<SplashScreenProps> = ({
  onFinish,
  duration = 2000,
}) => {
  const colors = useThemedColors();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto finish after duration
    const timer = setTimeout(() => {
      onFinish?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [fadeAnim, scaleAnim, onFinish, duration]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.primary[500],
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    logo: {
      marginBottom: spacing.xl,
    },
    title: {
      ...typography.styles.h1,
      color: colors.background.secondary,
      marginBottom: spacing.sm,
      textAlign: 'center',
    },
    subtitle: {
      ...typography.styles.body,
      color: colors.background.secondary,
      textAlign: 'center',
      opacity: 0.9,
    },
  });

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Logo size={120} variant="rounded" style={styles.logo} />
        <Text style={styles.title}>Butce360</Text>
        <Text style={styles.subtitle}>Kişisel Finans Yönetimi</Text>
      </Animated.View>
    </View>
  );
};



export default SplashScreen;
