import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { usePremiumFeature } from '../../context/PremiumContext';
import { spacing } from '../../theme/spacing';
import { typography } from '../../theme/typography';
import Button from '../common/Button';

interface PremiumGateProps {
  children: React.ReactNode;
  featureName: string;
  title?: string;
  description?: string;
  showUpgradeButton?: boolean;
}

type RootStackParamList = {
  Login: undefined;
  Paywall: undefined;
};

const PremiumGate: React.FC<PremiumGateProps> = ({
  children,
  featureName,
  title,
  description,
  showUpgradeButton = true,
}) => {
  const colors = useThemedColors();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { canAccess, accessInfo } = usePremiumFeature();

  const styles = createStyles(colors);

  // If user has access, render children
  if (canAccess) {
    return <>{children}</>;
  }

  // Determine what to show based on access info
  const getContent = () => {
    if (accessInfo.reason === 'LOGIN_REQUIRED') {
      return {
        icon: '🔐',
        title: title || 'Giriş Yapın',
        description: description || `${featureName} özelliğini kullanmak için giriş yapmanız gerekiyor.`,
        buttonText: 'Giriş Yap',
        onButtonPress: () => navigation.navigate('Login'),
      };
    }

    if (accessInfo.reason === 'PREMIUM_REQUIRED') {
      return {
        icon: '⭐',
        title: title || 'Premium Gerekli',
        description: description || `${featureName} premium bir özelliktir. Premium abonelik alarak bu özelliği kullanabilirsiniz.`,
        buttonText: 'Premium\'a Geç',
        onButtonPress: () => navigation.navigate('Paywall'),
      };
    }

    return {
      icon: '❌',
      title: 'Erişim Engellendi',
      description: 'Bu özelliğe erişim izniniz bulunmuyor.',
      buttonText: null,
      onButtonPress: null,
    };
  };

  const content = getContent();

  return (
    <View style={styles.container}>
      <View style={[styles.gateCard, { backgroundColor: colors.background.secondary }]}>
        <View style={styles.iconContainer}>
          <Text style={styles.icon}>{content.icon}</Text>
        </View>

        <Text style={[styles.title, { color: colors.text.primary }]}>
          {content.title}
        </Text>

        <Text style={[styles.description, { color: colors.text.secondary }]}>
          {content.description}
        </Text>

        {showUpgradeButton && content.buttonText && content.onButtonPress && (
          <Button
            title={content.buttonText}
            onPress={content.onButtonPress}
            variant="primary"
            style={styles.actionButton}
          />
        )}

        {accessInfo.reason === 'PREMIUM_REQUIRED' && (
          <View style={styles.featuresContainer}>
            <Text style={[styles.featuresTitle, { color: colors.text.primary }]}>
              Premium Özellikleri:
            </Text>
            <View style={styles.featuresList}>
              <Text style={[styles.featureItem, { color: colors.text.secondary }]}>
                ✨ Sınırsız yatırım simülasyonu
              </Text>
              <Text style={[styles.featureItem, { color: colors.text.secondary }]}>
                📊 Banka ekstresi okuma
              </Text>
              <Text style={[styles.featureItem, { color: colors.text.secondary }]}>
                📈 Gelişmiş raporlar
              </Text>
              <Text style={[styles.featureItem, { color: colors.text.secondary }]}>
                🔄 Otomatik yedekleme
              </Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.screenPadding,
  },
  gateCard: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    padding: spacing['3xl'],
    borderRadius: spacing.cardRadius,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    marginBottom: spacing.xl,
  },
  icon: {
    fontSize: 64,
  },
  title: {
    ...typography.styles.h3,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  description: {
    ...typography.styles.body,
    textAlign: 'center',
    marginBottom: spacing['2xl'],
    lineHeight: 24,
  },
  actionButton: {
    marginBottom: spacing.xl,
    minWidth: 200,
  },
  featuresContainer: {
    width: '100%',
    marginTop: spacing.lg,
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border.primary,
  },
  featuresTitle: {
    ...typography.styles.h6,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  featuresList: {
    alignItems: 'flex-start',
  },
  featureItem: {
    ...typography.styles.body2,
    marginBottom: spacing.sm,
    textAlign: 'left',
  },
});

export default PremiumGate;
