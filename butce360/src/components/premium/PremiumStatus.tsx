import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { usePremium } from '../../context/PremiumContext';
import { useAuth } from '../../hooks/useAuth';
import { spacing } from '../../theme/spacing';
import { typography } from '../../theme/typography';
import Card from '../common/Card';

interface PremiumStatusProps {
  showUpgradeButton?: boolean;
  compact?: boolean;
}

type RootStackParamList = {
  Paywall: undefined;
};

const PremiumStatus: React.FC<PremiumStatusProps> = ({
  showUpgradeButton = true,
  compact = false,
}) => {
  const colors = useThemedColors();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { state: authState } = useAuth();
  const { state: premiumState } = usePremium();

  const styles = createStyles(colors);

  // Don't show for guest users
  if (authState.isGuest) {
    return null;
  }

  const formatRemainingTime = () => {
    if (!premiumState.isPremium || !premiumState.expiresAt) {
      return null;
    }

    const days = premiumState.remainingDays;
    if (days > 30) {
      const months = Math.floor(days / 30);
      return `${months} ay`;
    } else if (days > 0) {
      return `${days} gün`;
    } else {
      return 'Bugün sona eriyor';
    }
  };

  const getStatusColor = () => {
    if (!premiumState.isPremium) {
      return colors.text.secondary;
    }
    
    if (premiumState.remainingDays <= 7) {
      return colors.warning[500];
    } else if (premiumState.remainingDays <= 30) {
      return colors.accent[500];
    } else {
      return colors.success[500];
    }
  };

  const getStatusIcon = () => {
    if (!premiumState.isPremium) {
      return '⭐';
    }
    
    if (premiumState.remainingDays <= 7) {
      return '⚠️';
    } else {
      return '✅';
    }
  };

  const getStatusText = () => {
    if (!premiumState.isPremium) {
      return 'Premium Değil';
    }
    
    const remainingTime = formatRemainingTime();
    if (remainingTime) {
      return `Premium Aktif (${remainingTime} kaldı)`;
    }
    
    return 'Premium Aktif';
  };

  if (compact) {
    return (
      <TouchableOpacity
        style={styles.compactContainer}
        onPress={() => navigation.navigate('Paywall')}
        activeOpacity={0.7}
      >
        <Text style={styles.compactIcon}>{getStatusIcon()}</Text>
        <Text style={[styles.compactText, { color: getStatusColor() }]}>
          {premiumState.isPremium ? 'Premium' : 'Premium\'a Geç'}
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <Card style={styles.statusCard}>
      <View style={styles.statusHeader}>
        <Text style={styles.statusIcon}>{getStatusIcon()}</Text>
        <View style={styles.statusInfo}>
          <Text style={[styles.statusTitle, { color: colors.text.primary }]}>
            Abonelik Durumu
          </Text>
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>
      </View>

      {premiumState.isPremium && premiumState.expiresAt && (
        <View style={styles.expirationInfo}>
          <Text style={[styles.expirationLabel, { color: colors.text.secondary }]}>
            Bitiş Tarihi:
          </Text>
          <Text style={[styles.expirationDate, { color: colors.text.primary }]}>
            {new Date(premiumState.expiresAt).toLocaleDateString('tr-TR')}
          </Text>
        </View>
      )}

      {!premiumState.isPremium && showUpgradeButton && (
        <TouchableOpacity
          style={[styles.upgradeButton, { backgroundColor: colors.primary[500] }]}
          onPress={() => navigation.navigate('Paywall')}
          activeOpacity={0.8}
        >
          <Text style={[styles.upgradeButtonText, { color: colors.surface.primary }]}>
            Premium'a Geç
          </Text>
        </TouchableOpacity>
      )}

      {premiumState.isPremium && premiumState.remainingDays <= 30 && (
        <TouchableOpacity
          style={[styles.renewButton, { backgroundColor: colors.accent[500] }]}
          onPress={() => navigation.navigate('Paywall')}
          activeOpacity={0.8}
        >
          <Text style={[styles.renewButtonText, { color: colors.surface.primary }]}>
            Aboneliği Yenile
          </Text>
        </TouchableOpacity>
      )}
    </Card>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  // Compact version
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: spacing.cardRadius,
    backgroundColor: colors.surface.primary,
  },
  compactIcon: {
    fontSize: 16,
    marginRight: spacing.sm,
  },
  compactText: {
    ...typography.styles.caption,
    fontWeight: '600',
  },

  // Full version
  statusCard: {
    padding: spacing.lg,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statusIcon: {
    fontSize: 24,
    marginRight: spacing.md,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    ...typography.styles.h6,
    marginBottom: spacing.xs,
  },
  statusText: {
    ...typography.styles.body2,
    fontWeight: '600',
  },
  expirationInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border.primary,
    marginBottom: spacing.md,
  },
  expirationLabel: {
    ...typography.styles.body2,
  },
  expirationDate: {
    ...typography.styles.body2,
    fontWeight: '600',
  },
  upgradeButton: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    marginTop: spacing.md,
  },
  upgradeButtonText: {
    ...typography.styles.button,
  },
  renewButton: {
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.xl,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    marginTop: spacing.md,
  },
  renewButtonText: {
    ...typography.styles.button,
  },
});

export default PremiumStatus;
