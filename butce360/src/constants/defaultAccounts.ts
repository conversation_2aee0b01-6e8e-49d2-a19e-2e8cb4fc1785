import { Account } from '../types/models';

interface DefaultAccount extends Account {
  isActive: boolean;
}

// Backend ile eşleşen varsayılan hesaplar (auth service InitializeUserDefaults'tan)
export const DEFAULT_ACCOUNTS: DefaultAccount[] = [
  {
    id: 'account-cash',
    name: 'Naki<PERSON>',
    type: 'cash',
    balance: 0,
    currency: 'TRY',
    icon: 'cash-outline',
    color: '#10B981',
    isDefault: true,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'account-bank',
    name: 'Banka Hesabı',
    type: 'bank',
    balance: 0,
    currency: 'TRY',
    icon: 'card-outline',
    color: '#3B82F6',
    isDefault: true,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'account-credit',
    name: '<PERSON><PERSON><PERSON>',
    type: 'credit',
    balance: 0,
    currency: 'TRY',
    icon: 'card',
    color: '#EF4444',
    isDefault: true,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'account-checking',
    name: '<PERSON><PERSON><PERSON>',
    type: 'bank',
    balance: 0,
    currency: 'TRY',
    icon: 'wallet-outline',
    color: '#F59E0B',
    isDefault: true,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'account-savings',
    name: 'Vadeli Hesap',
    type: 'savings',
    balance: 0,
    currency: 'TRY',
    icon: 'trending-up-outline',
    color: '#8B5CF6',
    isDefault: true,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export const getDefaultAccountsByType = (type: Account['type']): DefaultAccount[] => {
  return DEFAULT_ACCOUNTS.filter(account => account.type === type);
};

export const getDefaultAccountById = (id: string): DefaultAccount | undefined => {
  return DEFAULT_ACCOUNTS.find(account => account.id === id);
};

export const getActiveDefaultAccounts = (): Account[] => {
  return DEFAULT_ACCOUNTS.filter(account => account.isActive);
};
