import { Category } from '../types/models';

// Backend ile eşleşen var<PERSON><PERSON><PERSON> ka<PERSON> (auth service InitializeUserDefaults'tan)
export const DEFAULT_CATEGORIES: Category[] = [
  // <PERSON><PERSON><PERSON> (backend auth service ile eşleşen)
  {
    id: 'income-salary',
    name: '<PERSON><PERSON><PERSON>',
    type: 'income',
    icon: '💰',
    color: '#10B981',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-freelance',
    name: 'Freelance',
    type: 'income',
    icon: '💻',
    color: '#059669',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-investment',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    type: 'income',
    icon: '📈',
    color: '#047857',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-gift',
    name: '<PERSON><PERSON><PERSON>',
    type: 'income',
    icon: '🎁',
    color: '#065F46',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-bonus',
    name: 'Bonus',
    type: 'income',
    icon: '🎯',
    color: '#064E3B',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-rent',
    name: 'Kira Geliri',
    type: 'income',
    icon: '🏠',
    color: '#10B981',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-interest',
    name: 'Faiz',
    type: 'income',
    icon: '🏦',
    color: '#059669',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-sale',
    name: 'Satış',
    type: 'income',
    icon: '💼',
    color: '#047857',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'income-other',
    name: 'Diğer',
    type: 'income',
    icon: '📝',
    color: '#065F46',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },

  // Gider Kategorileri (backend auth service ile eşleşen)
  {
    id: 'expense-market',
    name: 'Market',
    type: 'expense',
    icon: '🛒',
    color: '#EF4444',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-restaurant',
    name: 'Restoran',
    type: 'expense',
    icon: '🍽️',
    color: '#DC2626',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-transport',
    name: 'Ulaşım',
    type: 'expense',
    icon: '🚗',
    color: '#B91C1C',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-rent',
    name: 'Kira',
    type: 'expense',
    icon: '🏠',
    color: '#991B1B',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-bills',
    name: 'Faturalar',
    type: 'expense',
    icon: '📄',
    color: '#7F1D1D',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-entertainment',
    name: 'Eğlence',
    type: 'expense',
    icon: '🎬',
    color: '#F97316',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-health',
    name: 'Sağlık',
    type: 'expense',
    icon: '🏥',
    color: '#EA580C',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-shopping',
    name: 'Alışveriş',
    type: 'expense',
    icon: '🛍️',
    color: '#C2410C',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-fuel',
    name: 'Yakıt',
    type: 'expense',
    icon: '⛽',
    color: '#9A3412',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-phone',
    name: 'Telefon',
    type: 'expense',
    icon: '📱',
    color: '#7C2D12',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-internet',
    name: 'İnternet',
    type: 'expense',
    icon: '🌐',
    color: '#EF4444',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'expense-other',
    name: 'Diğer',
    type: 'expense',
    icon: '📝',
    color: '#DC2626',
    isDefault: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export const getDefaultCategoriesByType = (type: 'income' | 'expense'): Category[] => {
  return DEFAULT_CATEGORIES.filter(category => category.type === type);
};

export const getDefaultCategoryById = (id: string): Category | undefined => {
  return DEFAULT_CATEGORIES.find(category => category.id === id);
};

export const getActiveDefaultCategories = (): Category[] => {
  return DEFAULT_CATEGORIES.filter(category => category.isDefault);
};
