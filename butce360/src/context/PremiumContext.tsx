import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useRevenueCat } from '../providers/RevenueCatProvider';
import { subscriptionService } from '../services/subscriptionService';

// Premium state interface
interface PremiumState {
  isPremium: boolean;
  isLoading: boolean;
  plan: string;
  expiresAt: Date | null;
  remainingDays: number;
  error: string | null;
}

// Premium context interface
interface PremiumContextType {
  state: PremiumState;
  checkPremiumStatus: () => Promise<void>;
  syncWithRevenueCat: () => Promise<void>;
  canAccessPremiumFeature: () => boolean;
  requiresPremium: () => { hasAccess: boolean; reason?: string };
}

// Initial state
const initialState: PremiumState = {
  isPremium: false,
  isLoading: false,
  plan: 'free',
  expiresAt: null,
  remainingDays: 0,
  error: null,
};

// Create context
const PremiumContext = createContext<PremiumContextType | undefined>(undefined);

// Premium provider props
interface PremiumProviderProps {
  children: React.ReactNode;
}

export const PremiumProvider: React.FC<PremiumProviderProps> = ({ children }) => {
  const [state, setState] = useState<PremiumState>(initialState);
  const { state: authState } = useAuth();
  const { customerInfo } = useRevenueCat();

  // Check premium status from backend
  const checkPremiumStatus = useCallback(async () => {
    if (!authState.isAuthenticated || authState.isGuest) {
      setState(prev => ({
        ...prev,
        isPremium: false,
        plan: authState.isGuest ? 'guest' : 'free',
        expiresAt: null,
        remainingDays: 0,
        error: null,
      }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const status = await subscriptionService.getSubscriptionStatus();

      // Ensure status is valid before using it
      if (status && typeof status === 'object') {
        setState(prev => ({
          ...prev,
          isPremium: Boolean(status.isPremium),
          plan: status.plan || 'free',
          expiresAt: status.expiresAt ? new Date(status.expiresAt) : null,
          remainingDays: status.remainingDays || 0,
          isLoading: false,
          error: null,
        }));
      } else {
        // If status is invalid, set default values
        setState(prev => ({
          ...prev,
          isPremium: false,
          plan: 'free',
          expiresAt: null,
          remainingDays: 0,
          isLoading: false,
          error: null,
        }));
      }
    } catch (error: any) {
      console.error('[Premium] Failed to check premium status:', error);
      // Set default free status instead of error state
      setState(prev => ({
        ...prev,
        isPremium: false,
        plan: 'free',
        expiresAt: null,
        remainingDays: 0,
        isLoading: false,
        error: null, // Don't show error to user, just use default values
      }));
    }
  }, [authState.isAuthenticated, authState.isGuest]);

  // Sync with RevenueCat customer info
  const syncWithRevenueCat = useCallback(async () => {
    if (!authState.isAuthenticated || authState.isGuest || !customerInfo) {
      return;
    }

    try {
      // Check if user has active premium entitlement in RevenueCat
      const hasRevenueCatPremium = !!customerInfo?.entitlements?.active?.premium;
      
      if (hasRevenueCatPremium && !state.isPremium) {
        // User has premium in RevenueCat but not in backend, sync it
        console.log('[Premium] Syncing RevenueCat premium status with backend');
        await subscriptionService.notifyPurchase({
          customerInfo,
          productId: 'premium', // This will be updated based on actual product
          originalTransactionId: customerInfo.originalAppUserId || '',
          transactionId: '',
          purchaseDate: new Date(),
          expirationDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        });
        
        // Refresh status after sync
        await checkPremiumStatus();
      }
    } catch (error) {
      console.error('[Premium] Failed to sync with RevenueCat:', error);
    }
  }, [authState.isAuthenticated, authState.isGuest, customerInfo, state.isPremium, checkPremiumStatus]);

  // Check if user can access premium features
  const canAccessPremiumFeature = useCallback(() => {
    return authState.isAuthenticated && !authState.isGuest && state.isPremium;
  }, [authState.isAuthenticated, authState.isGuest, state.isPremium]);

  // Get detailed access information
  const requiresPremium = useCallback(() => {
    if (!authState.isAuthenticated) {
      return { hasAccess: false, reason: 'LOGIN_REQUIRED' };
    }
    
    if (authState.isGuest) {
      return { hasAccess: false, reason: 'LOGIN_REQUIRED' };
    }
    
    if (!state.isPremium) {
      return { hasAccess: false, reason: 'PREMIUM_REQUIRED' };
    }
    
    return { hasAccess: true };
  }, [authState.isAuthenticated, authState.isGuest, state.isPremium]);

  // Check premium status when auth state changes
  useEffect(() => {
    checkPremiumStatus();
  }, [checkPremiumStatus]);

  // Sync with RevenueCat when customer info changes
  useEffect(() => {
    if (customerInfo) {
      syncWithRevenueCat();
    }
  }, [customerInfo, syncWithRevenueCat]);

  const contextValue: PremiumContextType = {
    state,
    checkPremiumStatus,
    syncWithRevenueCat,
    canAccessPremiumFeature,
    requiresPremium,
  };

  return (
    <PremiumContext.Provider value={contextValue}>
      {children}
    </PremiumContext.Provider>
  );
};

// Custom hook to use premium context
export const usePremium = (): PremiumContextType => {
  const context = useContext(PremiumContext);
  if (!context) {
    throw new Error('usePremium must be used within PremiumProvider');
  }
  return context;
};

// Helper hook for premium feature access
export const usePremiumFeature = () => {
  const { canAccessPremiumFeature, requiresPremium } = usePremium();
  
  return {
    canAccess: canAccessPremiumFeature(),
    accessInfo: requiresPremium(),
  };
};
