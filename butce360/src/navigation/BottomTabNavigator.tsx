import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../hooks/useThemedStyles';

export const TAB_BAR_MIN_HEIGHT = 80;

interface TabBarProps {
  activeTab: string;
  onTabPress: (tab: string) => void;
}

const TabBar: React.FC<TabBarProps> = ({ activeTab, onTabPress }) => {
  const colors = useThemedColors();
  const insets = useSafeAreaInsets();

  const tabs = [
    { id: 'home', icon: 'home-outline', label: '' },
    { id: 'budget', icon: 'wallet-outline', label: '' },
    { id: 'add', icon: 'add-circle', label: '' },
    { id: 'investment', icon: 'trending-up-outline', label: '' },
    { id: 'menu', icon: 'menu-outline', label: '' },
  ];

  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      paddingTop: 12,
      paddingHorizontal: 16,
      borderTopWidth: 0.33,
      elevation: 8,
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      borderRadius: 24,
      minHeight: TAB_BAR_MIN_HEIGHT, // Sabit minimum yükseklik
    },
    tab: {
      flex: 1,
      alignItems: 'center',
      paddingVertical: 8,
      justifyContent: 'center',
    },
    iconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addButton: {
      width: 56,
      height: 56,
      borderRadius: 28,
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: -8,
      borderWidth: 3,
      elevation: 4,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
  });

  return (
    <View 
      style={[
        styles.container, 
        { 
          backgroundColor: colors.background.secondary,
          paddingBottom: Math.max(insets.bottom, 12),
          borderTopColor: colors.border.primary,
        }
      ]}
    >
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        const isAdd = tab.id === 'add';

        return (
          <TouchableOpacity
            key={tab.id}
            style={styles.tab}
            onPress={() => onTabPress(tab.id)}
            activeOpacity={0.7}
          >
            <View style={[
              isAdd ? styles.addButton : styles.iconContainer,
              isAdd && {
                backgroundColor: colors.primary[500],
                borderColor: colors.primary[600],
              },

              isActive && !isAdd && {
                backgroundColor: colors.background.primary === '#1c1c1e'
                  ? colors.neutral[800]  // Dark theme - dark background
                  : colors.neutral[200]  // Light theme - light background
              }
            ]}>
              <Ionicons
                name={tab.icon}
                size={isAdd ? 32 : 24}
                color={
                  isAdd
                    ? colors.background.secondary // add button always white
                    : isActive
                      ? colors.text.primary // active tab -> tema text rengi (beyaz koyu temada)
                      : colors.text.secondary       // inactive tab -> gray
                }
              />
            </View>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};



export default TabBar;
