import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import NavigationHeader from '../components/common/NavigationHeader';
import BudgetScreen from '../screens/Budget/BudgetScreen';
import AddBudgetScreen from '../screens/Budget/AddBudgetScreen';

type BudgetStackParamList = {
  BudgetHome: undefined;
  AddBudget: undefined;
};

const Stack = createNativeStackNavigator<BudgetStackParamList>();

// Legacy adapter for onNavigate prop
const legacyNavigate = (navigation: any, screen: string) => {
  try {
    if (!screen) return;

    // Handle budget navigation - go to BudgetHome
    if (screen === 'budget') {
      return navigation.navigate('BudgetHome');
    }

    const qIndex = screen.indexOf('?');
    if (qIndex === -1) {
      const target = screen === 'addBudget' ? 'AddBudget' : screen;
      return navigation.navigate(target);
    }
    const base = screen.substring(0, qIndex);
    const query = screen.substring(qIndex + 1);
    const params: Record<string, any> = {};
    query.split('&').forEach(pair => {
      const [k, v] = pair.split('=');
      if (k) params[k] = decodeURIComponent(v || '');
    });
    const target = base === 'addBudget' ? 'AddBudget' : base;
    return navigation.navigate(target, params);
  } catch {}
};

const BudgetHomeWrapper: React.FC<any> = (props) => (
  <BudgetScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const AddBudgetWrapper: React.FC<any> = (props) => (
  <AddBudgetScreen route={props.route} onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);

interface BudgetStackHeaderProps {
  navigation: any;
  back?: any;
  route: any;
}

const BudgetStackHeader = ({ navigation, back, route }: BudgetStackHeaderProps) => {
  const isEdit = route.name === 'AddBudget' && (route as any).params?.budgetId;
  const title = route.name === 'BudgetHome' ? 'Bütçe' : (isEdit ? 'Bütçe Düzenle' : 'Yeni Bütçe');
  const right = route.name === 'BudgetHome'
    ? { rightButtonText: 'Ekle', onRightButtonPress: () => navigation.navigate('AddBudget') }
    : {};
  
  return (
    <NavigationHeader
      title={title}
      showBackButton={!!back}
      onBackPress={navigation.goBack}
      {...right}
    />
  );
};

const BudgetStackNavigator: React.FC = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: true,
      header: BudgetStackHeader
    }}
  >
    <Stack.Screen name="BudgetHome" component={BudgetHomeWrapper} />
    <Stack.Screen name="AddBudget" component={AddBudgetWrapper} />
  </Stack.Navigator>
);

export default BudgetStackNavigator;
