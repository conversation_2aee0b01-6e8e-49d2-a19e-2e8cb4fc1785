import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import NavigationHeader from '../components/common/NavigationHeader';

// Screens
import MenuScreen from '../screens/Menu/MenuScreen';
import BudgetScreen from '../screens/Budget/BudgetScreen';
import AddBudgetScreen from '../screens/Budget/AddBudgetScreen';
import CategoriesScreen from '../screens/Categories/CategoriesScreen';
import CategoryDetailScreen from '../screens/Categories/CategoryDetailScreen';
import AddCategoryScreen from '../screens/Categories/AddCategoryScreen';
import AccountsScreen from '../screens/Accounts/AccountsScreen';
import AddAccountScreen from '../screens/Accounts/AddAccountScreen';
import AccountDetailScreen from '../screens/Accounts/AccountDetailScreen';
import ReportsScreen from '../screens/Reports/ReportsScreen';
import SettingsScreen from '../screens/Settings/SettingsScreen';
import ProfileScreen from '../screens/Profile/ProfileScreen';
import ProfileDetailsScreen from '../screens/Profile/ProfileDetailsScreen';
import EditProfileScreen from '../screens/Profile/EditProfileScreen';
import ChangePasswordScreen from '../screens/Profile/ChangePasswordScreen';
import HelpScreen from '../screens/Help/HelpScreen';
import AboutScreen from '../screens/About/AboutScreen';
import BankStatementScreen from '../screens/BankStatement/BankStatementScreen';
import ExportScreen from '../screens/Export/ExportScreen';
import PaywallScreen from '../screens/Paywall/PaywallScreen';

type MenuStackParamList = {
  MenuHome: undefined;
  Paywall: undefined;
  budget: undefined;
  addBudget: undefined;
  categories: undefined;
  CategoryDetail: { id?: string; name?: string } | undefined;
  AddCategory: undefined;
  accounts: undefined;
  AddAccount: undefined;
  AccountDetails: undefined;
  reports: undefined;
  settings: undefined;
  profile: undefined;
  ProfileDetails: undefined;
  EditProfile: undefined;
  changePassword: undefined;
  help: undefined;
  about: undefined;
  bankStatement: undefined;
  export: undefined;
};

const Stack = createNativeStackNavigator<MenuStackParamList>();

// Adapter for legacy onNavigate string API
const legacyNavigate = (navigation: any, screen: string) => {
  try {
    if (!screen) return;
    const qIndex = screen.indexOf('?');
    if (qIndex === -1) {
      if (screen === 'MainTabs') return navigation.navigate('MenuHome');
      if (screen === 'Welcome') return navigation.navigate('login');
      return navigation.navigate(screen);
    }
    const base = screen.substring(0, qIndex);
    const query = screen.substring(qIndex + 1);
    const params: Record<string, any> = {};
    query.split('&').forEach(pair => {
      const [k, v] = pair.split('=');
      if (k) params[k] = decodeURIComponent(v || '');
    });
    return navigation.navigate(base, params);
  } catch {}
};

// Wrapper components to avoid inline render functions (ESLint: no-unstable-nested-components)
const BudgetScreenWrapper: React.FC<any> = (props) => (
  <BudgetScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const AddBudgetScreenWrapper: React.FC<any> = (props) => (
  <AddBudgetScreen route={props.route} onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const CategoriesScreenWrapper: React.FC<any> = (props) => (
  <CategoriesScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const CategoryDetailScreenWrapper: React.FC<any> = (props) => (
  <CategoryDetailScreen 
    categoryId={(props.route?.params as any)?.id}
    categoryName={(props.route?.params as any)?.name}
    onNavigate={(s) => legacyNavigate(props.navigation, s)}
    onGoBack={props.navigation.goBack}
  />
);
const AddCategoryScreenWrapper: React.FC<any> = (props) => (
  <AddCategoryScreen route={props.route} onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const AccountsScreenWrapper: React.FC<any> = (props) => (
  <AccountsScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const AccountDetailScreenWrapper: React.FC<any> = (props) => (
  <AccountDetailScreen accountId={(props.route?.params as any)?.id} onNavigate={(s) => legacyNavigate(props.navigation, s)} onGoBack={props.navigation.goBack} />
);
const AddAccountScreenWrapper: React.FC<any> = (props) => (
  <AddAccountScreen route={props.route} onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const SettingsScreenWrapper: React.FC<any> = (props) => (
  <SettingsScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const ProfileScreenWrapper: React.FC<any> = (props) => (
  <ProfileScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const ProfileDetailsScreenWrapper: React.FC<any> = (props) => (
  <ProfileDetailsScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const EditProfileScreenWrapper: React.FC<any> = (props) => (
  <EditProfileScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const BankStatementScreenWrapper: React.FC<any> = (props) => (
  <BankStatementScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);
const ExportScreenWrapper: React.FC<any> = (props) => (
  <ExportScreen onNavigate={(s) => legacyNavigate(props.navigation, s)} />
);

interface MenuStackHeaderProps {
  navigation: any;
  back?: any;
  route: any;
}

const MenuStackHeader = ({ navigation, back, route }: MenuStackHeaderProps) => {
  // Default dynamic title mapping
  const titleMap: Record<string, string> = {
    MenuHome: 'Menü',
    budget: 'Bütçe',
    addBudget: 'Yeni Bütçe',
    categories: 'Kategoriler',
    CategoryDetail: 'Kategori Detayı',
    AddCategory: 'Kategori Ekle',
    accounts: 'Hesaplar',
    AccountDetails: 'Hesap Detayı',
    AddAccount: 'Hesap Ekle',
    reports: 'Raporlar',
    settings: 'Ayarlar',
    profile: 'Profil',
    ProfileDetails: 'Profil Bilgileri',
    EditProfile: 'Profili Düzenle',
    changePassword: 'Şifre Değiştir',
    help: 'Yardım',
    about: 'Hakkında',
    bankStatement: 'Banka Ekstresi',
    export: 'Dışa Aktar',
  };
  let title = titleMap[route.name] || 'Butce360';
  // Override titles for edit forms when id params exist
  if (route.name === 'addBudget' && (route as any).params?.budgetId) title = 'Bütçe Düzenle';
  if (route.name === 'AddCategory' && (route as any).params?.categoryId) title = 'Kategori Düzenle';
  if (route.name === 'AddAccount' && (route as any).params?.accountId) title = 'Hesap Düzenle';
  const withAddRight = route.name === 'budget' || route.name === 'categories' || route.name === 'accounts';
  const right = withAddRight ? {
    rightButtonText: 'Ekle',
    onRightButtonPress: () => {
      const map: Record<string, string> = { budget: 'addBudget', categories: 'AddCategory', accounts: 'AddAccount' };
      const dest = map[route.name];
      if (dest) navigation.navigate(dest as any);
    }
  } : {};
  return <NavigationHeader title={title} showBackButton={!!back} onBackPress={navigation.goBack} {...right} />;
};

const MenuEditProfileHeader = (props: any) => (
  <NavigationHeader 
    title="Profili Düzenle" 
    showBackButton={!!props.back} 
    onBackPress={props.navigation.goBack} 
    rightButtonText="Kaydet" 
    onRightButtonPress={() => (props.route as any).params?.onSave?.()} 
  />
);

const MenuChangePasswordHeader = (props: any) => (
  <NavigationHeader title="Şifre Değiştir" showBackButton={!!props.back} onBackPress={props.navigation.goBack} />
);

const MenuStackNavigator: React.FC = () => (
  <Stack.Navigator 
    screenOptions={{ 
      headerShown: true, 
      contentStyle: { backgroundColor: 'transparent' }, 
      header: MenuStackHeader
    }}
  >
      <Stack.Screen name="MenuHome" component={MenuScreen} />
      <Stack.Screen name="Paywall" component={PaywallScreenWrapper} />
      <Stack.Screen name="budget" component={BudgetScreenWrapper} />
      <Stack.Screen name="addBudget" component={AddBudgetScreenWrapper} />
      <Stack.Screen name="categories" component={CategoriesScreenWrapper} />
      <Stack.Screen name="CategoryDetail" component={CategoryDetailScreenWrapper} />
      <Stack.Screen name="AddCategory" component={AddCategoryScreenWrapper} />
      <Stack.Screen name="accounts" component={AccountsScreenWrapper} />
      <Stack.Screen name="AddAccount" component={AddAccountScreenWrapper} />
      <Stack.Screen name="AccountDetails" component={AccountDetailScreenWrapper} />
      <Stack.Screen name="reports" component={ReportsScreen} />
      <Stack.Screen name="settings" component={SettingsScreenWrapper} />
      <Stack.Screen name="profile" component={ProfileScreenWrapper} />
      <Stack.Screen name="ProfileDetails" component={ProfileDetailsScreenWrapper} />
      <Stack.Screen name="EditProfile" component={EditProfileScreenWrapper} options={{ header: MenuEditProfileHeader }} />
      <Stack.Screen name="changePassword" component={ChangePasswordScreen} options={{ header: MenuChangePasswordHeader }} />
      <Stack.Screen name="help" component={HelpScreen} />
      <Stack.Screen name="about" component={AboutScreen} />
      <Stack.Screen name="bankStatement" component={BankStatementScreenWrapper} />
      <Stack.Screen name="export" component={ExportScreenWrapper} />
    </Stack.Navigator>
  );

const PaywallScreenWrapper: React.FC<any> = (props) => (
  <PaywallScreen {...props} />
);

export default MenuStackNavigator;
