import React from 'react';
import type { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import TabBar from './BottomTabNavigator';

const TabBarAdapter: React.FC<BottomTabBarProps> = ({ state, navigation }) => {
  const activeTab = state.routes[state.index]?.name ?? 'home';

  const onTabPress = (tab: string) => {
    // Prevent double tap behavior on current route
    if (tab !== activeTab) {
      // @ts-ignore - route names are aligned with our tabs
      navigation.navigate(tab);
    }
  };

  return <TabBar activeTab={activeTab} onTabPress={onTabPress} />;
};

export default TabBarAdapter;

