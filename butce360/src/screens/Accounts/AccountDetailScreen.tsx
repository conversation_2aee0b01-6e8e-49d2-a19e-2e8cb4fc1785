import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, RefreshControl, StatusBar, Alert, ActivityIndicator, TouchableOpacity } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { accountService } from '../../services/accountService';
import { localTransactionService } from '../../services/localTransactionService';

interface AccountDetailScreenProps {
  onNavigate?: (screen: string) => void;
  onGoBack?: () => void;
  accountId?: string;
}

const AccountDetailScreen: React.FC<AccountDetailScreenProps> = ({ onNavigate, accountId }) => {
  const colors = useThemedColors();
  const [account, setAccount] = useState<any | null>(null);
  const [transactions, setTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = useCallback(async () => {
    if (!accountId) return;
    try {
      setLoading(true);
      const acc = await accountService.getAccount(accountId);
      setAccount(acc);

      try {
        const resp = await accountService.getAccountTransactions(accountId, 1, 100);
        setTransactions(resp.transactions || []);
      } catch (e) {
        // Guest fallback via local storage
        const local = await localTransactionService.getTransactions({ accountId });
        setTransactions(local);
      }
    } catch (error) {
      console.error('[AccountDetailScreen] fetch error:', error);
      Alert.alert('Hata', 'Hesap bilgisi yüklenemedi.');
    } finally {
      setLoading(false);
    }
  }, [accountId]);

  useEffect(() => { fetchData(); }, [fetchData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const getTypeLabel = (t: string) => {
    switch (t) {
      case 'checking': return 'Vadesiz Hesap';
      case 'savings': return 'Vadeli Hesap';
      case 'credit': return 'Kredi Kartı';
      case 'cash': return 'Nakit';
      case 'investment': return 'Yatırım';
      case 'bank': return 'Banka Hesabı';
      default: return 'Hesap';
    }
  };

  const renderTxn = (tx: any) => (
    <View key={tx.id} style={[styles.txnItem, { backgroundColor: colors.background.secondary }]}> 
      <View>
        <Text style={[styles.txnTitle, { color: colors.text.primary }]}>{tx.title || tx.description || 'İşlem'}</Text>
        <Text style={[styles.txnDate, { color: colors.text.secondary }]}>
          {new Date(tx.date || tx.createdAt).toLocaleDateString('tr-TR')}
        </Text>
      </View>
      <Text style={[styles.txnAmount, { color: (tx.type === 'income') ? colors.success[500] : colors.error[500] }]}>
        {(tx.type === 'income' ? '+' : '-')}{Math.abs(tx.amount || 0).toFixed(2)}
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <StatusBar backgroundColor={colors.background.primary} barStyle="dark-content" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>Yükleniyor…</Text>
        </View>
      </View>
    );
  }

  if (!account) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <Text style={[styles.emptyText, { color: colors.text.secondary }]}>Hesap bulunamadı</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar backgroundColor={colors.background.primary} barStyle="dark-content" />
      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[colors.primary[500]]} />}
      >
        <View style={[styles.card, { backgroundColor: colors.background.secondary }]}> 
          <View style={styles.rowBetween}>
            <View>
              <Text style={[styles.name, { color: colors.text.primary }]}>{account.name}</Text>
              <Text style={[styles.type, { color: colors.text.secondary }]}>{getTypeLabel(account.type)}</Text>
            </View>
            <Text style={[styles.balance, { color: colors.primary[600] }]}>{account.currency || 'TRY'} {Number(account.balance || 0).toFixed(2)}</Text>
          </View>
          <View style={[styles.actions, { borderTopColor: colors.border.primary }]}>
            <TouchableOpacity onPress={() => onNavigate?.(`AddAccount?accountId=${account.id}`)} style={styles.actionBtn}>
              <Ionicons name="create-outline" size={18} color={colors.text.secondary} />
              <Text style={[styles.actionText, { color: colors.text.secondary }]}>Düzenle</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>İşlemler ({transactions.length})</Text>
          {transactions.length > 0 ? transactions.map(renderTxn) : (
            <View style={styles.emptyBox}><Text style={[styles.emptyText, { color: colors.text.secondary }]}>İşlem yok</Text></View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { flex: 1, padding: 16 },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  loadingText: { marginTop: 8 },
  emptyText: { marginTop: 40, textAlign: 'center' },
  card: { padding: 16, borderRadius: 12, marginBottom: 16 },
  rowBetween: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  name: { ...typography.styles.title3, fontWeight: '600' },
  type: { ...typography.styles.subhead },
  balance: { ...typography.styles.h5, fontWeight: '700' },
  actions: { marginTop: 12, paddingTop: 12, borderTopWidth: 1, flexDirection: 'row', gap: 16 },
  actionBtn: { flexDirection: 'row', alignItems: 'center', gap: 6 },
  actionText: { ...typography.styles.caption },
  section: { marginTop: 8 },
  sectionTitle: { ...typography.styles.headline, marginBottom: 8 },
  emptyBox: { padding: 16, alignItems: 'center', borderRadius: 10, opacity: 0.8 },
  txnItem: { padding: 12, borderRadius: 10, marginBottom: 8, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  txnTitle: { ...typography.styles.body1, fontWeight: '500' },
  txnDate: { ...typography.styles.caption },
  txnAmount: { ...typography.styles.subhead, fontWeight: '700' },
});

export default AccountDetailScreen;
