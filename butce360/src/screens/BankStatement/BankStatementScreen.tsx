import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import DocumentPicker from 'react-native-document-picker';
import { useThemedColors, useThemeState } from '../../hooks/useThemedStyles';
import { authService } from '../../services/authService';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import {
  bankStatementService,
  BankStatementEntry,
} from '../../services/bankStatementService';
import { accountService } from '../../services/accountService';
import { Account } from '../../types/models';
import PremiumGate from '../../components/premium/PremiumGate';
import { usePremium } from '../../context/PremiumContext';

interface BankStatementScreenProps {
  onNavigate?: (screen: string) => void;
}

const BankStatementScreen: React.FC<BankStatementScreenProps> = ({
  onNavigate,
}) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  const { isDark } = useThemeState();

  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Premium context
  const { state: premiumState } = usePremium();

  const checkAuthStatus = useCallback(async () => {
    try {
      const token = await authService.getStoredToken();
      setIsAuthenticated(!!token);
    } catch (error) {
      console.error('Error checking auth status:', error);
      setIsAuthenticated(false);
    }
  }, []);

  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  const formatDate = (dateString: string) => {
    try {
      console.log('[BankStatementScreen] Formatting date:', dateString);

      // Handle DD.MM.YYYY format from backend
      if (dateString.includes('.')) {
        const parts = dateString.split('.');
        if (parts.length === 3) {
          const day = parts[0].padStart(2, '0');
          const month = parts[1].padStart(2, '0');
          const year = parts[2];

          // Create date in YYYY-MM-DD format for proper parsing
          const isoDate = `${year}-${month}-${day}`;
          const date = new Date(isoDate);

          if (!isNaN(date.getTime())) {
            return date.toLocaleDateString('tr-TR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            });
          }
        }
      }

      // Fallback to original parsing
      const date = new Date(dateString);
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString('tr-TR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
        });
      }

      // If all parsing fails, return original string
      return dateString;
    } catch (error) {
      console.error('[BankStatementScreen] Date formatting error:', error);
      return dateString;
    }
  };
  const [selectedFile, setSelectedFile] = useState<{
    uri: string;
    name: string;
    type: string;
    size: number;
  } | null>(null);
  const [selectedBank, setSelectedBank] = useState<
    'vakifbank' | 'enpara' | 'garanti' | 'creditcard'
  >('vakifbank');
  const [processing, setProcessing] = useState(false);
  const [extractedEntries, setExtractedEntries] = useState<
    BankStatementEntry[]
  >([]);
  const [selectedEntries, setSelectedEntries] = useState<Set<number>>(
    new Set(),
  );
  const [showPreview, setShowPreview] = useState(false);
  const [importing, setImporting] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(
    null,
  );

  // Hesapları yükle (login ise API, misafir ise local storage)
  useEffect(() => {
    (async () => {
      try {
        const accs = await accountService.getAccounts();
        setAccounts(accs);
        if (accs.length > 0) setSelectedAccountId(accs[0].id || null);
      } catch (e) {
        console.log('[BankStatementScreen] Accounts load failed', e);
      }
    })();
  }, []);

  const handleSelectPDF = async () => {
    try {
      const result = await DocumentPicker.pick({
        type: [DocumentPicker.types.pdf],
        allowMultiSelection: false,
      });

      if (result && result.length > 0) {
        const file = result[0];
        setSelectedFile({
          uri: file.uri,
          name: file.name || 'bank_statement.pdf',
          type: file.type || 'application/pdf',
          size: file.size || 0,
        });
        Alert.alert('Başarılı', 'PDF dosyası seçildi!');
      }
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        // User cancelled the picker
        return;
      }
      console.error('Error picking document:', error);
      Alert.alert('Hata', 'Dosya seçilirken bir hata oluştu.');
    }
  };

  const handleProcessPDF = async () => {
    if (!selectedFile) {
      Alert.alert('Hata', 'Lütfen önce bir PDF dosyası seçin.');
      return;
    }

    // Don't make API call if user is not premium
    if (!premiumState.isPremium) {
      console.log('[BankStatementScreen] User is not premium, skipping API call');
      Alert.alert('Premium Gerekli', 'Bu özellik premium abonelik gerektirir.');
      return;
    }

    setProcessing(true);

    try {
      // Upload PDF to backend
      const response = await bankStatementService.uploadBankStatement({
        file: selectedFile,
        bank: selectedBank,
      });

      // Check if response has entries property
      if (!response || !response.entries) {
        console.error(
          '[BankStatementScreen] Invalid response structure:',
          response,
        );
        Alert.alert('Hata', 'Sunucudan geçersiz yanıt alındı.');
        return;
      }

      setExtractedEntries(response.entries);
      // Başlangıçta tüm işlemleri seçili yap
      setSelectedEntries(new Set(response.entries.map((_, index) => index)));
      setProcessing(false);
      setShowPreview(true);

      Alert.alert(
        'İşlem Tamamlandı',
        `${response.entries.length} işlem başarıyla çıkarıldı! Lütfen eklemek istediğiniz işlemleri seçin.`,
        [{ text: 'Tamam' }],
      );
    } catch (error) {
      setProcessing(false);
      console.error('Error processing PDF:', error);
      Alert.alert(
        'Hata',
        'PDF işlenirken bir hata oluştu. Lütfen tekrar deneyin.',
      );
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setExtractedEntries([]);
    setSelectedEntries(new Set());
    setShowPreview(false);
  };

  const toggleEntrySelection = (index: number) => {
    const newSelected = new Set(selectedEntries);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedEntries(newSelected);
  };

  const selectAllEntries = () => {
    setSelectedEntries(new Set(extractedEntries.map((_, index) => index)));
  };

  const deselectAllEntries = () => {
    setSelectedEntries(new Set());
  };

  const handleImportSelected = async () => {
    if (selectedEntries.size === 0) {
      Alert.alert('Uyarı', 'Lütfen en az bir işlem seçin.');
      return;
    }

    if (!selectedAccountId) {
      Alert.alert('Uyarı', 'Lütfen işlemler için bir hesap seçin.');
      return;
    }

    // Don't make API call if user is not premium
    if (!premiumState.isPremium) {
      console.log('[BankStatementScreen] User is not premium, skipping import');
      Alert.alert('Premium Gerekli', 'Bu özellik premium abonelik gerektirir.');
      return;
    }

    setImporting(true);
    try {
      const selectedEntriesArray = Array.from(selectedEntries).map(index => {
        const entry = extractedEntries[index];
        return {
          date: entry.date,
          description: entry.description,
          amount: entry.amount,
          type: entry.type,
          categoryId: entry.categoryId || '',
          accountId: selectedAccountId,
        };
      });

      await bankStatementService.importBankStatementEntries(
        selectedEntriesArray,
      );

      Alert.alert(
        'Başarılı',
        `${selectedEntries.size} işlem başarıyla eklendi!`,
        [{ text: 'Tamam', onPress: () => onNavigate?.('home') }],
      );
    } catch (error) {
      console.error('Error importing entries:', error);
      Alert.alert('Hata', 'İşlemler eklenirken bir hata oluştu.');
    } finally {
      setImporting(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <View
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <StatusBar
          barStyle={isDark ? 'light-content' : 'dark-content'}
          backgroundColor={colors.background.primary}
        />
        <View style={styles.emptyStateContainer}>
          <Ionicons
            name="trending-up-outline"
            size={80}
            color={colors.text.secondary}
          />
          <Text
            style={[styles.emptyStateTitle, { color: colors.text.primary }]}
          >
            Giriş Yapın
          </Text>
          <Text
            style={[
              styles.emptyStateSubtitle,
              { color: colors.text.secondary },
            ]}
          >
            Yatırım simülasyonu yapmak için giriş yapmanız gerekiyor
          </Text>
          <TouchableOpacity
            style={[
              styles.loginButton,
              { backgroundColor: colors.primary[500] },
            ]}
            onPress={() => onNavigate?.('Login')}
          >
            <Text
              style={[
                styles.loginButtonText,
                { color: colors.background.secondary },
              ]}
            >
              Giriş Yap
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <PremiumGate
      featureName="Banka Ekstresi Okuma"
      title="Banka Ekstresi Okuma"
      description="Banka ekstresi okuma özelliği premium abonelik gerektirir. PDF ekstrenizi yükleyerek otomatik işlem kaydı oluşturun."
    >
      <SafeAreaView
        edges={['left', 'right']}
        style={[styles.container, { backgroundColor: colors.background.primary }]}
      >
        <StatusBar
        barStyle={
          colors.background.primary === '#1c1c1e'
            ? 'light-content'
            : 'dark-content'
        }
        backgroundColor={colors.background.primary}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.section}>
          <View style={styles.infoCard}>
            <View style={styles.infoIconContainer}>
              <Ionicons
                name="document-text-outline"
                size={32}
                color={colors.primary[500]}
              />
            </View>
            <Text style={styles.infoTitle}>PDF Banka Ekstresi İçe Aktarma</Text>
            <Text style={styles.infoText}>
              Banka ekstrenizi PDF formatında yükleyerek işlemlerinizi otomatik
              olarak içe aktarabilirsiniz.
            </Text>
          </View>
        </View>

        {/* File Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>PDF Dosyası</Text>

          {!selectedFile ? (
            <TouchableOpacity
              style={styles.selectButton}
              onPress={handleSelectPDF}
            >
              <Ionicons
                name="cloud-upload-outline"
                size={32}
                color={colors.primary[500]}
              />
              <Text style={styles.selectButtonText}>PDF Dosyası Seç</Text>
              <Text style={styles.selectButtonSubtext}>
                Banka ekstrenizi PDF formatında seçin
              </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.selectedFile}>
              <View style={styles.fileInfo}>
                <Ionicons
                  name="document-outline"
                  size={24}
                  color={colors.primary[500]}
                />
                <View style={styles.fileDetails}>
                  <Text style={styles.fileName}>{selectedFile.name}</Text>
                  <Text style={styles.fileSize}>
                    {(selectedFile.size / (1024 * 1024)).toFixed(1)} MB • PDF
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={handleRemoveFile}
              >
                <Ionicons
                  name="close-circle-outline"
                  size={24}
                  color={colors.accent[500]}
                />
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Bank Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Banka Seçimi</Text>
          <View style={styles.bankList}>
            {bankStatementService
              .getSupportedBanks()
              .map((bank: { key: string; name: string }) => (
                <TouchableOpacity
                  key={bank.key}
                  style={[
                    styles.bankItem,
                    selectedBank === bank.key && styles.bankItemSelected,
                  ]}
                  onPress={() =>
                    setSelectedBank(
                      bank.key as
                        | 'vakifbank'
                        | 'enpara'
                        | 'garanti'
                        | 'creditcard',
                    )
                  }
                >
                  <Ionicons
                    name={
                      selectedBank === bank.key
                        ? 'checkmark-circle'
                        : 'checkmark-circle-outline'
                    }
                    size={20}
                    color={
                      selectedBank === bank.key
                        ? colors.primary[500]
                        : colors.secondary[500]
                    }
                  />
                  <Text
                    style={[
                      styles.bankName,
                      selectedBank === bank.key && styles.bankNameSelected,
                    ]}
                  >
                    {bank.name}
                  </Text>
                </TouchableOpacity>
              ))}
          </View>
        </View>

        {/* Hesap Seçimi */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hesap Seçimi</Text>
          <View style={styles.bankList}>
            {accounts.map(acc => (
              <TouchableOpacity
                key={acc.id || acc.name}
                style={[
                  styles.bankItem,
                  selectedAccountId === acc.id && styles.bankItemSelected,
                ]}
                onPress={() => setSelectedAccountId(acc.id || null)}
              >
                <Ionicons
                  name={
                    selectedAccountId === acc.id
                      ? 'checkmark-circle'
                      : 'checkmark-circle-outline'
                  }
                  size={20}
                  color={
                    selectedAccountId === acc.id
                      ? colors.primary[500]
                      : colors.secondary[500]
                  }
                />
                <Text
                  style={[
                    styles.bankName,
                    selectedAccountId === acc.id && styles.bankNameSelected,
                  ]}
                >
                  {acc.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Process Button */}
        {selectedFile && (
          <View style={styles.section}>
            <TouchableOpacity
              style={[
                styles.processButton,
                {
                  backgroundColor: processing
                    ? colors.neutral[400]
                    : colors.primary[500],
                },
              ]}
              onPress={handleProcessPDF}
              disabled={processing}
            >
              {processing ? (
                <>
                  <Ionicons
                    name="sync-outline"
                    size={20}
                    color={colors.background.secondary}
                  />
                  <Text
                    style={[
                      styles.processButtonText,
                      { color: colors.background.secondary },
                    ]}
                  >
                    İşleniyor...
                  </Text>
                </>
              ) : (
                <>
                  <Ionicons
                    name="scan-outline"
                    size={20}
                    color={colors.background.secondary}
                  />
                  <Text
                    style={[
                      styles.processButtonText,
                      { color: colors.background.secondary },
                    ]}
                  >
                    PDF'i İşle ve İçe Aktar
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        )}

        {/* Preview Section */}
        {showPreview && extractedEntries.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Çıkarılan İşlemler ({extractedEntries.length})
            </Text>

            {/* Selection Controls */}
            <View style={styles.selectionControls}>
              <TouchableOpacity
                style={styles.selectionButton}
                onPress={selectAllEntries}
              >
                <Ionicons
                  name="checkmark-done-outline"
                  size={16}
                  color={colors.primary[500]}
                />
                <Text style={styles.selectionButtonText}>Tümünü Seç</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.selectionButton}
                onPress={deselectAllEntries}
              >
                <Ionicons
                  name="close-outline"
                  size={16}
                  color={colors.accent[500]}
                />
                <Text style={styles.selectionButtonText}>Tümünü Kaldır</Text>
              </TouchableOpacity>

              <Text style={styles.selectedCount}>
                {selectedEntries.size} / {extractedEntries.length} seçili
              </Text>
            </View>

            {/* İçe Aktar Butonu (üstte) */}
            <TouchableOpacity
              style={[
                styles.importButton,
                {
                  backgroundColor:
                    importing || selectedEntries.size === 0
                      ? colors.neutral[400]
                      : colors.secondary[500],
                },
              ]}
              onPress={handleImportSelected}
              disabled={importing || selectedEntries.size === 0}
            >
              {importing ? (
                <>
                  <Ionicons
                    name="sync-outline"
                    size={20}
                    color={colors.background.secondary}
                  />
                  <Text
                    style={[
                      styles.importButtonText,
                      { color: colors.background.secondary },
                    ]}
                  >
                    İçe Aktarılıyor...
                  </Text>
                </>
              ) : (
                <>
                  <Ionicons
                    name="add-circle-outline"
                    size={20}
                    color={colors.background.secondary}
                  />
                  <Text
                    style={[
                      styles.importButtonText,
                      { color: colors.background.secondary },
                    ]}
                  >
                    Seçili İşlemleri Ekle ({selectedEntries.size})
                  </Text>
                </>
              )}
            </TouchableOpacity>

            {/* Entries List */}
            <View style={styles.entriesList}>
              {extractedEntries.map((entry, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.entryItem,
                    selectedEntries.has(index) && styles.entryItemSelected,
                  ]}
                  onPress={() => toggleEntrySelection(index)}
                >
                  <View style={styles.entryLeft}>
                    <Ionicons
                      name={
                        selectedEntries.has(index)
                          ? 'checkmark-circle'
                          : 'ellipse-outline'
                      }
                      size={20}
                      color={
                        selectedEntries.has(index)
                          ? colors.primary[500]
                          : colors.neutral[400]
                      }
                    />
                    <View style={styles.entryDetails}>
                      <Text
                        style={[
                          styles.entryDescription,
                          selectedEntries.has(index) &&
                            styles.entryDescriptionSelected,
                        ]}
                        numberOfLines={2}
                      >
                        {entry.description}
                      </Text>
                      <Text
                        style={[
                          styles.entryDate,
                          selectedEntries.has(index) &&
                            styles.entryDateSelected,
                        ]}
                      >
                        {formatDate(entry.date)}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.entryRight}>
                    <Text
                      style={[
                        styles.entryAmount,
                        entry.type === 'income'
                          ? styles.entryAmountIncome
                          : styles.entryAmountExpense,
                        selectedEntries.has(index) &&
                          styles.entryAmountSelected,
                      ]}
                    >
                      {entry.type === 'income' ? '+' : '-'}₺
                      {Math.abs(entry.amount).toFixed(2)}
                    </Text>
                    <Text
                      style={[
                        styles.entryType,
                        selectedEntries.has(index) && styles.entryTypeSelected,
                      ]}
                    >
                      {entry.type === 'income' ? 'Gelir' : 'Gider'}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            {/* Import Button */}
            <TouchableOpacity
              style={[
                styles.importButton,
                {
                  backgroundColor:
                    importing || selectedEntries.size === 0
                      ? colors.neutral[400]
                      : colors.secondary[500],
                },
              ]}
              onPress={handleImportSelected}
              disabled={importing || selectedEntries.size === 0}
            >
              {importing ? (
                <>
                  <Ionicons
                    name="sync-outline"
                    size={20}
                    color={colors.background.secondary}
                  />
                  <Text
                    style={[
                      styles.importButtonText,
                      { color: colors.background.secondary },
                    ]}
                  >
                    İçe Aktarılıyor...
                  </Text>
                </>
              ) : (
                <>
                  <Ionicons
                    name="add-circle-outline"
                    size={20}
                    color={colors.background.secondary}
                  />
                  <Text
                    style={[
                      styles.importButtonText,
                      { color: colors.background.secondary },
                    ]}
                  >
                    Seçili İşlemleri Ekle ({selectedEntries.size})
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        )}

        {/* Instructions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Nasıl Kullanılır?</Text>
          <View style={styles.instructionsList}>
            <View style={styles.instructionItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <Text style={styles.instructionText}>
                Bankanızın mobil uygulaması veya internet bankacılığından PDF
                ekstre indirin
              </Text>
            </View>
            <View style={styles.instructionItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <Text style={styles.instructionText}>
                "PDF Dosyası Seç" butonuna tıklayarak ekstrenizi seçin
              </Text>
            </View>
            <View style={styles.instructionItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <Text style={styles.instructionText}>
                "PDF'i İşle ve İçe Aktar" butonuna tıklayarak işlemleri otomatik
                olarak ekleyin
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
      </SafeAreaView>
    </PremiumGate>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: spacing.screenPadding,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.primary,
    },
    backButton: {
      padding: spacing.sm,
    },
    headerTitle: {
      ...typography.styles.h4,
      color: colors.text.primary,
    },
    placeholder: {
      width: 40,
    },
    content: {
      flex: 1,
    },
    section: {
      paddingHorizontal: spacing.screenPadding,
      marginBottom: spacing.xl,
    },
    loginButtonText: {
      fontSize: typography.sizes.subhead,
      fontWeight: '600',
    },
    loginButton: {
      paddingHorizontal: 32,
      paddingVertical: 12,
      borderRadius: 8,
    },
    emptyStateSubtitle: {
      fontSize: typography.sizes.body,
      textAlign: 'center',
      marginBottom: 24,
      paddingHorizontal: 32,
    },
    emptyStateTitle: {
      fontSize: typography.sizes.title2,
      fontWeight: '600',
      marginTop: 16,
      marginBottom: 8,
    },
    emptyStateContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 60,
    },
    sectionTitle: {
      ...typography.styles.h5,
      color: colors.text.primary,
      marginBottom: spacing.lg,
    },
    infoCard: {
      backgroundColor: colors.background.secondary,
      padding: spacing.xl,
      borderRadius: spacing.cardRadius,
      alignItems: 'center',
      marginTop: spacing.lg,
    },
    infoIconContainer: {
      width: 64,
      height: 64,
      borderRadius: 32,
      backgroundColor: colors.primary[50],
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: spacing.lg,
    },
    infoTitle: {
      ...typography.styles.h5,
      color: colors.text.primary,
      textAlign: 'center',
      marginBottom: spacing.md,
    },
    infoText: {
      ...typography.styles.body2,
      color: colors.text.secondary,
      textAlign: 'center',
      lineHeight: 22,
    },
    bankList: {
      gap: spacing.md,
    },
    bankItem: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.background.secondary,
      padding: spacing.md,
      borderRadius: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border.primary,
    },
    bankItemSelected: {
      borderColor: colors.primary[500],
      backgroundColor: colors.primary[50],
    },
    bankName: {
      ...typography.styles.body2,
      color: colors.text.primary,
      marginLeft: spacing.md,
    },
    bankNameSelected: {
      color: colors.primary[500],
      fontWeight: '600',
    },
    selectButton: {
      backgroundColor: colors.background.secondary,
      padding: spacing['2xl'],
      borderRadius: spacing.cardRadius,
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.border.primary,
      borderStyle: 'dashed',
    },
    selectButtonText: {
      ...typography.styles.h6,
      color: colors.text.primary,
      marginTop: spacing.md,
      marginBottom: spacing.sm,
    },
    selectButtonSubtext: {
      ...typography.styles.body2,
      color: colors.text.secondary,
      textAlign: 'center',
    },
    selectedFile: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: colors.background.secondary,
      padding: spacing.lg,
      borderRadius: spacing.cardRadius,
    },
    fileInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    fileDetails: {
      marginLeft: spacing.md,
      flex: 1,
    },
    fileName: {
      ...typography.styles.body2,
      color: colors.text.primary,
      fontWeight: '600',
    },
    fileSize: {
      ...typography.styles.caption,
      color: colors.text.secondary,
      marginTop: spacing.xs,
    },
    removeButton: {
      padding: spacing.sm,
    },
    processButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.buttonPaddingVertical,
      borderRadius: spacing.cardRadius,
    },
    processButtonText: {
      ...typography.styles.button,
      fontWeight: '600',
      marginLeft: spacing.sm,
    },
    instructionsList: {
      gap: spacing.lg,
    },
    instructionItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    stepNumber: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.primary[500],
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: spacing.md,
      marginTop: 2,
    },
    stepNumberText: {
      ...typography.styles.caption,
      color: colors.background.secondary,
      fontWeight: '600',
    },
    instructionText: {
      ...typography.styles.body2,
      color: colors.text.secondary,
      flex: 1,
      lineHeight: 20,
    },
    // Preview styles
    selectionControls: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: spacing.lg,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      backgroundColor: colors.background.secondary,
      borderRadius: spacing.sm,
    },
    selectionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: spacing.sm,
      paddingVertical: spacing.xs,
    },
    selectionButtonText: {
      ...typography.styles.caption,
      color: colors.text.primary,
      marginLeft: spacing.xs,
      fontWeight: '600',
    },
    selectedCount: {
      ...typography.styles.caption,
      color: colors.text.secondary,
      fontWeight: '600',
    },
    entriesList: {
      gap: spacing.sm,
      marginBottom: spacing.xl,
    },
    entryItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: colors.background.secondary,
      padding: spacing.md,
      borderRadius: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border.primary,
    },
    entryItemSelected: {
      borderColor: colors.primary[500],
      backgroundColor: colors.background.secondary,
    },
    entryLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    entryDetails: {
      marginLeft: spacing.md,
      flex: 1,
    },
    entryDescription: {
      ...typography.styles.body2,
      color: colors.text.primary,
      fontWeight: '600',
      marginBottom: spacing.xs,
    },
    entryDescriptionSelected: {
      color: colors.text.primary, // Normal text rengi seçili durumda
    },
    entryDate: {
      ...typography.styles.caption,
      color: colors.text.secondary,
    },
    entryDateSelected: {
      color: colors.text.secondary, // Normal text rengi seçili durumda
    },
    entryRight: {
      alignItems: 'flex-end',
    },
    entryAmount: {
      ...typography.styles.body2,
      fontWeight: '700',
      marginBottom: spacing.xs,
    },
    entryAmountIncome: {
      color: colors.secondary[500],
    },
    entryAmountExpense: {
      color: colors.accent[500],
    },
    entryAmountSelected: {
      // Normal tutar renklerini koru (yeşil/kırmızı)
    },
    entryType: {
      ...typography.styles.caption,
      color: colors.text.secondary,
      fontWeight: '600',
    },
    entryTypeSelected: {
      color: colors.text.secondary, // Normal text rengi seçili durumda
    },
    importButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: spacing.buttonPaddingVertical,
      borderRadius: spacing.cardRadius,
      marginBottom: 15,
    },
    importButtonText: {
      ...typography.styles.button,
      fontWeight: '600',
      marginLeft: spacing.sm,
    },
  });

export default BankStatementScreen;
