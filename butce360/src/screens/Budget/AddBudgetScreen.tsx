import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Modal,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';


import { useThemedColors } from '../../hooks/useThemedStyles';
import { NumberFormatter } from '../../utils/number';
import { categoryService } from '../../services/categoryService';
import { budgetService } from '../../services/budgetService';
import { Category } from '../../types/models';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { spacing } from '../../theme/spacing';
import { typography } from '../../theme/typography';

// CategorySeparator will be defined after modalStyles

// Color options for budget
const BUDGET_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
];

interface AddBudgetScreenProps {
  route?: {
    params?: {
      budgetId?: string;
    };
  };
  onNavigate?: (screen: string) => void;
}

const AddBudgetScreen: React.FC<AddBudgetScreenProps> = ({ route, onNavigate }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  const modalStyles = createModalStyles(colors);
  
  const isEditing = !!route?.params?.budgetId;
  
  // Form data
  const [formData, setFormData] = useState({
    name: '',
    amount: '',
    period: 'monthly' as 'monthly' | 'yearly',
    categoryId: '',
    color: '#10B981',
  });
  
  // UI states
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  
  // Data states
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      setIsLoadingData(true);

      // Her durumda kategorileri yükle (ziyaretçi modu dahil)
      console.log('[AddBudgetScreen] Fetching categories...');
      const categoriesData = await categoryService.getCategories();
      console.log('[AddBudgetScreen] Categories received:', {
        income: categoriesData?.income?.length || 0,
        expense: categoriesData?.expense?.length || 0,
        expenseCategories: categoriesData?.expense?.map(cat => ({ id: cat.id, name: cat.name })) || []
      });

      // Only expense categories for budgets
      setCategories(categoriesData?.expense || []);
    } catch (error) {
      console.error('[AddBudgetScreen] Error fetching categories:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken hata oluştu');
    } finally {
      setIsLoadingData(false);
    }
  }, []);

  // Load data on mount and prefill when editing
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    const loadBudgetForEdit = async () => {
      if (!isEditing || !route?.params?.budgetId) return;
      try {
        const all = await budgetService.getBudgets();
        const current = all.find(b => b.id === route?.params?.budgetId);
        if (current) {
          setFormData({
            name: current.name,
            amount: String(current.amount),
            period: current.period,
            categoryId: current.categoryId || '',
            color: current.color || '#10B981',
          });
        }
      } catch (e) {
        // ignore
      }
    };
    loadBudgetForEdit();
  }, [isEditing, route?.params?.budgetId]);

  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Bütçe adı gerekli';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Tutar gerekli';
    } else if (!NumberFormatter.isValidNumber(formData.amount)) {
      newErrors.amount = 'Geçerli bir tutar girin';
    } else if (NumberFormatter.parseCurrency(formData.amount) <= 0) {
      newErrors.amount = 'Tutar sıfırdan büyük olmalı';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'Kategori seçimi gerekli';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSave = useCallback(async () => {
    if (!validateForm()) return;

    // Remove guest mode check - allow budget creation for all users

    setIsLoading(true);

    try {
      const budgetData: any = {
        name: formData.name,
        amount: NumberFormatter.parseCurrency(formData.amount),
        period: formData.period,
        color: formData.color,
      };

      // Add category_id if selected
      if (formData.categoryId) {
        budgetData.category_id = formData.categoryId;
      }

      console.log('[AddBudgetScreen] Saving budget:', budgetData);

      // Call API
      if (isEditing && route?.params?.budgetId) {
        await budgetService.updateBudget(route.params.budgetId, budgetData);
      } else {
        await budgetService.createBudget(budgetData);
      }
      
      Alert.alert(
        'Başarılı',
        `Bütçe ${isEditing ? 'güncellendi' : 'eklendi'}.`,
        [{ text: 'Tamam', onPress: () => onNavigate?.('budget') }]
      );
    } catch (error) {
      console.error('[AddBudgetScreen] Error saving budget:', error);
      Alert.alert(
        'Hata',
        error instanceof Error ? error.message : 'Bütçe kaydedilirken bir hata oluştu.',
        [{ text: 'Tamam' }]
      );
    } finally {
      setIsLoading(false);
    }
  }, [validateForm, isEditing, formData, onNavigate, route?.params?.budgetId]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleCategorySelect = (category: Category) => {
    setFormData(prev => ({ ...prev, categoryId: category.id || '' }));
    setErrors(prev => ({ ...prev, categoryId: '' }));
    setShowCategoryModal(false);
  };

  const selectedCategory = categories.find(cat => cat.id === formData.categoryId);

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={[styles.scrollContent, { paddingBottom: spacing.lg }]}
          showsVerticalScrollIndicator={false}
        >
          {/* Form */}
          <View style={styles.form}>
            {/* Name */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Bütçe Adı</Text>
              <TextInput
                style={[styles.input, errors.name && styles.inputError]}
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                placeholder="Örn: Market Bütçesi"
                placeholderTextColor={colors.text.secondary}
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name}</Text>
              )}
            </View>

            {/* Amount */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Tutar</Text>
              <TextInput
                style={[styles.input, errors.amount && styles.inputError]}
                value={formData.amount}
                onChangeText={(value) => handleInputChange('amount', value)}
                placeholder="0,00"
                placeholderTextColor={colors.text.secondary}
                keyboardType="numeric"
              />
              {errors.amount && (
                <Text style={styles.errorText}>{errors.amount}</Text>
              )}
            </View>

            {/* Period */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Dönem</Text>
              <View style={styles.periodSelector}>
                <TouchableOpacity
                  style={[
                    styles.periodButton,
                    formData.period === 'monthly' && styles.periodButtonActive
                  ]}
                  onPress={() => handleInputChange('period', 'monthly')}
                >
                  <Text style={[
                    styles.periodButtonText,
                    formData.period === 'monthly' && styles.periodButtonTextActive
                  ]}>Aylık</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.periodButton,
                    formData.period === 'yearly' && styles.periodButtonActive
                  ]}
                  onPress={() => handleInputChange('period', 'yearly')}
                >
                  <Text style={[
                    styles.periodButtonText,
                    formData.period === 'yearly' && styles.periodButtonTextActive
                  ]}>Yıllık</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Category */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Kategori</Text>
              <TouchableOpacity 
                style={[styles.selector, errors.categoryId && styles.inputError]}
                onPress={() => setShowCategoryModal(true)}
                disabled={isLoadingData || categories.length === 0}
              >
                {selectedCategory ? (
                  <View style={styles.selectedItem}>
                    <Text style={styles.selectedItemText}>{selectedCategory.name}</Text>
                  </View>
                ) : (
                  <Text style={styles.selectorPlaceholder}>
                    {isLoadingData ? 'Yükleniyor...' : 
                     categories.length === 0 ? 'Kategori bulunamadı' : 
                     'Kategori seçin'}
                  </Text>
                )}
                <Text style={styles.selectorArrow}>›</Text>
              </TouchableOpacity>
              {errors.categoryId && (
                <Text style={styles.errorText}>{errors.categoryId}</Text>
              )}
            </View>
          </View>

          {/* Color Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Renk</Text>
            <View style={styles.colorGrid}>
              {BUDGET_COLORS.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    formData.color === color && styles.colorOptionSelected
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, color }))}
                >
                  {formData.color === color && (
                    <Text style={styles.colorOptionCheck}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Save Button */}
          <TouchableOpacity 
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]} 
            onPress={handleSave}
            disabled={isLoading}
          >
            <Text style={styles.saveButtonText}>
              {isLoading ? 'Kaydediliyor...' : isEditing ? 'Güncelle' : 'Kaydet'}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Category Selection Modal */}
      <Modal
        visible={showCategoryModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <SafeAreaView style={modalStyles.container}>
          <View style={modalStyles.header}>
            <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
              <Text style={modalStyles.cancelButton}>İptal</Text>
            </TouchableOpacity>
            <Text style={modalStyles.title}>Kategori Seçin</Text>
            <View style={modalStyles.headerRight} />
          </View>
          
          {isLoadingData ? (
            <View style={modalStyles.loadingContainer}>
              <LoadingSpinner />
            </View>
          ) : (
            <FlatList
              data={categories}
              keyExtractor={(item) => item.id || ''}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    modalStyles.listItem,
                    formData.categoryId === item.id && modalStyles.listItemSelected
                  ]}
                  onPress={() => handleCategorySelect(item)}
                >
                  <View style={modalStyles.itemLeft}>
                    <View style={[modalStyles.categoryIconCircle, { backgroundColor: item.color || '#EF4444' }]}>
                      <Text style={modalStyles.categoryIconText}>{item.icon || '💸'}</Text>
                    </View>
                    <View style={modalStyles.categoryInfo}>
                      <Text style={modalStyles.categoryTitle}>{item.name}</Text>
                      {/* Removed type subtitle per request */}
                    </View>
                  </View>
                  {formData.categoryId === item.id && (
                    <View style={modalStyles.selectedIndicator}>
                      <Text style={modalStyles.checkmark}>✓</Text>
                    </View>
                  )}
                </TouchableOpacity>
              )}
              ItemSeparatorComponent={CategorySeparator}
            />
          )}
        </SafeAreaView>
      </Modal>
    </View>
  );
};

// Main styles
const createStyles = (colors: any) => ({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing.screenPadding,
    paddingTop: spacing.lg,
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    paddingHorizontal: spacing.screenPadding,
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
    backgroundColor: colors.background.primary,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderRadius: 20,
  },
  title: {
    ...typography.styles.title2,
    color: colors.text.primary,
    textAlign: 'center' as const,
    flex: 1,
  },
  headerRight: {
    width: 40,
  },
  form: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: spacing.xl,
  },
  label: {
    ...typography.styles.subhead,
    fontWeight: '600' as const,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border.primary,
    borderRadius: spacing.md,
    paddingHorizontal: spacing.inputPaddingHorizontal,
    paddingVertical: spacing.inputPaddingVertical,
    ...typography.styles.body,
    color: colors.text.primary,
    backgroundColor: colors.background.secondary,
  },
  inputError: {
    borderColor: colors.error[500],
  },
  errorText: {
    ...typography.styles.footnote,
    color: colors.error[500],
    marginTop: spacing.xs,
  },
  periodSelector: {
    flexDirection: 'row' as const,
    backgroundColor: colors.background.tertiary || colors.neutral?.[100] || '#f1f5f9',
    borderRadius: 12,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center' as const,
    borderRadius: 8,
  },
  periodButtonActive: {
    backgroundColor: colors.background.secondary,
    shadowColor: colors.text.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  periodButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: colors.text.secondary,
  },
  periodButtonTextActive: {
    color: colors.text.primary,
  },
  selector: {
    borderWidth: 1,
    borderColor: colors.border.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    backgroundColor: colors.background.secondary,
  },
  selectedItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  selectedCategoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: 12,
  },
  selectedCategoryIconText: {
    fontSize: 16,
    color: '#ffffff',
  },
  selectedItemText: {
    fontSize: 16,
    color: colors.text.primary,
  },
  selectorPlaceholder: {
    fontSize: 16,
    color: colors.text.secondary,
  },
  selectorArrow: {
    fontSize: 18,
    color: colors.text.secondary,
  },
  saveButton: {
    backgroundColor: colors.primary[500],
    paddingVertical: spacing.buttonPaddingVertical,
    borderRadius: spacing.md,
    alignItems: 'center' as const,
    marginTop: spacing.lg,
    minHeight: 48,
  },
  saveButtonDisabled: {
    backgroundColor: colors.neutral[400],
  },
  saveButtonText: {
    ...typography.styles.button,
    color: colors.background.secondary,
  },
  // Color picker styles
  colorGrid: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 12,
    marginTop: 8,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  colorOptionSelected: {
    borderColor: colors.text.primary,
    borderWidth: 3,
  },
  colorOptionCheck: {
    color: colors.background.secondary,
    fontSize: 16,
    fontWeight: 'bold' as const,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});

// Modal styles - tema uyumlu fonksiyon haline getir
const createModalStyles = (colors: any) => ({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  cancelButton: {
    fontSize: 16,
    color: colors.primary[500],
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold' as const,
    color: colors.text.primary,
  },
  headerRight: {
    width: 50,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  listItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.background.primary,
  },
  listItemSelected: {
    backgroundColor: colors.background.secondary,
  },
  itemLeft: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  categoryIconCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginRight: 16,
  },
  categoryIconText: {
    fontSize: 24,
    color: '#ffffff',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: colors.text.primary,
    marginBottom: 2,
  },
  categorySubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
  },
  selectedIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary[500],
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  checkmark: {
    fontSize: 18,
    color: '#ffffff',
    fontWeight: 'bold' as const,
  },
  separator: {
    height: 1,
    backgroundColor: colors.border.primary,
    marginLeft: 20,
  },
});

// Separator component for category list
const CategorySeparator: React.FC = () => (
  <View style={separatorStyle} />
);

const separatorStyle = {
  height: 1,
  backgroundColor: '#e2e8f0', // Default border color
  marginLeft: 20,
};

export default AddBudgetScreen;
