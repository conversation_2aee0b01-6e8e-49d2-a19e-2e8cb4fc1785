import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  Alert,
  ActivityIndicator,
} from 'react-native';

import { useAuth } from '../../context/AuthContext';
import { categoryService } from '../../services/categoryService';
import { Category } from '../../types/models';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';



interface CategoriesScreenProps {
  onNavigate?: (screen: string) => void;
}

const CategoriesScreen: React.FC<CategoriesScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const { state: authState } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'income' | 'expense'>('expense');
  const [_categories, setCategories] = useState<{ income: Category[]; expense: Category[] }>({
    income: [],
    expense: []
  });
  const [_loading, setLoading] = useState(true);

  // Load categories from service
  const loadCategories = async () => {
    try {
      setLoading(true);
      const fetchedCategories = await categoryService.getCategories();
      setCategories(fetchedCategories);
      console.log('[CategoriesScreen] Loaded categories:', {
        income: fetchedCategories.income.length,
        expense: fetchedCategories.expense.length
      });
    } catch (error) {
      console.error('[CategoriesScreen] Error loading categories:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, []);

  // Get category icon by type
  const getCategoryIcon = (type: string) => {
    return type === 'income' ? '💰' : '💸';
  };

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      setLoading(true);
      const categoriesData = await categoryService.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('[CategoriesScreen] Error fetching categories:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // Load categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchCategories();
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleAddCategory = () => {
    onNavigate?.('AddCategory');
  };

  const handleCategoryPress = (categoryId: string, categoryName: string) => {
    if (authState.isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Kategori detaylarını görmek için giriş yapmanız gerekiyor.',
        [
          { text: 'İptal', style: 'cancel' },
          { text: 'Giriş Yap', onPress: () => onNavigate && onNavigate('login') },
        ]
      );
      return;
    }
    // Navigate to category detail screen with category ID
    onNavigate && onNavigate(`CategoryDetail?id=${categoryId}&name=${encodeURIComponent(categoryName)}`);
  };

  const handleDeleteCategory = async (categoryId: string, categoryName: string, isDefault: boolean) => {
    if (isDefault) {
      Alert.alert(
        'Varsayılan Kategori',
        'Varsayılan kategoriler silinemez.',
        [{ text: 'Tamam' }]
      );
      return;
    }

    Alert.alert(
      'Kategori Sil',
      `"${categoryName}" kategorisini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await categoryService.deleteCategory(categoryId);
              await fetchCategories(); // Refresh categories
              Alert.alert('Başarılı', 'Kategori başarıyla silindi.');
            } catch (error) {
              console.error('[CategoriesScreen] Error deleting category:', error);
              Alert.alert('Hata', 'Kategori silinirken bir hata oluştu.');
            }
          }
        }
      ]
    );

    // duplicate alert removed
  };

  const currentCategories = _categories[selectedTab];

  const renderTabButtons = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity
        style={[
          styles.tabButton,
          { backgroundColor: colors.background.secondary },
          selectedTab === 'income' && [styles.tabButtonActive, { backgroundColor: colors.primary[500] }]
        ]}
        onPress={() => setSelectedTab('income')}
      >
        <Text style={[
          styles.tabButtonText,
          { color: colors.text.secondary },
          selectedTab === 'income' && [styles.tabButtonTextActive, { color: colors.background.secondary }]
        ]}>
          💰 Gelir ({_categories.income.length})
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[
          styles.tabButton,
          { backgroundColor: colors.background.secondary },
          selectedTab === 'expense' && [styles.tabButtonActive, { backgroundColor: colors.primary[500] }]
        ]}
        onPress={() => setSelectedTab('expense')}
      >
        <Text style={[
          styles.tabButtonText,
          { color: colors.text.secondary },
          selectedTab === 'expense' && [styles.tabButtonTextActive, { color: colors.background.secondary }]
        ]}>
          💸 Gider ({_categories.expense.length})
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderCategoryItem = (category: typeof currentCategories[0]) => (
    <TouchableOpacity
      key={category.id}
      style={[styles.categoryItem, { backgroundColor: colors.background.secondary }]}
      onPress={() => handleCategoryPress(category.id!, category.name)}
      activeOpacity={0.7}
    >
      <View style={styles.categoryLeft}>
        <View style={[
          styles.categoryIcon,
          { backgroundColor: category.color }
        ]}>
          <Text style={styles.categoryIconText}>
            {getCategoryIcon(category.type)}
          </Text>
        </View>
        <View style={styles.categoryInfo}>
          <View style={styles.categoryHeader}>
            <Text style={[styles.categoryName, { color: colors.text.primary }]}>{category.name}</Text>
          </View>
          {/* Removed type subtitle for cleaner list UI */}
        </View>
      </View>
      <View style={styles.categoryRight}>
        <TouchableOpacity
          style={styles.moreButton}
          onPress={() => {
            Alert.alert(
              category.name,
              'Ne yapmak istiyorsunuz?',
              [
                { text: 'İptal', style: 'cancel' },
                { text: 'Düzenle', onPress: () => onNavigate && onNavigate(`AddCategory?categoryId=${category.id}`) },
                ...(category.isDefault ? [] : [
                  {
                    text: 'Sil',
                    style: 'destructive' as const,
                    onPress: () => handleDeleteCategory(category.id!, category.name, category.isDefault)
                  }
                ]),
              ]
            );
          }}
          activeOpacity={0.7}
        >
          <Text style={[styles.moreButtonText, { color: colors.text.secondary }]}>⋯</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateIcon}>
        {selectedTab === 'income' ? '💰' : '💸'}
      </Text>
      <Text style={[styles.emptyStateTitle, { color: colors.text.primary }]}>
        {selectedTab === 'income' ? 'Henüz gelir kategorisi yok' : 'Henüz gider kategorisi yok'}
      </Text>
      <Text style={[styles.emptyStateText, { color: colors.text.secondary }]}>
        {selectedTab === 'income'
          ? 'İlk gelir kategorinizi ekleyerek başlayın'
          : 'İlk gider kategorinizi ekleyerek başlayın'}
      </Text>
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: colors.primary[500] }]}
        onPress={handleAddCategory}
        activeOpacity={0.8}
      >
        <Text style={[styles.addButtonText, { color: colors.background.secondary }]}>+ Kategori Ekle</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      {/* Tab Buttons */}
      {renderTabButtons()}

      {/* Category List */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {_loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
            <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
              Kategoriler yükleniyor...
            </Text>
          </View>
        ) : currentCategories.length > 0 ? (
          currentCategories.map(renderCategoryItem)
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Tabs
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    minHeight: 44,
  },
  tabButtonActive: {},
  tabButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
    fontWeight: '600',
  },
  tabButtonTextActive: {},

  // List
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 80,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryIconText: {
    fontSize: 18,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryName: {
    ...typography.styles.listItem,
    fontWeight: '600',
    marginRight: 8,
  },
  defaultBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  defaultBadgeText: {
    ...typography.styles.caption2,
    fontSize: 10,
  },
  categoryStats: {
    ...typography.styles.footnote,
  },
  categoryRight: {
    alignItems: 'flex-end',
  },
  moreButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moreButtonText: {
    fontSize: 20,
  },

  // Empty State
  emptyState: {
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyStateIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyStateTitle: {
    ...typography.styles.title3,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    ...typography.styles.subhead,
    textAlign: 'center',
    maxWidth: 250,
    marginBottom: 32,
  },
  addButton: {
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    minHeight: 44,
  },
  addButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
  },

  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    ...typography.styles.body,
  },
});

export default CategoriesScreen;
