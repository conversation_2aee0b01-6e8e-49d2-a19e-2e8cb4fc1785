import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Alert,
  ScrollView,
  Switch,
  Platform,
  Share,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';

// @ts-ignore
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import { transactionService } from '../../services/transactionService';
import { categoryService } from '../../services/categoryService';
import { authService } from '../../services/authService';
import { localTransactionService } from '../../services/localTransactionService';
import { localCategoryService } from '../../services/localCategoryService';

import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

interface ExportScreenProps {
  onNavigate?: (screen: string) => void;
}

const ExportScreen: React.FC<ExportScreenProps> = ({ }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  
  const [exportOptions, setExportOptions] = useState({
    includeTransactions: true,
    includeCategories: false,
    includeAccounts: false,
    includeBudgets: false,
    includeReports: true,
    dateRange: 'all', // 'all', 'thisMonth', 'thisYear', 'custom'
  });
  const [exporting, setExporting] = useState(false);



  const generatePDFContent = async () => {
    let content = `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #3b82f6; text-align: center; }
            h2 { color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 5px; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #d1d5db; padding: 8px; text-align: left; }
            th { background-color: #f3f4f6; }
            .summary { background-color: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <h1>Finansal Rapor</h1>
          <p><strong>Oluşturulma Tarihi:</strong> ${new Date().toLocaleDateString('tr-TR')}</p>
    `;

    if (exportOptions.includeTransactions) {
      try {
        // Check if user is authenticated
        const token = await authService.getStoredToken();
        let transactions = [];

        if (token) {
          // Authenticated user: get from API
          console.log('[ExportScreen] Getting transactions from API...');
          const result = await transactionService.getTransactions(1, 1000); // Get all transactions
          transactions = result.transactions || [];
        } else {
          // Guest mode: get from local storage using local service
          transactions = await localTransactionService.getTransactions();
        }

        content += `
          <h2>İşlemler (${transactions.length} adet)</h2>
          <table>
            <tr>
              <th>Tarih</th>
              <th>Açıklama</th>
              <th>Kategori</th>
              <th>Tutar</th>
              <th>Tür</th>
            </tr>
        `;

        transactions.forEach((transaction: any) => {
          // Handle different data formats (API vs local storage)
          let date, title, category, amount, type;

          if (token) {
            // API format (Transaction interface)
            date = transaction.transactionDate;
            title = transaction.title;
            category = transaction.categoryName;
            amount = transaction.amount;
            type = transaction.type;
          } else {
            // Local storage format (LocalTransaction interface)
            date = transaction.date;
            title = transaction.title;
            category = transaction.category;
            amount = transaction.amount;
            type = transaction.type;
          }

          content += `
            <tr>
              <td>${new Date(date).toLocaleDateString('tr-TR')}</td>
              <td>${title}</td>
              <td>${category}</td>
              <td>${amount.toLocaleString('tr-TR')} ₺</td>
              <td>${type === 'income' ? 'Gelir' : 'Gider'}</td>
            </tr>
          `;
        });

        content += '</table>';

        // Summary
        const totalIncome = transactions.filter((t: any) => t.type === 'income').reduce((sum: number, t: any) => sum + t.amount, 0);
        const totalExpense = transactions.filter((t: any) => t.type === 'expense').reduce((sum: number, t: any) => sum + t.amount, 0);

        content += `
          <div class="summary">
            <h3>Özet</h3>
            <p><strong>Toplam Gelir:</strong> ${totalIncome.toLocaleString('tr-TR')} ₺</p>
            <p><strong>Toplam Gider:</strong> ${totalExpense.toLocaleString('tr-TR')} ₺</p>
            <p><strong>Net Bakiye:</strong> ${(totalIncome - totalExpense).toLocaleString('tr-TR')} ₺</p>
          </div>
        `;
      } catch (error) {
        content += `
          <h2>İşlemler</h2>
          <p>İşlemler yüklenirken hata oluştu.</p>
        `;
      }
    }

    if (exportOptions.includeCategories) {
      try {
        // Check if user is authenticated
        const token = await authService.getStoredToken();
        let categories = [];

        if (token) {
          // Authenticated user: get from API
          console.log('[ExportScreen] Getting categories from API...');
          const categoriesData = await categoryService.getCategories();
          categories = [...(categoriesData?.income || []), ...(categoriesData?.expense || [])];
        } else {
          // Guest mode: get from local storage using local service
          categories = await localCategoryService.getCategories();
        }

        content += `
          <h2>Kategoriler (${categories.length} adet)</h2>
          <table>
            <tr>
              <th>Ad</th>
              <th>Tür</th>
              <th>Renk</th>
              <th>İkon</th>
            </tr>
        `;

        categories.forEach((category: any) => {
          content += `
            <tr>
              <td>${category.name}</td>
              <td>${category.type === 'income' ? 'Gelir' : 'Gider'}</td>
              <td style="background-color: ${category.color}; color: white;">${category.color}</td>
              <td>${category.icon || '📦'}</td>
            </tr>
          `;
        });

        content += '</table>';
      } catch (error) {
        content += `
          <h2>Kategoriler</h2>
          <p>Kategoriler yüklenirken hata oluştu.</p>
        `;
      }
    }

    content += `
        </body>
      </html>
    `;

    return content;
  };

  const openPDFWithSystemViewer = async (filePath: string) => {
    try {
      if (Platform.OS === 'ios') {
        // iOS'ta Share sheet kullan
        await Share.share({
          url: `file://${filePath}`,
          title: 'Finansal Rapor',
        });
      } else {
        // Android'de intent ile aç
        const url = `file://${filePath}`;
        const supported = await Linking.canOpenURL(url);
        if (supported) {
          await Linking.openURL(url);
        } else {
          // Fallback: Share sheet kullan
          await Share.share({
            url: `file://${filePath}`,
            title: 'Finansal Rapor',
          });
        }
      }
    } catch (error) {
      // Fallback: Share sheet kullan
      try {
        await Share.share({
          url: `file://${filePath}`,
          title: 'Finansal Rapor',
        });
      } catch (shareError) {
        Alert.alert('Hata', 'PDF açılırken bir hata oluştu');
      }
    }
  };

  const handleExport = async () => {
    setExporting(true);

    try {
      // Generate PDF content
      const htmlContent = await generatePDFContent();

      // Create PDF in temp directory
      const fileName = `finansal-rapor-${new Date().getTime()}.pdf`;
      const options = {
        html: htmlContent,
        fileName: fileName.replace('.pdf', ''),
        directory: 'Documents',
      };

      const file = await RNHTMLtoPDF.convert(options);

      // Show options to user
      Alert.alert(
        'PDF Hazır',
        'PDF başarıyla oluşturuldu. Ne yapmak istiyorsunuz?',
        [
          {
            text: 'İptal',
            style: 'cancel',
          },
          {
            text: 'Görüntüle',
            onPress: async () => {
              await openPDFWithSystemViewer(file.filePath);
            }
          },
          {
            text: 'Paylaş',
            onPress: async () => {
              try {
                await Share.share({
                  url: `file://${file.filePath}`,
                  title: 'Finansal Rapor',
                  message: 'Bütçe360 finansal raporu',
                });
              } catch (shareError) {
                Alert.alert('Hata', 'PDF paylaşılırken bir hata oluştu');
              }
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Hata', 'PDF oluşturulurken bir hata oluştu');
    } finally {
      setExporting(false);
    }
  };

  const toggleOption = (key: string) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const setDateRange = (range: string) => {
    setExportOptions(prev => ({
      ...prev,
      dateRange: range
    }));
  };

  return (
    <SafeAreaView edges={['left','right']} style={[styles.container, { backgroundColor: colors.background.primary }]}> 
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      <ScrollView style={styles.content} contentContainerStyle={{ paddingBottom: spacing.lg }} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.section}>
          <View style={styles.infoCard}>
            <View style={styles.infoIconContainer}>
              <Ionicons name="document-outline" size={32} color={colors.primary[500]} />
            </View>
            <Text style={styles.infoTitle}>PDF Raporu Oluştur</Text>
            <Text style={styles.infoText}>
              Tüm finansal verilerinizi detaylı PDF raporu olarak dışa aktarın.
            </Text>
          </View>
        </View>

        {/* Export Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dışa Aktarılacak Veriler</Text>
          <View style={styles.optionsList}>
            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="swap-horizontal-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>İşlemler</Text>
              </View>
              <Switch
                value={exportOptions.includeTransactions}
                onValueChange={() => toggleOption('includeTransactions')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeTransactions ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="folder-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Kategoriler</Text>
              </View>
              <Switch
                value={exportOptions.includeCategories}
                onValueChange={() => toggleOption('includeCategories')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeCategories ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="business-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Hesaplar</Text>
              </View>
              <Switch
                value={exportOptions.includeAccounts}
                onValueChange={() => toggleOption('includeAccounts')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeAccounts ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="target-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Bütçeler</Text>
              </View>
              <Switch
                value={exportOptions.includeBudgets}
                onValueChange={() => toggleOption('includeBudgets')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeBudgets ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="bar-chart-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Raporlar</Text>
              </View>
              <Switch
                value={exportOptions.includeReports}
                onValueChange={() => toggleOption('includeReports')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeReports ? colors.primary[500] : colors.neutral[500]}
              />
            </View>
          </View>
        </View>

        {/* Date Range */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tarih Aralığı</Text>
          <View style={styles.dateRangeOptions}>
            {[
              { key: 'all', label: 'Tüm Veriler', icon: 'infinite-outline' },
              { key: 'thisMonth', label: 'Bu Ay', icon: 'calendar-outline' },
              { key: 'thisYear', label: 'Bu Yıl', icon: 'calendar-outline' },
              { key: 'custom', label: 'Özel Aralık', icon: 'options-outline' },
            ].map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.dateRangeOption,
                  exportOptions.dateRange === option.key && styles.dateRangeOptionActive
                ]}
                onPress={() => setDateRange(option.key)}
              >
                <Ionicons 
                  name={option.icon} 
                  size={20} 
                  color={exportOptions.dateRange === option.key ? colors.primary[500] : colors.text.secondary} 
                />
                <Text style={[
                  styles.dateRangeOptionText,
                  exportOptions.dateRange === option.key && styles.dateRangeOptionTextActive
                ]}>
                  {option.label}
                </Text>
                {exportOptions.dateRange === option.key && (
                  <Ionicons name="checkmark-circle" size={20} color={colors.primary[500]} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Export Button */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[
              styles.exportButton,
              { backgroundColor: exporting ? colors.neutral[400] : colors.primary[500] }
            ]}
            onPress={handleExport}
            disabled={exporting}
          >
            {exporting ? (
              <>
                <Ionicons name="sync-outline" size={20} color={colors.background.secondary} />
                <Text style={[styles.exportButtonText, { color: colors.background.secondary }]}>
                  PDF Oluşturuluyor...
                </Text>
              </>
            ) : (
              <>
                <Ionicons name="download-outline" size={20} color={colors.background.secondary} />
                <Text style={[styles.exportButtonText, { color: colors.background.secondary }]}>
                  PDF Olarak Dışa Aktar
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        {/* Info */}
        <View style={styles.section}>
          <View style={styles.infoBox}>
            <Ionicons name="information-circle-outline" size={20} color={colors.primary[500]} />
            <Text style={styles.infoBoxText}>
              PDF raporu tüm seçtiğiniz verileri içerecek ve cihazınıza indirilecektir.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },
  infoCard: {
    backgroundColor: colors.background.secondary,
    padding: spacing.xl,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  infoIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  infoTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  infoText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsList: {
    backgroundColor: colors.background.secondary,
    borderRadius: spacing.cardRadius,
    overflow: 'hidden',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    ...typography.styles.body2,
    color: colors.text.primary,
    marginLeft: spacing.md,
  },
  dateRangeOptions: {
    gap: spacing.sm,
  },
  dateRangeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    borderWidth: 1,
    borderColor: colors.border.primary,
  },
  dateRangeOptionActive: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  dateRangeOptionText: {
    ...typography.styles.body2,
    color: colors.text.primary,
    marginLeft: spacing.md,
    flex: 1,
  },
  dateRangeOptionTextActive: {
    color: colors.primary[500],
    fontWeight: '600',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.buttonPaddingVertical,
    borderRadius: spacing.cardRadius,
  },
  exportButtonText: {
    ...typography.styles.button,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.primary[50],
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
  },
  infoBoxText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    marginLeft: spacing.md,
    flex: 1,
    lineHeight: 20,
  },
});

export default ExportScreen;
