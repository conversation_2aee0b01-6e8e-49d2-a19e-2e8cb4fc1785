import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors, useThemeState } from '../../hooks/useThemedStyles';
import DateTimePicker from '@react-native-community/datetimepicker';
import { typography } from '../../theme/typography';
import { investmentService, SUPPORTED_ASSETS } from '../../services/investmentService';
import { NavigationProp, useNavigation } from '@react-navigation/native';

type RootStackParamList = {
  Investment: undefined;
};

const AddInvestmentSimulationScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const colors = useThemedColors();
  const { isDark } = useThemeState();
  const insets = useSafeAreaInsets();
  
  const [selectedAsset, setSelectedAsset] = useState('');
  const [amount, setAmount] = useState('');
  const [startDate, setStartDate] = useState('');
  const [loading, setLoading] = useState(false);
  const [isDatePickerOpen, setDatePickerOpen] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(new Date());

  const handleSubmit = async () => {
    // Validation
    if (!selectedAsset) {
      Alert.alert('Hata', 'Lütfen bir varlık seçin.');
      return;
    }
    
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
      return;
    }
    
    if (!startDate) {
      Alert.alert('Hata', 'Lütfen bir tarih girin.');
      return;
    }

    // Date validation
    const selectedDate = new Date(startDate);
    const today = new Date();
    
    if (selectedDate >= today) {
      Alert.alert('Hata', 'Başlangıç tarihi bugünden önce olmalıdır.');
      return;
    }

    try {
      setLoading(true);
      
      const result = await investmentService.simulateInvestment({
        asset: selectedAsset,
        amount: parseFloat(amount),
        start_date: startDate,
      });
      
      // Navigate back and notify
      navigation.goBack();
      Alert.alert(
        'Yatırım Eklendi',
        `${investmentService.getAssetDisplayName(selectedAsset)} için ₺${result.amount_invested} tutarında yatırım eklendi.`
      );
      
      // Go back to investment screen
      navigation.goBack();
      
    } catch (error: any) {
      console.error('Simulation error:', error);
      Alert.alert('Hata', error.message || 'Simülasyon sırasında bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  // no-op

  const getAssetIcon = (assetId: string) => {
    const icons: { [key: string]: string } = {
      'BTC': 'logo-bitcoin',
      'ETH': 'diamond-outline',
      'GOLD': 'medal-outline',
      'USDTRY': 'cash-outline',
      'BIST100': 'trending-up-outline',
    };
    return icons[assetId] || 'help-circle-outline';
  };

  const getAssetCategory = (assetId: string) => {
    const categories: { [key: string]: string } = {
      'BTC': 'Kripto Para',
      'ETH': 'Kripto Para',
      'GOLD': 'Emtia',
      'USDTRY': 'Döviz',
      'BIST100': 'Hisse Senedi',
    };
    return categories[assetId] || 'Diğer';
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} backgroundColor={colors.background.primary} />
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={insets.top}
      >
      <ScrollView 
        style={styles.content} 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        
        {/* Removed simulation type selector; this screen now acts as 'Yatırım Ekle' */}

        {/* Asset Selection */}
        <View style={[styles.section, styles.sectionCard, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Varlık Seçin
          </Text>
          <View style={styles.assetGrid}>
            {SUPPORTED_ASSETS.map((asset) => (
              <TouchableOpacity
                key={asset.id}
                style={[
                  styles.assetCard,
                  { backgroundColor: colors.background.secondary },
                  selectedAsset === asset.id && { backgroundColor: colors.primary[500] }
                ]}
                onPress={() => setSelectedAsset(asset.id)}
              >
                <Ionicons
                  name={getAssetIcon(asset.id)}
                  size={32}
                  color={selectedAsset === asset.id ? colors.background.secondary : colors.primary[500]}
                />
                <Text style={[
                  styles.assetName,
                  { color: selectedAsset === asset.id ? colors.background.secondary : colors.text.primary }
                ]}>
                  {asset.name}
                </Text>
                <Text style={[
                  styles.assetCategory,
                  { color: selectedAsset === asset.id ? colors.background.secondary : colors.text.secondary }
                ]}>
                  {getAssetCategory(asset.id)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Amount Input */}
        <View style={[styles.section, styles.sectionCard, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Yatırım Tutarı (₺)
          </Text>
          <TextInput
            style={[
              styles.input,
              { 
                backgroundColor: colors.background.secondary,
                color: colors.text.primary,
                borderColor: colors.border.primary
              }
            ]}
            value={amount}
            onChangeText={setAmount}
            placeholder="Örn: 1000"
            placeholderTextColor={colors.text.secondary}
            keyboardType="numeric"
          />
        </View>

        {/* Date Input */}
        <View style={[styles.section, styles.sectionCard, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Yatırım Tarihi
          </Text>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => { setTempDate(startDate ? new Date(startDate) : new Date()); setDatePickerOpen(true); }}
            style={[
              styles.input,
              styles.dateInput,
              { 
                backgroundColor: colors.background.secondary,
                borderColor: (colors as any).neutral?.[900] || '#000',
              }
            ]}
          >
            <Text style={[styles.dateInputText, { color: colors.text.primary }]}>
              {startDate || 'YYYY-MM-DD (Örn: 2023-01-01)'}
            </Text>
          </TouchableOpacity>
          <Text style={[styles.dateHint, { color: colors.text.secondary }]}>
            Yatırımı başlattığınız tarihi seçin
          </Text>
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: colors.primary[500] },
            (!selectedAsset || !amount || !startDate || loading) && styles.submitButtonDisabled
          ]}
          onPress={handleSubmit}
          disabled={!selectedAsset || !amount || !startDate || loading}
        >
          {loading ? (
            <ActivityIndicator color={colors.background.secondary} />
          ) : (
            <Text style={[styles.submitButtonText, { color: colors.background.secondary }]}>
              Yatırım Ekle
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>
      </KeyboardAvoidingView>

      {isDatePickerOpen && (
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.background.secondary }]}>
            <Text style={[styles.sectionTitle, styles.modalTitleMargin, { color: colors.text.primary }]}>Tarih</Text>
            <DateTimePicker
              value={tempDate}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              themeVariant={isDark ? 'dark' : 'light'}
              textColor={isDark ? '#fff' : '#000'}
              onChange={(_e, d) => { if (d) setTempDate(d); }}
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity onPress={() => setDatePickerOpen(false)}>
                <Text style={[styles.modalButtonText, { color: colors.text.secondary }]}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => {
                const y = tempDate.getFullYear();
                const m = String(tempDate.getMonth() + 1).padStart(2, '0');
                const d = String(tempDate.getDate()).padStart(2, '0');
                setStartDate(`${y}-${m}-${d}`);
                setDatePickerOpen(false);
              }}>
                <Text style={[styles.modalButtonText, { color: colors.primary[500] }]}>Tamam</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { flex: 1, padding: 20 },
  section: { marginBottom: 24 },
  sectionTitle: { ...typography.styles.headline, marginBottom: 12, fontWeight: '600' },
  typeContainer: { flexDirection: 'row', gap: 12 },
  typeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  typeButtonText: { ...typography.styles.button },
  assetGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  assetCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'center',
  },
  assetName: { ...typography.styles.body, fontWeight: '600', marginTop: 8, textAlign: 'center' },
  assetCategory: { ...typography.styles.caption, marginTop: 4, textAlign: 'center' },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    ...typography.styles.body,
  },
  dateHint: { ...typography.styles.caption, marginTop: 8 },
  submitButton: {
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 40,
  },
  submitButtonText: { ...typography.styles.button, fontWeight: '600' },
  submitButtonDisabled: {
    opacity: 0.5,
  },
  modalTitleMargin: {
    marginBottom: 8,
  },
  keyboardContainer: { flex: 1 },
  scrollContent: { 
    paddingTop: 8, 
    paddingBottom: 12, 
  },
  sectionCard: {
    borderRadius: 12,
    padding: 16,
  },
  dateInput: {
    justifyContent: 'center',
  },
  dateInputText: {
    ...typography.styles.body,
  },
  modalOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: '#00000080',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    borderRadius: 12,
    padding: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
    gap: 12,
  },
  modalButtonText: {
    ...typography.styles.body,
    fontWeight: '600',
  },
});

export default AddInvestmentSimulationScreen;
