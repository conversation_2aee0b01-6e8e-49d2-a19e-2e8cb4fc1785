import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  RefreshControl,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors, useThemeState } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { investmentService, InvestmentSimulateResponse } from '../../services/investmentService';
import { authService } from '../../services/authService';
import { formatCurrency } from '../../utils/formatters';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { TAB_BAR_MIN_HEIGHT } from '../../navigation/BottomTabNavigator';
import PremiumGate from '../../components/premium/PremiumGate';
import { usePremium } from '../../context/PremiumContext';

// navigation typings simplified for nested navigation
type RootStackParamList = {
  Investment: undefined;
  login: undefined;
};

const InvestmentScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const colors = useThemedColors();
  const { isDark } = useThemeState();
  const insets = useSafeAreaInsets();
  
  const handleNavigation = (screen: keyof RootStackParamList) => {
    // Ensure navigation is initialized and ready
    if (navigation && navigation.navigate) {
      navigation.navigate(screen);
    } else {
      console.warn('Navigation is not ready yet');
    }
  };
  
  const [simulations, setSimulations] = useState<InvestmentSimulateResponse[]>([]);
  const [_loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Premium context
  const { state: premiumState } = usePremium();

  const checkAuthStatus = useCallback(async () => {
    try {
      const token = await authService.getStoredToken();
      setIsAuthenticated(!!token);
    } catch (error) {
      console.error('Error checking auth status:', error);
      setIsAuthenticated(false);
    }
  }, []);

  const loadSimulations = useCallback(async () => {
    if (!isAuthenticated) return;

    // Don't make API call if user is not premium
    if (!premiumState.isPremium) {
      console.log('[InvestmentScreen] User is not premium, skipping API call');
      setSimulations([]);
      return;
    }

    try {
      setLoading(true);
      const result = await investmentService.getSimulationHistory(1, 50);
      setSimulations(result.simulations);
    } catch (error: any) {
      console.error('Error loading simulations:', error);

      // For any unexpected errors, show alert
      Alert.alert('Hata', 'Yatırım simülasyonları yüklenirken bir hata oluştu.');
      setSimulations([]);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, premiumState.isPremium]);

  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  useEffect(() => {
    if (isAuthenticated) {
      loadSimulations();
    }
  }, [isAuthenticated, loadSimulations]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSimulations();
    setRefreshing(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getAssetIcon = (asset: string) => {
    const icons: { [key: string]: string } = {
      'BTC': 'logo-bitcoin',
      'ETH': 'logo-ethereum',
      'GOLD': 'star',
      'USDTRY': 'logo-usd',
      'EURTRY': 'logo-euro',
      'BIST100': 'trending-up',
    };
    return icons[asset] || 'flash';
  };

  const getGrowthColor = (growthRate: number) => {
    if (growthRate > 0) return colors.success[500];
    if (growthRate < 0) return colors.error[500];
    return colors.text.secondary;
  };

  const formatGrowthRate = (rate: number) => {
    const sign = rate >= 0 ? '+' : '';
    return `${sign}${rate.toFixed(2)}%`;
  };

  if (!isAuthenticated) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} backgroundColor={colors.background.primary} />
        <View style={styles.emptyStateContainer}>
          <Ionicons name="trending-up-outline" size={80} color={colors.text.secondary} />
          <Text style={[styles.emptyStateTitle, { color: colors.text.primary }]}>
            Giriş Yapın
          </Text>
          <Text style={[styles.emptyStateSubtitle, { color: colors.text.secondary }]}>
            Yatırım simülasyonu yapmak için giriş yapmanız gerekiyor
          </Text>
          <TouchableOpacity
            style={[styles.loginButton, { backgroundColor: colors.primary[500] }]}
            onPress={() => handleNavigation('login')}
          >
            <Text style={[styles.loginButtonText, { color: colors.background.secondary }]}>
              Giriş Yap
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <PremiumGate
      featureName="Yatırım Simülasyonu"
      title="Yatırım Simülasyonu"
      description="Yatırım simülasyonu özelliği premium abonelik gerektirir. Geçmiş verilerle yatırımlarınızın performansını analiz edin."
    >
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} backgroundColor={colors.background.primary} />

        {/* Header removed; NavigationHeader handles title/actions */}

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={insets.top}
      >
      <ScrollView
        style={styles.content}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: insets.bottom + TAB_BAR_MIN_HEIGHT + 24 }
        ]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
      >
        {/* Top inline action to open comparison */}
        <View style={styles.whatIfContainer}>
          <TouchableOpacity
            onPress={() => (navigation as any).navigate('investment', { screen: 'WhatIfComparison' })}
            style={[styles.whatIfButton, styles.whatIfButtonCenter, { backgroundColor: colors.accent[500] }]}
          >
            <Ionicons name="analytics-outline" size={20} color={colors.background.secondary} />
            <Text style={[styles.whatIfButtonText, { color: colors.background.secondary }]}>Simülasyonda İncele</Text>
          </TouchableOpacity>
        </View>

        {simulations.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <Ionicons name="trending-up-outline" size={80} color={colors.text.secondary} />
            <Text style={[styles.emptyStateTitle, { color: colors.text.primary }]}>
              Henüz Simülasyon
            </Text>
            <Text style={[styles.emptyStateSubtitle, { color: colors.text.secondary }]}>
              İlk yatırım simülasyonunuzu oluşturmak için yukarıdaki + butonuna dokunun
            </Text>
          </View>
        ) : (
          <View style={styles.simulationsList}>
            {simulations.map((simulation, index) => (
              <View key={index} style={[styles.simulationCard, { backgroundColor: colors.background.secondary }]}>
                <View style={styles.cardHeader}>
                  <View style={styles.assetInfo}>
                    <Ionicons 
                      name={getAssetIcon(simulation.asset)} 
                      size={24} 
                      color={colors.primary[500]} 
                    />
                    <View style={styles.assetDetails}>
                      <Text style={[styles.assetName, { color: colors.text.primary }]}>
                        {simulation.asset}
                      </Text>
                      <Text style={[styles.simulationDate, { color: colors.text.secondary }]}>
                        {formatDate(simulation.start_date)}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.cardActions}>
                    <View style={styles.performanceInfo}>
                      <Text style={[styles.growthRate, { color: getGrowthColor(simulation.growth_rate_percent) }]}>
                        {formatGrowthRate(simulation.growth_rate_percent)}
                      </Text>
                    </View>
                    <TouchableOpacity onPress={() => Alert.alert('İşlem', 'Ne yapmak istiyorsunuz?', [
                      { text: 'İptal', style: 'cancel' },
                      simulation.id ? { text: 'Sil', style: 'destructive', onPress: () => investmentService.deleteSimulation(simulation.id!).then(loadSimulations).catch(() => Alert.alert('Hata', 'Silme sırasında bir hata oluştu')) } : undefined,
                    ].filter(Boolean) as any)}>
                      <Text style={[styles.menuDots, { color: colors.text.secondary }]}>⋯</Text>
                    </TouchableOpacity>
                  </View>
                </View>
                
                <View style={styles.cardDetails}>
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: colors.text.secondary }]}>
                      Yatırım:
                    </Text>
                    <Text style={[styles.detailValue, { color: colors.text.primary }]}>
                      {formatCurrency(simulation.amount_invested)}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: colors.text.secondary }]}>
                      Mevcut Değer:
                    </Text>
                    <Text style={[styles.detailValue, { color: colors.text.primary }]}>
                      {formatCurrency(simulation.current_value)}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: colors.text.secondary }]}>
                      Kar/Zarar:
                    </Text>
                    <Text style={[styles.detailValue, { color: getGrowthColor(simulation.profit) }]}>
                      {formatCurrency(simulation.profit)}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
      </KeyboardAvoidingView>
      </View>
    </PremiumGate>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: typography.sizes.title1,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  whatIfButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6,
  },
  whatIfButtonCenter: {
    alignItems: 'center',
  },
  whatIfButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  emptyStateContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: typography.sizes.title2,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: typography.sizes.body,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 32,
  },
  loginButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  loginButtonText: {
    fontSize: typography.sizes.subhead,
    fontWeight: '600',
  },
  simulationsList: {
    paddingBottom: 24,
  },
  simulationCard: {
    marginTop: 12,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  assetInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  assetDetails: {
    marginLeft: 12,
  },
  assetName: {
    fontSize: typography.sizes.title3,
    fontWeight: '600',
  },
  simulationDate: {
    fontSize: typography.sizes.caption1,
    marginTop: 2,
  },
  performanceInfo: {
    alignItems: 'flex-end',
  },
  growthRate: {
    fontSize: typography.sizes.title3,
    fontWeight: '700',
  },
  cardDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: typography.sizes.subhead,
  },
  detailValue: {
    fontSize: typography.sizes.subhead,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    right: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 8,
  },
  whatIfContainer: {
    marginBottom: 12,
    alignItems: 'center',
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  menuDots: {
    fontSize: 18,
    ...typography.styles.body,
  },
});

export default InvestmentScreen;
