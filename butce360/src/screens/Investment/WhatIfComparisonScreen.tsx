import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  StyleSheet,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Share,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { useThemeState } from '../../hooks/useThemedStyles';
import DateTimePicker from '@react-native-community/datetimepicker';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import {
  investmentService,
  SUPPORTED_ASSETS,
  WhatIfComparisonRequest,
  WhatIfComparisonResponse,
} from '../../services/investmentService';
import { formatCurrency } from '../../utils/formatters';
interface SelectedAsset {
  id: string;
  name: string;
  selected: boolean;
}

const WhatIfComparisonScreen: React.FC = () => {
  const colors = useThemedColors();
  const { isDark } = useThemeState();
  const insets = useSafeAreaInsets();

  // State
  const [amount, setAmount] = useState<string>('10000');
  const [selectedDate, setSelectedDate] = useState<string>(() => {
    const date = new Date();
    date.setFullYear(date.getFullYear() - 1); // 1 year ago
    return date.toISOString().split('T')[0];
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [result, setResult] = useState<WhatIfComparisonResponse | null>(null);
  const [sortBy, _setSortBy] = useState<'percent' | 'profit' | 'value' | 'az'>('percent');
  const [filterClass, _setFilterClass] = useState<'all' | 'crypto' | 'equity' | 'commodity' | 'fx'>('all');
  const [isDatePickerOpen, setDatePickerOpen] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(new Date());

  // Asset selection
  const [assets, setAssets] = useState<SelectedAsset[]>(
    SUPPORTED_ASSETS.map(asset => ({
      id: asset.id,
      name: asset.name,
      selected: asset.id === 'BTC' || asset.id === 'ETH' || asset.id === 'GOLD' // Default selection
    }))
  );

  const toggleAssetSelection = useCallback((assetId: string) => {
    setAssets(prev =>
      prev.map(asset =>
        asset.id === assetId
          ? { ...asset, selected: !asset.selected }
          : asset
      )
    );
  }, []);

  const validateInputs = useCallback((): boolean => {
    const selectedAssets = assets.filter(asset => asset.selected);

    if (selectedAssets.length === 0) {
      Alert.alert('Hata', 'En az bir varlık seçmelisiniz');
      return false;
    }

    if (selectedAssets.length < 2) {
      Alert.alert('Hata', 'Karşılaştırma için en az 2 varlık seçmelisiniz');
      return false;
    }

    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      Alert.alert('Hata', 'Geçerli bir yatırım tutarı giriniz');
      return false;
    }

    const selectedDateObj = new Date(selectedDate);
    if (selectedDateObj >= new Date()) {
      Alert.alert('Hata', 'Geçmiş bir tarih seçmelisiniz');
      return false;
    }

    return true;
  }, [assets, amount, selectedDate]);

  const handleComparison = useCallback(async () => {
    if (!validateInputs()) return;

    setLoading(true);
    try {
      const selectedAssets = assets.filter(asset => asset.selected);
      const amountValue = parseFloat(amount);

      const request: WhatIfComparisonRequest = {
        title: `${selectedAssets.map(a => a.name).join(' vs ')} Karşılaştırması`,
        assets: selectedAssets.map(asset => asset.id),
        amounts: selectedAssets.map(() => amountValue), // Her varlık için aynı tutar
        date: selectedDate
      };

      const response = await investmentService.whatIfComparison(request);
      setResult(response);
    } catch (error: any) {
      console.error('What-if comparison error:', error);
      Alert.alert('Hata', error.message || 'Karşılaştırma sırasında hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [validateInputs, assets, amount, selectedDate]);

  const formatPercentage = useCallback((value: number): string => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  }, []);

  const getPerformanceColor = useCallback((percentage: number): string => {
    if (percentage > 0) return colors.success[500];
    if (percentage < 0) return colors.error[500];
    return colors.text.primary;
  }, [colors]);

  const getAssetOptionStyle = useCallback((selected: boolean) => [
    styles.assetOption,
    {
      backgroundColor: selected ? `${colors.primary[500]}20` : 'transparent',
      borderColor: colors.border.primary
    }
  ], [colors]);

  const getAssetNameStyle = useCallback((selected: boolean) => [
    styles.assetName,
    {
      color: selected ? colors.primary[500] : colors.text.primary,
      fontWeight: selected ? ('bold' as const) : ('normal' as const)
    }
  ], [colors]);

  const handleShareResults = useCallback(async () => {
    if (!result) return;

    try {
      // Sadece varlık karşılaştırma detaylarını paylaş
      const assetComparisons = result.assets
        .filter(asset => asset.success)
        .map(asset => {
          return `📈 ${asset.asset_name}:
• Yatırım: ${formatCurrency(asset.amount_invested)} ${asset.currency}
• Mevcut Değer: ${formatCurrency(asset.current_value)} ${asset.currency}
• Kar/Zarar: ${formatCurrency(asset.profit)} ${asset.currency}
• Getiri: ${formatPercentage(asset.profit_percentage)}
• Yıllık Getiri: ${formatPercentage(asset.annualized_return)}`;
        })
        .join('\n\n');

      const shareText = `🚀 Bütçe360 Yatırım Karşılaştırması 🚀

📊 ${result.analysis.title}

${assetComparisons}

📱 Bütçe360 ile finansal geleceğinizi planlayın!
#Bütçe360 #YatırımAnalizi #FinansalPlanlama`;

      await Share.share({
        message: shareText,
        title: 'Bütçe360 Yatırım Karşılaştırması',
      });
    } catch (error) {
      console.error('Share error:', error);
      Alert.alert('Hata', 'Paylaşım sırasında bir hata oluştu');
    }
  }, [result, formatPercentage]);

  const renderAssetSelection = () => (
    <View style={[styles.section, { backgroundColor: colors.background.secondary }]}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
        Karşılaştırılacak Varlıklar
      </Text>
      <Text style={[styles.sectionSubtitle, { color: colors.text.secondary }]}>
        (En az 2 varlık seçiniz)
      </Text>
      {assets.map((asset) => (
        <TouchableOpacity
          key={asset.id}
          style={getAssetOptionStyle(asset.selected)}
          onPress={() => toggleAssetSelection(asset.id)}
        >
          <Ionicons
            name={asset.selected ? 'checkbox' : 'square-outline'}
            size={24}
            color={asset.selected ? colors.primary[500] : colors.text.secondary}
          />
          <Text style={getAssetNameStyle(asset.selected)}>
            {asset.name}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderInputs = () => (
    <View style={[styles.section, { backgroundColor: colors.background.secondary }]}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
        Yatırım Detayları
      </Text>

      <Input
        label="Yatırım Tutarı (TRY)"
        value={amount}
        onChangeText={setAmount}
        keyboardType="numeric"
        placeholder="10000"
        variant="outlined"
        inputStyle={{ ...styles.dateInputStyle, backgroundColor: colors.background.secondary }}
      />

      <View style={styles.dateInputContainer}>
        <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
          Yatırım Tarihi
        </Text>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => { setTempDate(new Date(selectedDate)); setDatePickerOpen(true); }}
          style={[styles.dateButton, { borderColor: (colors as any).neutral?.[900] || '#000', backgroundColor: colors.background.secondary }]}
        >
          <Text style={[styles.dateButtonText, { color: colors.text.primary }]}>{selectedDate}</Text>
        </TouchableOpacity>
        <Text style={[styles.dateHelpText, { color: colors.text.secondary }]}>
          Format: YYYY-MM-DD (örn: 2023-01-15)
        </Text>
      </View>
    </View>
  );

  const renderResults = () => {
    if (!result) return null;

    // classify for filtering
    const classMap: Record<string, 'crypto' | 'equity' | 'commodity' | 'fx'> = {
      BTC: 'crypto',
      ETH: 'crypto',
      GOLD: 'commodity',
      USDTRY: 'fx',
      BIST100: 'equity',
    };

    // sort + filter
    let list = result.assets.slice();
    if (filterClass !== 'all') {
      list = list.filter(a => classMap[a.asset] === filterClass);
    }
    switch (sortBy) {
      case 'profit':
        list.sort((a, b) => b.profit - a.profit);
        break;
      case 'value':
        list.sort((a, b) => b.current_value - a.current_value);
        break;
      case 'az':
        list.sort((a, b) => a.asset_name.localeCompare(b.asset_name));
        break;
      case 'percent':
      default:
        list.sort((a, b) => b.profit_percentage - a.profit_percentage);
    }

    return (
      <View style={[styles.section, { backgroundColor: colors.background.secondary }]}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          {result.analysis.title}
        </Text>

        {/* Analysis Summary */}
        <View style={[styles.analysisCard, { backgroundColor: colors.background.secondary }]}>
          <Text style={[styles.analysisSummary, { color: colors.text.primary }]}>
            {result.analysis.summary}
          </Text>
        </View>



        {/* Best/Worst Performers */}
        <View style={styles.performanceContainer}>
          <View style={[styles.performanceCard, { backgroundColor: colors.success[500] + '20' }]}>
            <Ionicons name="trending-up" size={20} color={colors.success[500]} />
            <Text style={[styles.performanceLabel, { color: colors.success[500] }]}>
              En İyi Performans
            </Text>
            <Text style={[styles.performanceAsset, { color: colors.text.primary }]}>
              {result.performance.best_performer.asset_name}
            </Text>
            <Text style={[styles.performanceValue, { color: colors.success[500] }]}>
              {formatPercentage(result.performance.best_performer.profit_percentage)}
            </Text>
          </View>

          <View style={[styles.performanceCard, { backgroundColor: colors.error[500] + '20' }]}>
            <Ionicons name="trending-down" size={20} color={colors.error[500]} />
            <Text style={[styles.performanceLabel, { color: colors.error[500] }]}>
              En Kötü Performans
            </Text>
            <Text style={[styles.performanceAsset, { color: colors.text.primary }]}>
              {result.performance.worst_performer.asset_name}
            </Text>
            <Text style={[styles.performanceValue, { color: colors.error[500] }]}>
              {formatPercentage(result.performance.worst_performer.profit_percentage)}
            </Text>
          </View>
        </View>

        {/* Analysis Insights */}
        <View style={[styles.analysisCard, { backgroundColor: colors.background.primary }]}>
          <Text style={[styles.analysisTitle, { color: colors.text.primary }]}>
            Analiz ve Öneriler
          </Text>

          {result.analysis.insights.map((insight, index) => (
            <View key={index} style={styles.insightRow}>
              <Ionicons name="bulb-outline" size={16} color={colors.primary[500]} />
              <Text style={[styles.insightText, { color: colors.text.primary }]}>
                {insight}
              </Text>
            </View>
          ))}

          <View style={[styles.riskCard, { backgroundColor: colors.warning[500] + '20' }]}>
            <Text style={[styles.riskTitle, { color: colors.warning[700] }]}>
              Risk Değerlendirmesi
            </Text>
            <Text style={[styles.riskText, { color: colors.text.primary }]}>
              {result.analysis.risk_assessment}
            </Text>
          </View>

          <View style={[styles.recommendationCard, { backgroundColor: colors.primary[500] + '20' }]}>
            <Text style={[styles.recommendationTitle, { color: colors.primary[700] }]}>
              Öneri
            </Text>
            <Text style={[styles.recommendationText, { color: colors.text.primary }]}>
              {result.analysis.recommendation}
            </Text>
          </View>
        </View>

        {/* Asset Details */}
        <Text style={[styles.detailsTitle, { color: colors.text.primary }]}>
          Varlık Detayları
        </Text>

        {list.map((asset) => (
            <View key={asset.asset} style={[styles.assetCard, { backgroundColor: colors.background.secondary }]}>
              <View style={styles.assetHeader}>
                <View>
                  <Text style={[styles.assetTitle, { color: colors.text.primary }]}>
                    {asset.asset_name}
                  </Text>
                  <Text style={[styles.assetSubtitle, { color: colors.text.secondary }]}>
                    Risk: {asset.risk_level} | Volatilite: {asset.volatility_score.toFixed(1)}/10
                  </Text>
                </View>
                <Text style={[
                  styles.assetGrowth,
                  { color: getPerformanceColor(asset.profit_percentage) }
                ]}>
                  {formatPercentage(asset.profit_percentage)}
                </Text>
              </View>

              {asset.success ? (
                <>
                  <View style={styles.assetRow}>
                    <Text style={[styles.assetLabel, { color: colors.text.secondary }]}>
                      Yatırım:
                    </Text>
                    <Text style={[styles.assetValue, { color: colors.text.primary }]}>
                      {formatCurrency(asset.amount_invested)} {asset.currency}
                    </Text>
                  </View>

                  <View style={styles.assetRow}>
                    <Text style={[styles.assetLabel, { color: colors.text.secondary }]}>
                      Mevcut Değer:
                    </Text>
                    <Text style={[styles.assetValue, { color: colors.text.primary }]}>
                      {formatCurrency(asset.current_value)} {asset.currency}
                    </Text>
                  </View>

                  <View style={styles.assetRow}>
                    <Text style={[styles.assetLabel, { color: colors.text.secondary }]}>
                      Kar/Zarar:
                    </Text>
                    <Text style={[
                      styles.assetValue,
                      { color: getPerformanceColor(asset.profit) }
                    ]}>
                      {formatCurrency(asset.profit)} {asset.currency}
                    </Text>
                  </View>

                  <View style={styles.assetRow}>
                    <Text style={[styles.assetLabel, { color: colors.text.secondary }]}>
                      Yıllık Getiri:
                    </Text>
                    <Text style={[
                      styles.assetValue,
                      { color: getPerformanceColor(asset.annualized_return) }
                    ]}>
                      {formatPercentage(asset.annualized_return)}
                    </Text>
                  </View>

                  <View style={styles.assetRow}>
                    <Text style={[styles.assetLabel, { color: colors.text.secondary }]}>
                      Adet:
                    </Text>
                    <Text style={[styles.assetValue, { color: colors.text.primary }]}>
                      {asset.units_bought.toFixed(6)}
                    </Text>
                  </View>
                </>
              ) : (
                <Text style={[styles.errorText, { color: colors.error[500] }]}>
                  {asset.error || 'Veri alınamadı'}
                </Text>
              )}
            </View>
          ))}

        {/* Share Button */}
        <TouchableOpacity
          style={[styles.shareButton, { backgroundColor: colors.primary[500] }]}
          onPress={handleShareResults}
          activeOpacity={0.8}
        >
          <Ionicons name="share-outline" size={20} color={colors.background.secondary} />
          <Text style={[styles.shareButtonText, { color: colors.background.secondary }]}>
            Sonuçları Paylaş
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar backgroundColor={colors.background.primary} barStyle={isDark ? 'light-content' : 'dark-content'} />

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={insets.top}
      >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.scrollViewContent, { paddingBottom: insets.bottom + 24 }]}
        showsVerticalScrollIndicator={false}
      >
        {/* Content */}
        <View style={styles.content}>
          {renderAssetSelection()}
          {renderInputs()}

          <Button
            title={loading ? "Hesaplanıyor..." : "Karşılaştır"}
            onPress={handleComparison}
            disabled={loading}
            style={styles.compareButton}
          />

          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary[500]} />
              <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
                Karşılaştırma yapılıyor...
              </Text>
            </View>
          )}

   

          {renderResults()}
        </View>
      </ScrollView>
      </KeyboardAvoidingView>
      {isDatePickerOpen && (
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.background.secondary }]}>
            <Text style={[styles.sectionTitle, styles.modalTitleMargin, { color: colors.text.primary }]}>Yatırım Tarihi</Text>
            <DateTimePicker
              value={tempDate}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              themeVariant={isDark ? 'dark' : 'light'}
              textColor={isDark ? '#fff' : '#000'}
              onChange={(_e, d) => { if (d) setTempDate(d); }}
            />
            <View style={styles.modalButtonContainer}>
              <TouchableOpacity onPress={() => setDatePickerOpen(false)}>
                <Text style={[styles.modalCancelButton, { color: colors.text.secondary }]}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => {
                const y = tempDate.getFullYear();
                const m = String(tempDate.getMonth() + 1).padStart(2, '0');
                const d = String(tempDate.getDate()).padStart(2, '0');
                setSelectedDate(`${y}-${m}-${d}`);
                setDatePickerOpen(false);
              }}>
                <Text style={[styles.modalConfirmButton, { color: colors.primary[500] }]}>Tamam</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 16,
  },
  assetOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  assetOptionSelected: {
    backgroundColor: 'transparent',
  },
  assetName: {
    marginLeft: 12,
    fontSize: 16,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  helpText: {
    fontSize: 12,
    marginTop: 4,
  },
  compareButton: {
    marginBottom: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 16,
  },
  summaryCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  performanceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  performanceCard: {
    flex: 0.48,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  performanceLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 4,
  },
  performanceAsset: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 4,
  },
  performanceValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 2,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  assetCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  assetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  assetTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  assetGrowth: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  assetRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  assetLabel: {
    fontSize: 14,
  },
  assetValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  errorText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  analysisCard: {
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  analysisSummary: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    fontWeight: '500',
  },
  analysisTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  insightRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingHorizontal: 8,
  },
  insightText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    lineHeight: 20,
  },
  riskCard: {
    borderRadius: 8,
    padding: 12,
    marginTop: 16,
    marginBottom: 8,
  },
  riskTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  riskText: {
    fontSize: 14,
    lineHeight: 20,
  },
  recommendationCard: {
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  recommendationText: {
    fontSize: 14,
    lineHeight: 20,
  },
  assetSubtitle: {
    fontSize: 12,
    marginTop: 2,
  },
  // Sort & filter UI
  sortFilterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  chip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: StyleSheet.hairlineWidth,
    marginRight: 8,
  },
  separator: { width: 12 },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 16,
    marginHorizontal: 16,
  },
  shareButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  dateInput: {
    backgroundColor: 'transparent',
  },
  dateInputStyle: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  dateInputContainer: {
    marginTop: 16,
  },
  dateButton: {
    borderWidth: 1,
    borderColor: '#000',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
  },
  dateButtonText: {
    color: '#000',
  },
  dateHelpText: {
    color: '#666',
    marginTop: 6,
  },
  scrollViewContent: {
    paddingBottom: 0,
  },
  modalOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: '#00000080',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    borderRadius: 12,
    backgroundColor: '#fff',
    padding: 16,
  },
  modalTitleMargin: {
    marginBottom: 8,
  },
  modalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
    gap: 12,
  },
  modalCancelButton: {
    color: '#666',
  },
  modalConfirmButton: {
    color: '#007AFF',
    fontWeight: '600',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
});

export default WhatIfComparisonScreen;
