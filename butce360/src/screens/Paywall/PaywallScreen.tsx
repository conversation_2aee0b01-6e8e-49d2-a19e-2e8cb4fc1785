import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  Alert,
  ScrollView,
  StatusBar,
  Linking,
  TouchableOpacity
} from 'react-native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useRevenueCat } from '../../providers/RevenueCatProvider';
import { usePremium } from '../../context/PremiumContext';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { spacing } from '../../theme/spacing';
import { typography } from '../../theme/typography';
import Button from '../../components/common/Button';

type RootStackParamList = {
  Menu: undefined;
  home: undefined;
};

export default function PaywallScreen() {
  const { offerings, purchasePackage, customerInfo } = useRevenueCat();
  const { checkPremiumStatus } = usePremium();
  const [monthlyPkg, setMonthlyPkg] = useState<any>(null);
  const [yearlyPkg, setYearlyPkg] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const colors = useThemedColors();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  useEffect(() => {
    console.log('PaywallScreen loaded');
    console.log('Offerings:', offerings);
    if (offerings?.current?.availablePackages?.length) {
      console.log('Available packages:', offerings.current.availablePackages);

      // Aylık paketi bul
      const monthlyFound = offerings.current.availablePackages.find(
        (p: any) => p.product.identifier === 'com.butce360.app.one.month'
      );
      setMonthlyPkg(monthlyFound);

      // Yıllık paketi bul
      const yearlyFound = offerings.current.availablePackages.find(
        (p: any) => p.product.identifier === 'com.butce360.app.one.year'
      );
      setYearlyPkg(yearlyFound);
    }
  }, [offerings]);

  const handlePurchase = async (packageType: 'monthly' | 'yearly') => {
    const pkg = packageType === 'monthly' ? monthlyPkg : yearlyPkg;
    if (!pkg) {
      Alert.alert('Ürün bulunamadı', `${packageType === 'monthly' ? 'Aylık' : 'Yıllık'} paket RevenueCat tarafında gelmedi.`);
      return;
    }
    setLoading(true);
    const result = await purchasePackage(pkg);
    setLoading(false);

    if (result.success) {
      // Refresh premium status after successful purchase
      await checkPremiumStatus();

      Alert.alert('Başarılı 🎉', 'Abonelik aktif edildi!', [
        { text: 'Tamam', onPress: () => navigation.navigate('Menu') }
      ]);
    } else if (result.cancelled) {
      Alert.alert('İptal edildi', 'Kullanıcı satın almayı iptal etti.');
    } else {
      Alert.alert('Hata', result.error?.message || 'Bilinmeyen hata');
    }
  };

  // Aktif entitlement var mı kontrol et (RevenueCat dashboard'daki entitlement adına göre)
  const isPremiumActive = !!customerInfo?.entitlements?.active?.premium;

  const styles = createStyles(colors);

  return (
    <View
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <StatusBar
        barStyle={
          colors.background.primary === '#1c1c1e'
            ? 'light-content'
            : 'dark-content'
        }
        backgroundColor={colors.background.primary}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Premium Benefits Section */}
        <View style={styles.benefitsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Premium Özellikleri
          </Text>
          <View
            style={[
              styles.benefitsCard,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            <View style={styles.benefitItem}>
              <Text style={styles.benefitIcon}>📈</Text>
              <Text
                style={[styles.benefitText, { color: colors.text.primary }]}
              >
                Yatırımlarınızı akıllıca karşılaştırın
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitIcon}>📑</Text>
              <Text
                style={[styles.benefitText, { color: colors.text.primary }]}
              >
                Kredi kartı ekstrenizden işlemleri otomatik çıkarın
              </Text>
            </View>
          </View>
        </View>

        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary[500]} />
            <Text
              style={[styles.loadingText, { color: colors.text.secondary }]}
            >
              Paketler yükleniyor...
            </Text>
          </View>
        )}

        {!loading && (monthlyPkg || yearlyPkg) && (
          <View style={styles.packagesSection}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              Abonelik Seçenekleri
            </Text>

            {yearlyPkg && (
              <View
                style={[
                  styles.packageCard,
                  styles.recommendedCard,
                  { backgroundColor: colors.background.secondary },
                ]}
              >
                <View style={styles.recommendedBadge}>
                  <Text style={styles.recommendedText}>ÖNERİLEN</Text>
                </View>
                <Text
                  style={[styles.packageTitle, { color: colors.text.primary }]}
                >
                  Yıllık Abonelik
                </Text>
                <Text
                  style={[styles.packagePrice, { color: colors.primary[500] }]}
                >
                  {yearlyPkg.product.priceString}
                </Text>
                <Text
                  style={[
                    styles.packageDescription,
                    { color: colors.text.secondary },
                  ]}
                >
                  Aylık{' '}
                  {Math.round(
                    parseFloat(
                      yearlyPkg.product.priceString
                        .replace(/[^\d.,]/g, '')
                        .replace(',', '.'),
                    ) / 12,
                  )}{' '}
                  TL
                </Text>
                <Button
                  title={`Yıllık Satın Al (${yearlyPkg.product.priceString})`}
                  onPress={() => handlePurchase('yearly')}
                  variant="primary"
                  fullWidth
                  disabled={loading}
                  style={styles.purchaseButton}
                />
              </View>
            )}

            {monthlyPkg && (
              <View
                style={[
                  styles.packageCard,
                  { backgroundColor: colors.background.secondary },
                ]}
              >
                <Text
                  style={[styles.packageTitle, { color: colors.text.primary }]}
                >
                  Aylık Abonelik
                </Text>
                <Text
                  style={[styles.packagePrice, { color: colors.primary[500] }]}
                >
                  {monthlyPkg.product.priceString}
                </Text>
                <Text
                  style={[
                    styles.packageDescription,
                    { color: colors.text.secondary },
                  ]}
                >
                  Aylık faturalandırma
                </Text>
                <Button
                  title={`Aylık Satın Al (${monthlyPkg.product.priceString})`}
                  onPress={() => handlePurchase('monthly')}
                  variant="outline"
                  fullWidth
                  disabled={loading}
                  style={styles.purchaseButton}
                />
              </View>
            )}

            {/* Terms of Service */}
            <View style={styles.termsContainer}>
              <Text style={[styles.termsText, { color: colors.text.secondary }]}>
                Abone olarak{' '}
                <TouchableOpacity
                  onPress={() => Linking.openURL('https://www.apple.com/legal/internet-services/itunes/dev/stdeula/')}
                  style={styles.linkButton}
                >
                  <Text style={[styles.linkText, { color: colors.primary[500] }]}>
                    Alıcı Hizmet Şartları
                  </Text>
                </TouchableOpacity>
                'nı kabul etmiş olursun. Abonelikler, iptal edilene kadar otomatik olarak yenilenir. Ek ücretleri önlemek için yenileme işleminden en az 24 saat önce{' '}
                <TouchableOpacity
                  onPress={() => Linking.openURL('https://www.apple.com/legal/internet-services/itunes/tr/terms.html')}
                  style={styles.linkButton}
                >
                  <Text style={[styles.linkText, { color: colors.primary[500] }]}>
                    Dilediğin zaman iptal et
                  </Text>
                </TouchableOpacity>
                . Aboneliğini, abone olduğun platform üzerinden yönet.
              </Text>
            </View>
          </View>
        )}

        {!loading && !monthlyPkg && !yearlyPkg && (
          <View
            style={[
              styles.errorCard,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            <Text style={[styles.errorText, { color: colors.error[500] }]}>
              Ürünler getirilirken sorun yaşandı!
            </Text>
          </View>
        )}

        {/* Status Section */}
        <View
          style={[
            styles.statusCard,
            { backgroundColor: colors.background.secondary },
          ]}
        >
          <Text style={[styles.statusTitle, { color: colors.text.primary }]}>
            Abonelik Durumu
          </Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusIcon}>
              {isPremiumActive ? '✅' : '❌'}
            </Text>
            <Text
              style={[
                styles.statusText,
                {
                  color: isPremiumActive
                    ? colors.success[500]
                    : colors.text.secondary,
                },
              ]}
            >
              {isPremiumActive ? 'Premium Aktif' : 'Premium Pasif'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },



  // ScrollView
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.screenPadding,
    paddingBottom: spacing['4xl'],
  },

  // Benefits Section
  benefitsSection: {
    marginBottom: spacing['3xl'],
  },
  sectionTitle: {
    ...typography.styles.h4,
    marginBottom: spacing.lg,
    marginTop: spacing.xl,
  },
  benefitsCard: {
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  benefitIcon: {
    fontSize: 20,
    marginRight: spacing.md,
    width: 24,
  },
  benefitText: {
    ...typography.styles.body,
    flex: 1,
  },

  // Loading
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: spacing['3xl'],
    borderRadius: spacing.cardRadius,
  },
  loadingText: {
    ...typography.styles.body2,
    marginTop: spacing.md,
  },

  // Packages Section
  packagesSection: {
    marginBottom: spacing['3xl'],
    borderRadius: spacing.cardRadius,
  },
  packageCard: {
    backgroundColor: colors.surface.primary,
    borderRadius: spacing.cardRadius,
    padding: spacing.xl,
    marginBottom: spacing.lg,
    alignItems: 'center',
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  recommendedCard: {
    backgroundColor: colors.surface.primary,
    borderRadius: spacing.cardRadius,
    padding: spacing.xl,
    marginBottom: spacing.lg,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.primary[500],
    position: 'relative',
    shadowColor: colors.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  recommendedBadge: {
    position: 'absolute',
    top: -8,
    backgroundColor: colors.primary[500],
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    borderRadius: spacing.xs,
  },
  recommendedText: {
    ...typography.styles.caption,
    color: colors.surface.primary,
    fontWeight: '600',
    fontSize: 10,
  },
  packageTitle: {
    ...typography.styles.h5,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  packagePrice: {
    ...typography.styles.currencyLarge,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  packageDescription: {
    ...typography.styles.body2,
    marginBottom: spacing.xl,
    textAlign: 'center',
  },
  purchaseButton: {
    marginTop: spacing.md,
  },

  // Error
  errorCard: {
    padding: spacing.xl,
    alignItems: 'center',
    marginBottom: spacing['3xl'],
    borderRadius: spacing.cardRadius,
  },
  errorText: {
    ...typography.styles.body,
    textAlign: 'center',
  },

  // Status
  statusCard: {
    padding: spacing.xl,
    alignItems: 'center',
    borderRadius: spacing.cardRadius,
  },
  statusTitle: {
    ...typography.styles.h6,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIcon: {
    fontSize: 20,
    marginRight: spacing.sm,
  },
  statusText: {
    ...typography.styles.body,
  },

  // Terms of Service
  termsContainer: {
    marginTop: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  termsText: {
    ...typography.styles.caption,
    textAlign: 'center',
    lineHeight: 18,
  },
  linkButton: {
    // Inline style for touchable text
  },
  linkText: {
    ...typography.styles.caption,
    textDecorationLine: 'underline',
    fontWeight: '500',
  },
});
