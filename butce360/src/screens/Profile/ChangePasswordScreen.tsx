import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  StatusBar,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useThemedColors, useThemeState } from "../../hooks/useThemedStyles";
import { typography } from '../../theme/typography';
import { useAuth } from '../../context/AuthContext';
import { getLoginTypeFromToken, getLoginTypeDescription } from '../../utils/jwt';
import { authService } from '../../services/authService';
import Input from '../../components/common/Input';
import Button from '../../components/common/Button';

const ChangePasswordScreen: React.FC = () => {
  const colors = useThemedColors();
  const { isDark } = useThemeState();
  const insets = useSafeAreaInsets();
  const styles = createStyles(colors);
  const navigation = useNavigation();
  const { changePassword } = useAuth();

  const [loginType, setLoginType] = useState<number | null>(null);
  const [loginTypeDescription, setLoginTypeDescription] = useState<string>('');

  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Token'dan LoginType'ı al
  useEffect(() => {
    const getLoginType = async () => {
      try {
        const token = await authService.getStoredToken();
        if (token) {
          const type = getLoginTypeFromToken(token);
          setLoginType(type);
          if (type) {
            setLoginTypeDescription(getLoginTypeDescription(type));
          }
        }
      } catch (error) {
        console.error('[ChangePasswordScreen] Error getting login type:', error);
      }
    };

    getLoginType();
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // OAuth kullanıcıları (Google/Apple) için current password kontrolü yapma
    const isOAuthUser = loginType === 2 || loginType === 3;

    if (!isOAuthUser && !formData.currentPassword) {
      newErrors.currentPassword = 'Mevcut şifre gerekli';
    }

    if (!formData.newPassword) {
      newErrors.newPassword = 'Yeni şifre gerekli';
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = 'Şifre en az 6 karakter olmalı';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Şifre tekrarı gerekli';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Şifreler eşleşmiyor';
    }

    // OAuth kullanıcıları için current password ile karşılaştırma yapma
    if (!isOAuthUser && formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = 'Yeni şifre mevcut şifreden farklı olmalı';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChangePassword = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // OAuth kullanıcıları için current password boş gönder
      const isOAuthUser = loginType === 2 || loginType === 3;
      const currentPasswordToSend = isOAuthUser ? '' : formData.currentPassword;

      await changePassword(currentPasswordToSend, formData.newPassword);

      Alert.alert(
        'Başarılı',
        'Şifreniz başarıyla değiştirildi',
        [{ text: 'Tamam', onPress: () => navigation.goBack() }]
      );
    } catch (error: any) {
      console.error('[ChangePasswordScreen] Password change error:', error);
      const errorMessage = error.message || 'Şifre değiştirilirken bir hata oluştu';
      Alert.alert('Hata', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} backgroundColor={colors.background.primary} />
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={insets.top}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[
            styles.scrollContent,
            { paddingBottom: Math.max(insets.bottom, 12) }
          ]}
          showsVerticalScrollIndicator={false}
        >
        {/* OAuth kullanıcıları için farklı açıklama */}
        {loginType === 2 || loginType === 3 ? (
          <View style={styles.oauthInfoCard}>
            <Text style={styles.oauthInfoTitle}>
              {loginTypeDescription} ile giriş yaptınız
            </Text>
            <Text style={styles.oauthInfoDescription}>
              Hesabınız için bir şifre tanımlayabilirsiniz. Bu şifre ile de giriş yapabileceksiniz.
            </Text>
          </View>
        ) : (
          <View style={styles.infoCard}>
            <Text style={styles.infoDescription}>
              Güvenliğiniz için şifrenizi düzenli olarak değiştirmenizi öneririz.
            </Text>
          </View>
        )}

        {/* Form Section */}
        <View style={styles.formCard}>
          {/* Current Password - Sadece normal kullanıcılar için göster */}
          {loginType === 1 && (
            <Input
              label="Mevcut Şifre"
              value={formData.currentPassword}
              onChangeText={(value) => handleInputChange('currentPassword', value)}
              placeholder="Mevcut şifrenizi girin"
              secureTextEntry
              error={errors.currentPassword}
              autoCapitalize="none"
              variant="outlined"
              containerStyle={styles.inputContainer}
            />
          )}

          {/* New Password */}
          <Input
            label="Yeni Şifre"
            value={formData.newPassword}
            onChangeText={(value) => handleInputChange('newPassword', value)}
            placeholder="Yeni şifrenizi girin"
            secureTextEntry
            error={errors.newPassword}
            autoCapitalize="none"
            variant="outlined"
            containerStyle={styles.inputContainer}
          />

          {/* Confirm Password */}
          <Input
            label="Yeni Şifre Tekrar"
            value={formData.confirmPassword}
            onChangeText={(value) => handleInputChange('confirmPassword', value)}
            placeholder="Yeni şifrenizi tekrar girin"
            secureTextEntry
            error={errors.confirmPassword}
            autoCapitalize="none"
            variant="outlined"
            containerStyle={styles.inputContainer}
          />
        </View>

        {/* Password Requirements */}
        <View style={styles.requirementsCard}>
          <Text style={styles.requirementsTitle}>Şifre Gereksinimleri:</Text>
          <Text style={styles.requirementItem}>• En az 6 karakter</Text>
          {loginType === 1 && (
            <Text style={styles.requirementItem}>• Mevcut şifreden farklı olmalı</Text>
          )}
          <Text style={styles.requirementItem}>• Güçlü şifre için harf, rakam ve özel karakter kullanın</Text>
        </View>

        {/* Change Password Button */}
        <Button
          title={isLoading
            ? (loginType === 2 || loginType === 3 ? 'Tanımlanıyor...' : 'Değiştiriliyor...')
            : (loginType === 2 || loginType === 3 ? 'Şifre Tanımla' : 'Şifreyi Değiştir')
          }
          onPress={handleChangePassword}
          disabled={isLoading}
          loading={isLoading}
          fullWidth
          style={styles.submitButton}
        />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },

  // ScrollView - Simulasyon sayfası gibi
  scrollView: {
    flex: 1,
    padding: 20, // Simulasyon sayfası gibi
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 8,
  },

  // Info Cards - Simulasyon sayfası section tasarımı
  infoCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  infoDescription: {
    ...typography.styles.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },

  oauthInfoCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: colors.primary[200],
  },
  oauthInfoTitle: {
    ...typography.styles.headline,
    fontWeight: '600',
    color: colors.primary[600],
    textAlign: 'center',
    marginBottom: 12,
  },
  oauthInfoDescription: {
    ...typography.styles.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },

  // Form - Simulasyon sayfası section tasarımı
  formCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 16,
  },

  // Requirements - Simulasyon sayfası section tasarımı
  requirementsCard: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  requirementsTitle: {
    ...typography.styles.headline,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 12,
  },
  requirementItem: {
    ...typography.styles.body1,
    color: colors.text.secondary,
    marginBottom: 8,
    lineHeight: 20,
  },

  // Button - Simulasyon sayfası gibi
  submitButton: {
    paddingVertical: 16,
    borderRadius: 8,
    marginTop: 12,
    marginBottom: 40,
  },
});

export default ChangePasswordScreen;
