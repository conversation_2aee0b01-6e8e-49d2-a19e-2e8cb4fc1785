import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
} from 'react-native';
import { useAuth } from '../../hooks/useAuth';
import { useNavigation } from '@react-navigation/native';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { spacing } from '../../theme/spacing';

interface EditProfileScreenProps {
  onNavigate?: (screen: string) => void;
}

const EditProfileScreen: React.FC<EditProfileScreenProps> = ({ onNavigate }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  const navigation = useNavigation<any>();
  const { state: authState, updateProfile } = useAuth();
  
  const [formData, setFormData] = useState({
    name: authState?.user?.name || '',
    email: authState?.user?.email || '',
    phone: '',
    bio: '',
  });
  
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = useCallback(async () => {
    if (isLoading) return;
    setIsLoading(true);

    try {
      await updateProfile({
        name: formData.name?.trim() || authState?.user?.name,
        email: formData.email?.trim() || authState?.user?.email,
      });

      Alert.alert(
        'Başarılı',
        'Profil bilgileriniz güncellendi',
        [{ text: 'Tamam', onPress: () => onNavigate?.('ProfileDetails') }]
      );
    } catch (error: any) {
      Alert.alert('Hata', error?.message || 'Profil güncellenirken bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, updateProfile, formData.name, formData.email, authState?.user?.name, authState?.user?.email, onNavigate]);

  const saveRef = useRef<() => void>(() => {});
  // Always keep latest handler
  useEffect(() => {
    saveRef.current = handleSave;
  }, [handleSave]);

  // Set a stable onSave once; it calls latest via ref to avoid re-renders
  useEffect(() => {
    navigation.setParams({ onSave: () => saveRef.current() });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Removed duplicate setParams to prevent re-render loops

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'} backgroundColor={colors.background.primary} />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {/* Form Fields */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Ad Soyad</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Adınızı ve soyadınızı girin"
              placeholderTextColor="#666"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>E-posta</Text>
            <TextInput
              style={styles.input}
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              placeholder="E-posta adresinizi girin"
              placeholderTextColor="#666"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Telefon</Text>
            <TextInput
              style={styles.input}
              value={formData.phone}
              onChangeText={(value) => handleInputChange('phone', value)}
              placeholder="Telefon numaranızı girin"
              placeholderTextColor="#666"
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Hakkımda</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.bio}
              onChangeText={(value) => handleInputChange('bio', value)}
              placeholder="Kendiniz hakkında kısa bir açıklama yazın"
              placeholderTextColor="#666"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>


      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  // header removed; using stack NavigationHeader with right action
  content: {
    flex: 1,
  },

  formSection: {
    padding: spacing.lg,
    backgroundColor: colors.background.primary,
  },
  inputGroup: {
    marginBottom: spacing.lg,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border.primary,
    borderRadius: spacing.sm,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: 16,
    color: colors.text.primary,
    backgroundColor: colors.background.secondary,
  },
  textArea: {
    height: 100,
    paddingTop: spacing.md,
  },
});

export default EditProfileScreen;
