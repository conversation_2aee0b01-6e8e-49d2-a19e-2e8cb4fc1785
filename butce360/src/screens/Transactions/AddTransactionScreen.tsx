import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Switch,
  ActionSheetIOS,
  Alert,
} from "react-native";
import { SafeAreaView } from 'react-native-safe-area-context';
import DateTimePicker from "@react-native-community/datetimepicker";
import { categoryService } from "../../services/categoryService";
import { accountService } from "../../services/accountService";
import { Category, Account } from "../../types/models";
import { transactionService } from "../../services/transactionService";
import { useThemedColors, useThemeState } from "../../hooks/useThemedStyles";
import { typography } from "../../theme/typography";

// Interface for individual transaction in bulk mode
interface BulkTransaction {
  id: string;
  title: string;
  amount: string;
  note: string;
  date: Date;
  type: "income" | "expense";
  category: Category | null;
  account: Account | null;
}

interface AddTransactionScreenProps {
  onNavigate?: (screen: string) => void;
}

export default function AddTransactionScreen({ onNavigate }: AddTransactionScreenProps) {
  // Theme colors
  const colors = useThemedColors();
  const { isDark } = useThemeState();

  // Single transaction state
  const [title, setTitle] = useState("");
  const [amount, setAmount] = useState("");
  const [note, setNote] = useState("");
  const [date, setDate] = useState(new Date());
  const [isDatePickerOpen, setDatePickerOpen] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(new Date());

  const [type, setType] = useState<"income" | "expense">("expense");
  const [category, setCategory] = useState<Category | null>(null);
  const [account, setAccount] = useState<Account | null>(null);

  const [_autoPayment, _setAutoPayment] = useState(false);

  // Categories state
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Accounts state
  const [accounts, setAccounts] = useState<Account[]>([]);

  // Advanced mode toggle
  const [showAdvanced, setShowAdvanced] = useState(false);
  // Recurring controls
  const [isRecurring, setIsRecurring] = useState(false);
  const [interval, setInterval] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('monthly');
  const [dayOfMonth, setDayOfMonth] = useState<number>(new Date().getDate());

  // Bulk mode state
  const [bulkTransactions, setBulkTransactions] = useState<BulkTransaction[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  const loadCategories = useCallback(async () => {
    try {
      setLoadingCategories(true);
      const fetchedCategories = await categoryService.getCategories();
      setCategories(fetchedCategories[type] || []);
    } catch (error) {
      console.error('[AddTransaction] Error loading categories:', error);
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu');
    } finally {
      setLoadingCategories(false);
    }
  }, [type]);

  const loadAccounts = useCallback(async () => {
    try {
      const fetchedAccounts = await accountService.getAccounts();
      setAccounts(fetchedAccounts);
    } catch (error) {
      console.error('[AddTransaction] Error loading accounts:', error);
      Alert.alert('Hata', 'Hesaplar yüklenirken bir hata oluştu');
    }
  }, []);

  // Load categories on component mount and when type changes
  useEffect(() => {
    loadCategories();
    // Reset category when type changes
    setCategory(null);
  }, [type, loadCategories]);

  // Load accounts on component mount
  useEffect(() => {
    loadAccounts();
  }, [loadAccounts]);

  const handleCategorySelect = () => {
    if (categories.length === 0) {
      Alert.alert('Bilgi', 'Kategoriler yükleniyor, lütfen bekleyin...');
      return;
    }

    const options = ['İptal', ...categories.map(cat => cat.name)];

    ActionSheetIOS.showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex: 0,
      },
      (buttonIndex) => {
        if (buttonIndex === 0) return;
        const selectedCategory = categories[buttonIndex - 1];
        setCategory(selectedCategory);
      }
    );
  };

  const handleAccountSelect = () => {
    if (accounts.length === 0) {
      Alert.alert('Bilgi', 'Hesaplar yükleniyor, lütfen bekleyin...');
      return;
    }

    const options = ["İptal", ...accounts.map(acc => acc.name)];
    ActionSheetIOS.showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex: 0,
      },
      (buttonIndex) => {
        if (buttonIndex === 0) return;
        const selectedAccount = accounts[buttonIndex - 1];
        setAccount(selectedAccount);
      }
    );
  };

  const handleSave = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
      return;
    }

    try {
      // Duplicate control: same day + same amount + same title + same type
      const candidateDate = new Date(date);
      const result = await transactionService.getTransactions(1, 200);
      const existing = result?.transactions || [];
      const sameDay = (d1: Date, d2: Date) => (
        d1.getFullYear() === d2.getFullYear() &&
        d1.getMonth() === d2.getMonth() &&
        d1.getDate() === d2.getDate()
      );
      const normalizedTitle = (t: string) => t.trim().toLowerCase();
      const isDup = existing.some((t: any) => {
        const tDate = new Date(t.transactionDate || t.date);
        const tAmount = Math.abs(Number(t.amount));
        const cAmount = Math.abs(parseFloat(amount));
        const tTitle = normalizedTitle(t.title || t.description || '');
        const cTitle = normalizedTitle(title || (type === 'income' ? 'Gelir' : 'Gider'));
        const sameType = (t.type === type);
        return sameType && sameDay(tDate, candidateDate) && tAmount === cAmount && tTitle === cTitle;
      });

      if (isDup) {
        Alert.alert('Uyarı', 'Aynı gün, aynı tutar ve başlıkta bir işlem zaten mevcut.');
        return;
      }
      const transactionData = {
        title: title || (type === 'income' ? 'Gelir' : 'Gider'),
        amount: parseFloat(amount),
        note,
        transaction_date: date.toISOString(),
        type,
        category_id: category?.id || '',
        categoryName: category?.name || 'Diğer', // Kategori adını da gönder
        account_id: account?.id || '',
        accountName: account?.name || 'Nakit', // Hesap adını da gönder
        payment_method: 'card',
        currency: 'TRY',
        location: '',
      };

      console.log('[AddTransactionScreen] Transaction data:', {
        categoryName: transactionData.categoryName,
        categoryId: transactionData.category_id,
        accountName: transactionData.accountName,
        accountId: transactionData.account_id,
        amount: transactionData.amount,
        type: transactionData.type
      });

      // If user enabled recurring, create recurring schedule first
      if (isRecurring) {
        try {
          const { recurringService } = await import('../../services/recurringService');
          // Build start_date: for monthly choose selected day-of-month; for others use chosen date
          const startForRecurring = interval === 'monthly'
            ? new Date(date.getFullYear(), date.getMonth(), dayOfMonth)
            : date;
          const startStr = `${startForRecurring.getFullYear()}-${String(startForRecurring.getMonth()+1).padStart(2,'0')}-${String(startForRecurring.getDate()).padStart(2,'0')}`;
          await recurringService.createRecurringTransaction({
            title: transactionData.title,
            type: transactionData.type,
            amount: transactionData.amount,
            currency: transactionData.currency,
            interval,
            start_date: startStr,
            category_id: transactionData.category_id,
            payment_method: transactionData.payment_method,
            account_id: transactionData.account_id,
            note: transactionData.note,
          });
        } catch (e: any) {
          console.error('[AddTransaction] Recurring create error:', e);
          Alert.alert('Uyarı', e?.message || 'Tekrarlı işlem oluşturulamadı.');
        }
      }

      // Save using real API (single transaction now)
      await transactionService.createTransaction(transactionData);

      Alert.alert(
        'Başarılı',
        'İşlem başarıyla kaydedildi.',
        [
          {
            text: 'Tamam',
            onPress: () => {
              // Reset form
              setTitle('');
              setAmount('');
              setNote('');
              setDate(new Date());
              setCategory(null);
              setAccount(null);
              setShowAdvanced(false);

              // Navigate to home screen
              if (onNavigate) {
                onNavigate('MainTabs');
              }
            }
          }
        ]
      );
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || 'İşlem kaydedilirken bir hata oluştu.';
      Alert.alert('Hata', errorMessage);
    }
  };

  // Bulk transaction functions
  const generateTransactionId = () => {
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
  };

  const addNewTransaction = () => {
    // Validation
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Hata', 'Lütfen geçerli bir tutar girin.');
      return;
    }

    const newTransaction: BulkTransaction = {
      id: generateTransactionId(),
      title: title || (type === 'income' ? 'Gelir' : 'Gider'),
      amount,
      note,
      date,
      type,
      category,
      account,
    };

    setBulkTransactions(prev => [newTransaction, ...prev]);

    // Reset current form
    setTitle('');
    setAmount('');
    setNote('');
    setDate(new Date());
    setCategory(null);
    setAccount(null);
  };

  const removeTransaction = (id: string) => {
    setBulkTransactions(prev => prev.filter(t => t.id !== id));
  };

  const editTransaction = (transaction: BulkTransaction) => {
    setTitle(transaction.title);
    setAmount(transaction.amount);
    setNote(transaction.note);
    setDate(transaction.date);
    setType(transaction.type);
    setCategory(transaction.category);
    setAccount(transaction.account);

    // Remove from list to edit
    removeTransaction(transaction.id);
  };




  const saveBulkTransactions = async () => {
    if (bulkTransactions.length === 0) {
      Alert.alert('Hata', 'Kaydetmek için en az bir işlem eklemelisiniz.');
      return;
    }

    // Validate all transactions (only amount is required)
    const invalidTransactions = bulkTransactions.filter(transaction =>
      parseFloat(transaction.amount) <= 0
    );

    if (invalidTransactions.length > 0) {
      Alert.alert('Hata', 'Bazı işlemlerde geçersiz tutar var. Lütfen tüm tutarları kontrol edin.');
      return;
    }

    setIsSaving(true);
    try {
      // Remove duplicates vs existing
      const existing = (await transactionService.getTransactions(1, 500)).transactions || [];
      const sameDay = (d1: Date, d2: Date) => (
        d1.getFullYear() === d2.getFullYear() &&
        d1.getMonth() === d2.getMonth() &&
        d1.getDate() === d2.getDate()
      );
      const normalizedTitle = (t: string) => t.trim().toLowerCase();
      const filteredBulk = bulkTransactions.filter(tr => {
        const cDate = new Date(tr.date);
        const cAmount = Math.abs(parseFloat(tr.amount));
        const cTitle = normalizedTitle(tr.title);
        const cType = tr.type;
        return !existing.some((t: any) => {
          const tDate = new Date(t.transactionDate || t.date);
          const tAmount = Math.abs(Number(t.amount));
          const tTitle = normalizedTitle(t.title || t.description || '');
          return (t.type === cType) && sameDay(tDate, cDate) && tAmount === cAmount && tTitle === cTitle;
        });
      });

      // Convert bulk transactions to API format
      const transactionsData = filteredBulk.map(transaction => ({
        title: transaction.title,
        amount: parseFloat(transaction.amount),
        note: transaction.note,
        transaction_date: transaction.date.toISOString(),
        type: transaction.type,
        category_id: transaction.category?.id || '',
        account_id: transaction.account?.id || '',
        payment_method: 'card',
        currency: 'TRY',
        // For local storage (guest mode)
        categoryName: transaction.category?.name || 'Diğer',
        accountName: transaction.account?.name || 'Nakit',
        transactionDate: transaction.date.toISOString(),
        date: transaction.date.toISOString(),
        categoryId: transaction.category?.id || '',
        accountId: transaction.account?.id || '',
        paymentMethod: 'card',
      }));

      console.log('[AddTransactionScreen] Saving transactions:', transactionsData);
      console.log('[AddTransactionScreen] Number of transactions:', transactionsData.length);
      const result = await transactionService.createBulkTransactions(transactionsData);
      console.log('[AddTransactionScreen] Result:', result);

      const successCount = result?.success_count || result?.successful?.length || 0;
      const failureCount = result?.failure_count || result?.failed?.length || 0;
      const skippedCount = bulkTransactions.length - filteredBulk.length;
      
      let message = `${successCount} işlem başarıyla kaydedildi.`;
      if (skippedCount > 0) {
        message += ` ${skippedCount} yinelenen işlem atlandı.`;
      }
      if (failureCount > 0) {
        message += ` ${failureCount} işlem başarısız oldu.`;
        console.log('Failed transactions:', result?.failed);
      }

      Alert.alert(
        'Başarılı',
        message,
        [
          {
            text: 'Tamam',
            onPress: () => {
              console.log('[AddTransactionScreen] Bulk - Navigating to MainTabs...');
              console.log('[AddTransactionScreen] Bulk - onNavigate function:', onNavigate);
              
              // Reset everything
              setBulkTransactions([]);
              setTitle('');
              setAmount('');
              setNote('');
              setDate(new Date());
              setCategory(null);
              setAccount(null);
              setShowAdvanced(false);
              
              // Navigate to home screen
              if (onNavigate) {
                console.log('[AddTransactionScreen] Bulk - Calling onNavigate with MainTabs');
                onNavigate('MainTabs');
              } else {
                console.log('[AddTransactionScreen] Bulk - onNavigate is undefined!');
              }
            }
          }
        ]
      );
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || 'İşlemler kaydedilirken bir hata oluştu.';
      Alert.alert('Hata', errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
      edges={['top', 'left', 'right']} // Sadece üst, sol ve sağ kenarları korur, alt kısmı dahil etmez
    >
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      <KeyboardAvoidingView
        style={styles.flex}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView contentContainerStyle={styles.scroll}>
          <Text style={[styles.header, { color: colors.text.primary }]}>
            Yeni İşlem
          </Text>

          {/* Type selector */}
          <View style={styles.row}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                { backgroundColor: colors.background.secondary, borderColor: colors.border.primary },
                type === "income" && [styles.typeButtonActive, { backgroundColor: colors.primary[500] }],
              ]}
              onPress={() => setType("income")}
            >
              <Text
                style={[
                  styles.typeButtonText,
                  { color: colors.text.primary },
                  type === "income" && styles.typeButtonTextActive,
                ]}
              >
                Gelir
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                { backgroundColor: colors.background.secondary, borderColor: colors.border.primary },
                type === "expense" && [styles.typeButtonActive, { backgroundColor: colors.primary[500] }],
              ]}
              onPress={() => setType("expense")}
            >
              <Text
                style={[
                  styles.typeButtonText,
                  { color: colors.text.primary },
                  type === "expense" && styles.typeButtonTextActive,
                ]}
              >
                Gider
              </Text>
            </TouchableOpacity>
          </View>

          {/* Amount - Main Field */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text.secondary }]}>Tutar</Text>
            <TextInput
              style={[
                styles.input,
                styles.amountInput,
                styles.amountInputThemed,
                {
                  backgroundColor: colors.background.secondary,
                  borderColor: colors.border.primary,
                  color: colors.text.primary
                }
              ]}
              value={amount}
              onChangeText={setAmount}
              keyboardType="numeric"
              placeholder="0,00"
              placeholderTextColor={colors.text.tertiary}
              autoFocus
            />
          </View>

          {/* Title/Name Field */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: colors.text.secondary }]}>İşlem Adı (İsteğe bağlı)</Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: colors.background.secondary,
                  borderColor: colors.border.primary,
                  color: colors.text.primary
                }
              ]}
              value={title}
              onChangeText={setTitle}
              placeholder="Örn: Market alışverişi"
              placeholderTextColor={colors.text.tertiary}
            />
          </View>

          {/* Advanced Toggle */}
          <View style={styles.rowBetween}>
            <Text style={[styles.label, { color: colors.text.secondary }]}>Gelişmiş Seçenekler</Text>
            <Switch
              value={showAdvanced}
              onValueChange={setShowAdvanced}
            />
          </View>

          {/* Advanced Fields - Only show when toggle is on */}
          {showAdvanced && (
            <>
              {/* Category */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text.secondary }]}>Kategori</Text>
                <TouchableOpacity
                  style={[
                    styles.selector,
                    { backgroundColor: colors.background.secondary, borderColor: colors.border.primary },
                    loadingCategories && styles.selectorDisabled
                  ]}
                  onPress={handleCategorySelect}
                  disabled={loadingCategories}
                >
                  <Text style={[
                    styles.selectorText,
                    { color: colors.text.primary },
                    loadingCategories && styles.selectorTextDisabled
                  ]}>
                    {loadingCategories
                      ? "Kategoriler yükleniyor..."
                      : category
                        ? `${category.icon || ''} ${category.name}`
                        : "Kategori seçin"
                    }
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Account */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text.secondary }]}>Hesap</Text>
                <TouchableOpacity
                  style={[
                    styles.selector,
                    { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
                  ]}
                  onPress={handleAccountSelect}
                >
                  <Text style={[styles.selectorText, { color: colors.text.primary }]}>
                    {account?.name || "Hesap seçin"}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Note */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text.secondary }]}>Not</Text>
                <TextInput
                  style={[
                    styles.input,
                    styles.noteInput,
                    {
                      backgroundColor: colors.background.secondary,
                      borderColor: colors.border.primary,
                      color: colors.text.primary
                    }
                  ]}
                  value={note}
                  onChangeText={setNote}
                  placeholder="Not ekleyin"
                  placeholderTextColor={colors.text.tertiary}
                  multiline
                />
              </View>

              {/* Date */}
              <View style={styles.inputGroup}>
                <Text style={[styles.label, { color: colors.text.secondary }]}>Tarih</Text>
                <TouchableOpacity
                  style={[
                    styles.selector,
                    { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }
                  ]}
                onPress={() => { setTempDate(date); setDatePickerOpen(true); }}
                >
                  <Text style={[styles.selectorText, { color: colors.text.primary }]}>
                    {date.toLocaleDateString("tr-TR")}
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}

          {isDatePickerOpen && (
            <View style={styles.modalOverlay}>
              <View style={[styles.modalContainer, { backgroundColor: colors.background.secondary }]}>
                <Text style={[styles.label, styles.modalTitleMargin, { color: colors.text.primary }]}>Tarih</Text>
                <DateTimePicker
                  value={tempDate}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  themeVariant={isDark ? 'dark' : 'light'}
                  onChange={(_e, d) => { if (d) setTempDate(d); }}
                />
                <View style={styles.modalButtonContainer}>
                  <TouchableOpacity onPress={() => setDatePickerOpen(false)}>
                    <Text style={[styles.modalCancelButton, { color: colors.text.secondary }]}>İptal</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => { setDate(tempDate); setDatePickerOpen(false); }}>
                    <Text style={[styles.modalConfirmButton, { color: colors.primary[500] }]}>Tamam</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}

          {/* Recurring options */}
          <View style={[styles.recurringContainer, { borderColor: colors.border.primary, backgroundColor: colors.background.secondary }]}>
            <View style={styles.recurringHeader}>
              <Text style={[styles.label, { color: colors.text.primary }]}>Tekrarlı</Text>
              <Switch value={isRecurring} onValueChange={setIsRecurring} />
            </View>
            {isRecurring && (
              <View style={styles.recurringContent}>
                <View style={styles.recurringItem}>
                  <Text style={[styles.label, { color: colors.text.secondary }]}>Tekrar Aralığı</Text>
                  <TouchableOpacity
                    style={[styles.selector, { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }]}
                    onPress={() => {
                      ActionSheetIOS.showActionSheetWithOptions(
                        {
                          options: ['İptal', 'Günlük', 'Haftalık', 'Aylık', 'Yıllık'],
                          cancelButtonIndex: 0,
                        },
                        (idx) => {
                          const intervalMap: { [key: number]: "daily" | "weekly" | "monthly" | "yearly" } = {
                            1: 'daily',
                            2: 'weekly', 
                            3: 'monthly',
                            4: 'yearly'
                          };
                          if (intervalMap[idx]) {
                            setInterval(intervalMap[idx]);
                          }
                        }
                      );
                    }}
                  >
                    <Text style={[styles.selectorText, { color: colors.text.primary }]}>
                      {interval === 'daily' ? 'Günlük' : interval === 'weekly' ? 'Haftalık' : interval === 'monthly' ? 'Aylık' : 'Yıllık'}
                    </Text>
                  </TouchableOpacity>
                </View>
                {interval === 'monthly' && (
                  <View>
                    <Text style={[styles.label, { color: colors.text.secondary }]}>Ayın Günü</Text>
                    <TouchableOpacity
                      style={[styles.selector, { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }]}
                      onPress={() => {
                        const options = ['İptal', ...Array.from({length:31}, (_,i)=>String(i+1))];
                        ActionSheetIOS.showActionSheetWithOptions(
                          { options, cancelButtonIndex: 0 },
                          (idx) => {
                            if (idx && idx > 0) setDayOfMonth(idx);
                          }
                        );
                      }}
                    >
                      <Text style={[styles.selectorText, { color: colors.text.primary }]}>
                        {dayOfMonth}. gün
                      </Text>
                    </TouchableOpacity>
                    <Text style={[styles.recurringHelpText, { color: colors.text.tertiary }]}>
                      29-31 için kısa aylarda ayın son günü çalışır.
                    </Text>
                  </View>
                )}
              </View>
            )}
          </View>

          {/* Save Buttons */}
          <View style={styles.bulkButtonContainer}>
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: colors.success[500] }]}
              onPress={addNewTransaction}
            >
              <Text style={[styles.saveButtonText, { color: colors.background.secondary }]}>+ Ekle</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: colors.primary[500] }]}
              onPress={bulkTransactions.length > 0 ? saveBulkTransactions : handleSave}
              disabled={isSaving}
            >
              <Text style={[styles.saveButtonText, { color: colors.background.secondary }]}>
                {isSaving ? 'Kaydediliyor...' :
                 bulkTransactions.length > 0 ? `Tümünü Kaydet (${bulkTransactions.length})` : 'Kaydet'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Bulk transactions list - at the bottom */}
          {bulkTransactions.length > 0 && (
            <View style={styles.bulkListContainer}>
              <Text style={[styles.bulkListHeader, { color: colors.text.primary }]}>
                Eklenecek İşlemler ({bulkTransactions.length})
              </Text>
              {bulkTransactions.map((transaction) => (
                <View key={transaction.id} style={[styles.bulkTransactionItem, { backgroundColor: colors.background.secondary, borderColor: colors.border.primary }]}>
                  <View style={styles.bulkTransactionInfo}>
                    <Text style={[styles.bulkTransactionTitle, { color: colors.text.primary }]}>
                      {transaction.title}
                    </Text>
                    <Text style={[styles.bulkTransactionAmount, { color: transaction.type === 'income' ? colors.success[500] : colors.error[500] }]}>
                      {transaction.type === 'income' ? '+' : '-'}{transaction.amount} TL
                    </Text>
                    <Text style={[styles.bulkTransactionDate, { color: colors.text.secondary }]}>
                      {transaction.date.toLocaleDateString('tr-TR')}
                    </Text>
                  </View>
                  <View style={styles.bulkTransactionActions}>
                    <TouchableOpacity
                      style={[styles.bulkActionButton, { backgroundColor: colors.primary[500] }]}
                      onPress={() => editTransaction(transaction)}
                    >
                      <Text style={styles.bulkActionButtonText}>Düzenle</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.bulkActionButton, { backgroundColor: colors.error[500] }]}
                      onPress={() => removeTransaction(transaction.id)}
                    >
                      <Text style={styles.bulkActionButtonText}>Sil</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          )}


        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  flex: { flex: 1 },
  scroll: { padding: 20 },
  header: {
    ...typography.styles.largeTitle,
    fontWeight: "700",
    marginBottom: 20
  },
  row: { flexDirection: "row", marginBottom: 16 },
  typeButton: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    alignItems: "center",
    borderRadius: 8,
    marginHorizontal: 4,
    minHeight: 44,
  },
  typeButtonActive: {},
  typeButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
  },
  typeButtonTextActive: {
    color: "#fff",
  },
  inputGroup: { marginBottom: 16 },
  label: {
    ...typography.styles.inputLabel,
    marginBottom: 6,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    ...typography.styles.input,
    minHeight: 44,
  },
  amountInput: {
    ...typography.styles.largeTitle,
    fontWeight: '300',
    textAlign: 'center',
  },
  amountInputThemed: {},
  noteInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  selector: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    justifyContent: "center",
    minHeight: 44,
  },
  selectorDisabled: {
    opacity: 0.6,
  },
  selectorText: {
    ...typography.styles.input,
  },
  selectorTextDisabled: {
    ...typography.styles.input,
    opacity: 0.6,
  },
  rowBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 12,
  },
  saveButton: {
    marginTop: 20,
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    minHeight: 44,
  },
  saveButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
  },
  // Bulk mode styles
  bulkListContainer: {
    marginBottom: 20,
  },
  bulkListHeader: {
    ...typography.styles.subtitle1,
    fontWeight: '600',
    marginTop: 14,
    marginBottom: 10,
  },
  bulkTransactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  bulkTransactionInfo: {
    flex: 1,
  },
  bulkTransactionTitle: {
    ...typography.styles.body,
    fontWeight: '500',
    marginBottom: 2,
  },
  bulkTransactionAmount: {
    ...typography.styles.subtitle1,
    fontWeight: '600',
    marginBottom: 2,
  },
  bulkTransactionDate: {
    ...typography.styles.caption,
  },
  bulkTransactionActions: {
    flexDirection: 'row',
    gap: 8,
  },
  bulkActionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  bulkActionButtonText: {
    ...typography.styles.caption,
    color: '#fff',
    fontWeight: '500',
  },
  bulkButtonContainer: {
    gap: 12,
  },
  addButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    minHeight: 44,
  },
  modalOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: '#00000080',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    borderRadius: 12,
    padding: 16,
  },
  modalTitleMargin: {
    marginBottom: 8,
  },
  modalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
    gap: 12,
  },
  modalCancelButton: {
    color: '#666',
  },
  modalConfirmButton: {
    color: '#007AFF',
    fontWeight: '600',
  },
  recurringContainer: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  recurringHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  recurringContent: {
    marginTop: 12,
  },
  recurringItem: {
    marginBottom: 12,
  },
  recurringHelpText: {
    marginTop: 6,
  },
});
