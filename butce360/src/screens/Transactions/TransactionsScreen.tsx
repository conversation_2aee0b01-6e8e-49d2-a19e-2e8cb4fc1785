import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { useAuth } from '../../hooks/useAuth';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { transactionService } from '../../services/transactionService';
import { Transaction } from '../../types/models';
import { NumberFormatter } from '../../utils/number';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const TransactionsScreen: React.FC = () => {
  const { state: authState } = useAuth();
  const colors = useThemedColors();

  // State
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreData, setHasMoreData] = useState(true);

  // Fetch transactions from API
  const fetchTransactions = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      setError(null);
      if (append) {
        setIsLoadingMore(true);
      }

      // Only fetch data if user is authenticated (not guest)
      if (authState.isAuthenticated && !authState.isGuest) {
        const response = await transactionService.getTransactions(page, 20);
        const newTransactions = response.transactions || [];

        if (append) {
          setTransactions(prev => [...prev, ...newTransactions]);
        } else {
          setTransactions(newTransactions);
          setCurrentPage(1);
        }

        // Check if there's more data
        setHasMoreData(newTransactions.length === 20);
      } else {
        // For guest users, show empty state
        setTransactions([]);
        setHasMoreData(false);
      }
    } catch (fetchError) {
      console.error('[TransactionsScreen] Error fetching transactions:', fetchError);
      setError(fetchError instanceof Error ? fetchError.message : 'İşlemler yüklenirken hata oluştu');
      if (!append) {
        setTransactions([]);
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
      setIsLoadingMore(false);
    }
  }, [authState.isAuthenticated, authState.isGuest]);

  // Initial load
  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions, authState.isAuthenticated, authState.isGuest]);

  // Refresh transactions when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('[TransactionsScreen] Screen focused, refreshing transactions...');
      fetchTransactions();
    }, [fetchTransactions])
  );

  // Pull to refresh
  const onRefresh = () => {
    setIsRefreshing(true);
    fetchTransactions();
  };

  // Load more data
  const loadMore = () => {
    if (!isLoadingMore && hasMoreData && authState.isAuthenticated && !authState.isGuest) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchTransactions(nextPage, true);
    }
  };

  // Guest state
  if (authState.isGuest) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <StatusBar
          barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
          backgroundColor={colors.background.primary}
        />

        <View style={[styles.guestContainer, { backgroundColor: colors.background.primary }]}>
          <Text style={styles.guestIcon}>📝</Text>
          <Text style={[styles.guestTitle, { color: colors.text.primary }]}>İşlemlerinizi Görüntüle</Text>
          <Text style={[styles.guestText, { color: colors.text.secondary }]}>
            Gelir ve gider takibi için giriş yapmanız gerekiyor.
          </Text>
          <TouchableOpacity
            style={[styles.loginButton, { backgroundColor: colors.primary[500] }]}
            onPress={() => {}}
          >
            <Text style={[styles.loginButtonText, { color: colors.background.secondary }]}>Giriş Yap</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
        <StatusBar
          barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
          backgroundColor={colors.background.primary}
        />
        <View style={[styles.loadingContainer, { backgroundColor: colors.background.primary }]}>
          <LoadingSpinner />
        </View>
      </View>
    );
  }

  // Authenticated user with data
  return (
    <View style={[styles.container, { backgroundColor: colors.background.primary }]}>
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      {error && (
        <View style={[styles.errorContainer, { backgroundColor: colors.error[50], borderLeftColor: colors.error[500] }]}>
          <Text style={[styles.errorText, { color: colors.error[500] }]}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => fetchTransactions()}>
            <Text style={[styles.retryButtonText, { color: colors.error[500] }]}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
      >
        {transactions.length > 0 ? (
          <View style={styles.transactionList}>
            {transactions
              .sort((a, b) => new Date(b.transactionDate).getTime() - new Date(a.transactionDate).getTime())
              .map((transaction) => (
              <View key={transaction.id} style={styles.transactionItem}>
                <View style={styles.transactionLeft}>
                  <Text style={styles.transactionDescription}>
                    {transaction.title || 'İsimsiz İşlem'}
                  </Text>
                  <Text style={styles.transactionDate}>
                    {transaction.transactionDate
                      ? new Date(transaction.transactionDate).toLocaleDateString('tr-TR')
                      : 'Tarih belirtilmemiş'
                    }
                  </Text>
                  {transaction.note && (
                    <Text style={styles.transactionCategory}>
                      {transaction.note}
                    </Text>
                  )}
                </View>
                <View style={styles.transactionRight}>
                  <Text style={[
                    styles.transactionAmount,
                    transaction.type === 'income' ? styles.incomeAmount : styles.expenseAmount
                  ]}>
                    {transaction.type === 'income' ? '+' : '-'}
                    {NumberFormatter.formatCurrency(transaction.amount, transaction.currency)}
                  </Text>
                </View>
              </View>
            ))}

            {/* Load More Button */}
            {hasMoreData && !isLoading && (
              <TouchableOpacity
                style={styles.loadMoreButton}
                onPress={loadMore}
                disabled={isLoadingMore}
              >
                <Text style={styles.loadMoreText}>
                  {isLoadingMore ? 'Yükleniyor...' : 'Daha Fazla Yükle'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>📝</Text>
            <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>Henüz işlem yok</Text>
            <Text style={[styles.emptyText, { color: colors.text.secondary }]}>
              İlk gelir veya giderinizi ekleyerek başlayın
            </Text>
            <TouchableOpacity
              style={[styles.emptyStateButton, { backgroundColor: colors.primary[500] }]}
              onPress={() => {}}
            >
              <Text style={[styles.emptyStateButtonText, { color: colors.background.secondary }]}>İşlem Ekle</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Guest State
  guestContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  guestIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  guestTitle: {
    ...typography.styles.title2,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  guestText: {
    ...typography.styles.body,
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
  },
  loginButton: {
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    minHeight: 44,
  },
  loginButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
  },

  // Empty State
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  emptyTitle: {
    ...typography.styles.title2,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    ...typography.styles.body,
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
  },

  // Empty State Button
  emptyStateButton: {
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    minHeight: 44,
  },
  emptyStateButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
  },

  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Error
  errorContainer: {
    margin: 20,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  errorText: {
    ...typography.styles.subhead,
    marginBottom: 8,
  },
  retryButton: {
    alignSelf: 'flex-start',
  },
  retryButtonText: {
    ...typography.styles.subhead,
    fontWeight: '600',
  },

  // ScrollView
  scrollView: {
    flex: 1,
  },

  // Transaction List
  transactionList: {
    padding: 20,
  },
  transactionItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  transactionLeft: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 2,
  },
  transactionCategory: {
    fontSize: 12,
    color: '#64748b',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  incomeAmount: {
    color: '#22c55e',
  },
  expenseAmount: {
    color: '#ef4444',
  },
  loadMoreButton: {
    backgroundColor: '#f8fafc',
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginHorizontal: 16,
    marginVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    alignItems: 'center',
  },
  loadMoreText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0ea5e9',
  },
});

export default TransactionsScreen;
