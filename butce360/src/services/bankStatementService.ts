import { apiClient } from './api';
import { authService } from './authService';

export interface BankStatementEntry {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  categoryId?: string;
  accountId?: string;
}

export interface BankStatementUploadResponse {
  entries: BankStatementEntry[];
}

export interface BankStatementImportRequest {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  categoryId: string;
  accountId: string;
}

export interface BankStatementImportResponse {
  transactions: any[];
}

export interface UploadBankStatementParams {
  file: {
    uri: string;
    type: string;
    name: string;
  };
  bank: 'vakifbank' | 'enpara' | 'garanti' | 'creditcard';
}

class BankStatementService {
  async uploadBankStatement(params: UploadBankStatementParams): Promise<BankStatementUploadResponse> {
    console.log('[BankStatementService] Uploading bank statement:', {
      fileName: params.file.name,
      fileType: params.file.type,
      bank: params.bank,
    });

    const formData = new FormData();

    // Add file to form data
    formData.append('file', {
      uri: params.file.uri,
      type: params.file.type,
      name: params.file.name,
    } as any);

    // Add bank parameter
    formData.append('bank', params.bank);

    console.log('[BankStatementService] FormData prepared:', formData);

    try {
      const token = await authService.getStoredToken();
      console.log('[BankStatementService] Making authenticated request with token:', token ? 'present' : 'missing');
      
      const response = await apiClient.authenticatedRequest<BankStatementUploadResponse>('/bank-statements/upload', {
        method: 'POST',
        body: formData,
      }, token!);

      console.log('[BankStatementService] Upload successful:', response);
      console.log('[BankStatementService] Response data:', response.data);
      
      // Backend returns: { data: { entries: [...] }, status: 200 }
      // We need to access response.data directly, not response.data.data
      if (!response.data) {
        console.error('[BankStatementService] No data in response:', response);
        throw new Error('Invalid response from server');
      }
      
      // Check if response.data has entries property directly
      if (!response.data.entries) {
        console.error('[BankStatementService] No entries in response.data:', response.data);
        throw new Error('No entries found in response');
      }
      
      return response.data;
    } catch (error) {
      console.error('[BankStatementService] Upload failed:', error);
      
      // Better error handling
      if (error && typeof error === 'object') {
        const errorObj = error as any;

        // Extract meaningful error message
        let errorMessage = 'PDF yükleme sırasında bir hata oluştu';

        if (errorObj.message) {
          errorMessage = errorObj.message;
        } else if (errorObj.data && errorObj.data.error) {
          errorMessage = errorObj.data.error;
        } else if (errorObj.response && errorObj.response.data && errorObj.response.data.error) {
          errorMessage = errorObj.response.data.error;
        }

        throw new Error(errorMessage);
      }
      
      throw error;
    }
  }

  async importBankStatementEntries(entries: BankStatementImportRequest[]): Promise<BankStatementImportResponse> {
    const token = await authService.getStoredToken();
    const response = await apiClient.authenticatedRequest<{ data: BankStatementImportResponse }>('/bank-statements/import', {
      method: 'POST',
      body: JSON.stringify(entries)
    }, token!);
    return response.data!.data;
  }

  getSupportedBanks(): Array<{ key: string; name: string }> {
    return [
      { key: 'vakifbank', name: 'VakıfBank' },
      { key: 'enpara', name: 'Enpara Hesap Özeti' },
      { key: 'garanti', name: 'Garanti BBVA' },
      { key: 'creditcard', name: 'Enpara Kredi Kartı Ekstresi' },
    ];
  }
}

export const bankStatementService = new BankStatementService();
