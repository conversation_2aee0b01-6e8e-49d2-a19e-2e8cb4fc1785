import { apiClient } from './api';
import { authService } from './authService';
import { storage, STORAGE_KEYS } from './storage';


export interface Budget {
  id: string;
  name: string;
  amount: number;
  spent: number;
  remaining: number;
  currency: string;
  categoryId?: string;
  categoryName?: string;
  period: 'monthly' | 'yearly';
  startDate: string;
  endDate: string;
  color?: string; // Color for budget visualization
  createdAt: string;
  updatedAt: string;
}

export interface CreateBudgetRequest {
  name: string;
  amount: number;
  category_id?: string;
  period: 'monthly' | 'yearly';
  color?: string;
}

export interface UpdateBudgetRequest {
  name?: string;
  amount?: number;
  category_id?: string;
  period?: 'monthly' | 'yearly';
  startDate?: string;
  endDate?: string;
}

export class BudgetService {
  private static instance: BudgetService;

  private constructor() {}

  static getInstance(): BudgetService {
    if (!BudgetService.instance) {
      BudgetService.instance = new BudgetService();
    }
    return BudgetService.instance;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await authService.getStoredToken();
    if (!token) {
      throw new Error('No authentication token available');
    }
    return {
      'Authorization': `Bearer ${token}`
    };
  }

  // Get budgets with spending calculations
  async getBudgetsWithSpending(): Promise<Budget[]> {
    try {
      const budgets = await this.getBudgets();

      // Check if budgets is valid array
      if (!budgets || !Array.isArray(budgets)) {
        console.log('[BudgetService] No budgets found or invalid data:', budgets);
        return [];
      }

      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: Calculate spending from local transactions using category NAME
        console.log('[BudgetService] Guest mode: calculating spending from local transactions');

        const localTransactions = await storage.getObject<any[]>(STORAGE_KEYS.LOCAL_TRANSACTIONS) || [];
        console.log('[BudgetService] Local transactions found:', localTransactions.length);

        const budgetsWithSpending = budgets.map((budget: Budget) => {
          // Get current month's transactions for this budget's category NAME
          const currentDate = new Date();
          const currentMonth = currentDate.getMonth();
          const currentYear = currentDate.getFullYear();

          // Find transactions that match budget category NAME (not ID)
          const categoryTransactions = localTransactions.filter((transaction: any) => {
            const transactionDate = new Date(transaction.date);
            const categoryMatch = transaction.categoryName === budget.categoryName || transaction.category === budget.categoryName;
            const typeMatch = transaction.type === 'expense';
            const monthMatch = transactionDate.getMonth() === currentMonth;
            const yearMatch = transactionDate.getFullYear() === currentYear;

            console.log(`[BudgetService] Transaction check:`, {
              transactionCategory: transaction.categoryName || transaction.category,
              budgetCategory: budget.categoryName,
              categoryMatch,
              typeMatch,
              monthMatch,
              yearMatch,
              amount: transaction.amount
            });

            return categoryMatch && typeMatch && monthMatch && yearMatch;
          });

          console.log(`[BudgetService] Budget "${budget.name}" (category: ${budget.categoryName}): found ${categoryTransactions.length} matching transactions`);

          const totalSpent = categoryTransactions.reduce((sum: number, transaction: any) => sum + transaction.amount, 0);
          const remaining = budget.amount - totalSpent;
          const spentPercentage = budget.amount > 0 ? (totalSpent / budget.amount) * 100 : 0;

          console.log(`[BudgetService] Budget "${budget.name}": amount=${budget.amount}, spent=${totalSpent}, remaining=${remaining}`);

          return {
            ...budget,
            spent: totalSpent,
            remaining: Math.max(0, remaining),
            spentPercentage: Math.min(100, spentPercentage)
          };
        });

        return budgetsWithSpending;
      }

      // Authenticated mode: Backend already provides spent values, just calculate remaining
      const budgetsWithSpending = budgets.map((budget: Budget) => {
        const spent = budget.spent || 0; // Use backend provided spent value
        const remaining = budget.amount - spent;
        const spentPercentage = budget.amount > 0 ? (spent / budget.amount) * 100 : 0;

        return {
          ...budget,
          spent: spent, // Keep backend provided spent value
          remaining: Math.max(0, remaining),
          spentPercentage: Math.min(100, spentPercentage)
        };
      });

      return budgetsWithSpending;
    } catch (error) {
      console.error('[BudgetService] Error calculating budget spending:', error);
      // Fallback to regular budgets without spending data
      return this.getBudgets();
    }
  }

  // Get all budgets
  async getBudgets(): Promise<Budget[]> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: return local budgets
        console.log('[BudgetService] Guest mode: returning local budgets');
        const localBudgets = await storage.getObject<Budget[]>(STORAGE_KEYS.LOCAL_BUDGETS) || [];
        return localBudgets;
      }

      // Authenticated user: fetch from API
      const headers = await this.getAuthHeaders();
      const response = await apiClient.get<{data: Budget[], status: number}>('/budget', headers);



      // Handle different response formats
      let budgets: Budget[] = [];
      
      // Check if response.data is directly an array (current case)
      if (Array.isArray(response.data)) {
        budgets = response.data;
        console.log('[BudgetService] Using direct array format');
      }
      // Check if response.data has nested data property
      else if (response.data?.data && Array.isArray(response.data.data)) {
        budgets = response.data.data;
        console.log('[BudgetService] Using nested data format');
      }
      // Fallback to empty array
      else {
        budgets = [];
        console.log('[BudgetService] No valid budget data found, using empty array');
      }

      // Ensure budgets is an array
      if (!Array.isArray(budgets)) {
        console.log('[BudgetService] API returned invalid data format, returning empty array');
        return [];
      }

      // Map backend format to frontend format
      const mappedBudgets = budgets.map((budget: any) => {
        // Try different possible field names for spent value
        const spentValue = budget.spent || budget.total_spent || budget.spentAmount || budget.spent_amount || 0;



        return {
          ...budget,
          categoryId: budget.category_id || budget.categoryId,
          categoryName: budget.category_name || budget.categoryName,
          // Ensure required fields have default values
          currency: budget.currency || 'TRY',
          spent: spentValue, // Explicitly set spent value
          startDate: budget.startDate || new Date().toISOString().split('T')[0],
          endDate: budget.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          remaining: budget.remaining || Math.max(0, budget.amount - spentValue)
        };
      });



      return mappedBudgets;
    } catch (error) {
      console.error('[BudgetService] Error fetching budgets:', error);

      // Fallback to local budgets if API fails
      console.log('[BudgetService] Falling back to local budgets');
      const localBudgets = await storage.getObject<Budget[]>(STORAGE_KEYS.LOCAL_BUDGETS) || [];
      if (Array.isArray(localBudgets) && localBudgets.length > 0) {
        return localBudgets;
      }

      // Return mock data with colors for development
      return [
        {
          id: '1',
          name: 'Yiyecek & İçecek',
          amount: 2000,
          spent: 1250,
          remaining: 750,
          currency: 'TRY',
          categoryId: 'food',
          categoryName: 'Yiyecek & İçecek',
          period: 'monthly',
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          color: '#ff6b6b',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: 'Ulaşım',
          amount: 800,
          spent: 450,
          remaining: 350,
          currency: 'TRY',
          categoryId: 'transport',
          categoryName: 'Ulaşım',
          period: 'monthly',
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          color: '#4ecdc4',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: '3',
          name: 'Eğlence',
          amount: 1000,
          spent: 1200,
          remaining: -200,
          currency: 'TRY',
          categoryId: 'entertainment',
          categoryName: 'Eğlence',
          period: 'monthly',
          startDate: '2024-01-01',
          endDate: '2024-01-31',
          color: '#45b7d1',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ];
    }
  }

  // Create new budget
  async createBudget(budgetData: CreateBudgetRequest): Promise<Budget> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: save to local storage
        console.log('[BudgetService] Guest mode: saving budget locally');

        // Find category name from local categories
        const localCategories = await storage.getObject<any[]>(STORAGE_KEYS.LOCAL_CATEGORIES) || [];
        const category = localCategories.find(cat => cat.id === budgetData.category_id);
        const categoryName = category ? category.name : 'Bilinmeyen Kategori';

        console.log(`[BudgetService] Creating budget for category: ${categoryName} (ID: ${budgetData.category_id})`);

        const localBudget: Budget = {
          id: Date.now().toString(), // Simple ID generation
          name: budgetData.name,
          amount: budgetData.amount,
          spent: 0,
          remaining: budgetData.amount,
          currency: 'TRY',
          categoryId: budgetData.category_id,
          categoryName: categoryName, // Use actual category name
          period: budgetData.period,
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days later
          color: budgetData.color,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        // Get existing budgets from local storage
        const existingBudgets = await storage.getObject<Budget[]>(STORAGE_KEYS.LOCAL_BUDGETS) || [];
        existingBudgets.push(localBudget);

        // Save back to local storage
        await storage.setObject(STORAGE_KEYS.LOCAL_BUDGETS, existingBudgets);

        return localBudget;
      }

      // Authenticated user: save to API
      const headers = await this.getAuthHeaders();
      const response = await apiClient.post<Budget>('/budget', budgetData, headers);

      console.log('[BudgetService] Created budget:', response.data);
      return response.data;
    } catch (error) {
      console.error('[BudgetService] Error creating budget:', error);
      throw error;
    }
  }

  // Update budget
  async updateBudget(id: string, budgetData: UpdateBudgetRequest): Promise<Budget> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: update local budget
        console.log('[BudgetService] Guest mode: updating local budget');

        const existingBudgets = await storage.getObject<Budget[]>(STORAGE_KEYS.LOCAL_BUDGETS) || [];
        const budgetIndex = existingBudgets.findIndex(b => b.id === id);

        if (budgetIndex === -1) {
          throw new Error('Budget not found');
        }

        const updatedBudget: Budget = {
          ...existingBudgets[budgetIndex],
          ...budgetData,
          updatedAt: new Date().toISOString(),
        };

        existingBudgets[budgetIndex] = updatedBudget;
        await storage.setObject(STORAGE_KEYS.LOCAL_BUDGETS, existingBudgets);

        return updatedBudget;
      }

      // Authenticated user: update via API
      const headers = await this.getAuthHeaders();
      const response = await apiClient.put<Budget>(`/budget/${id}`, budgetData, headers);

      console.log('[BudgetService] Updated budget:', response.data);
      return response.data;
    } catch (error) {
      console.error('[BudgetService] Error updating budget:', error);
      throw error;
    }
  }

  // Delete budget
  async deleteBudget(id: string): Promise<void> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: delete local budget
        console.log('[BudgetService] Guest mode: deleting local budget');

        const existingBudgets = await storage.getObject<Budget[]>(STORAGE_KEYS.LOCAL_BUDGETS) || [];
        const filteredBudgets = existingBudgets.filter(b => b.id !== id);

        await storage.setObject(STORAGE_KEYS.LOCAL_BUDGETS, filteredBudgets);
        return;
      }

      // Authenticated user: delete via API
      const headers = await this.getAuthHeaders();
      await apiClient.delete(`/budget/${id}`, headers);

      console.log('[BudgetService] Deleted budget:', id);
    } catch (error) {
      console.error('[BudgetService] Error deleting budget:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const budgetService = BudgetService.getInstance();
