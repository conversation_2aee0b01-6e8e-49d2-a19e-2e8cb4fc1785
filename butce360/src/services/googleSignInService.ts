import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { authService } from './authService';

export class GoogleSignInService {
  private static instance: GoogleSignInService;

  private constructor() {}

  static getInstance(): GoogleSignInService {
    if (!GoogleSignInService.instance) {
      GoogleSignInService.instance = new GoogleSignInService();
    }
    return GoogleSignInService.instance;
  }

  // Configure Google Sign-In
  configure() {
    GoogleSignin.configure({
      webClientId: '102312339260-euuf2l3bnlvf708n3504m2qk3gm7ss94.apps.googleusercontent.com', // From Google Cloud Console
      iosClientId: '102312339260-ns38t9cipmomg5v8laqk7ii67ud28ona.apps.googleusercontent.com', // iOS Client ID
      offlineAccess: true,
      hostedDomain: '',
      forceCodeForRefreshToken: true,
    });
  }

  // Check if Google Play Services are available
  async isPlayServicesAvailable(): Promise<boolean> {
    try {
      await GoogleSignin.hasPlayServices();
      return true;
    } catch (error) {
      console.error('[GoogleSignInService] Play Services not available:', error);
      return false;
    }
  }

  // Sign in with Google
  async signIn(): Promise<{ success: boolean; user?: any; token?: string; error?: string }> {
    try {
      console.log('[GoogleSignInService] Starting Google Sign-In...');

      // Sign in
      const userInfo = await GoogleSignin.signIn();

      // Send Google token to backend for authentication
      const authResult = await this.authenticateWithBackend(userInfo);
      
      if (authResult.success) {
        return {
          success: true,
          user: authResult.user
        };
      } else {
        return {
          success: false,
          error: authResult.error || 'Backend authentication failed'
        };
      }
    } catch (error: any) {
      console.error('[GoogleSignInService] Sign-in error:', error);
      
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        return {
          success: false,
          error: 'Giriş iptal edildi'
        };
      } else if (error.code === statusCodes.IN_PROGRESS) {
        return {
          success: false,
          error: 'Giriş işlemi devam ediyor'
        };
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        return {
          success: false,
          error: 'Google Play Services mevcut değil'
        };
      } else {
        return {
          success: false,
          error: 'Google ile giriş yapılamadı'
        };
      }
    }
  }

  // Authenticate with backend using Google token
  private async authenticateWithBackend(userInfo: any): Promise<{ success: boolean; user?: any; token?: string; error?: string }> {
    try {
      console.log('[GoogleSignInService] Processing userInfo for backend:', userInfo);

      // Extract Google user info - @react-native-google-signin/google-signin format
      // Data is in userInfo.data, not userInfo directly
      const signInData = userInfo.data || userInfo;
      const user = signInData.user;

      const googleUser = {
        id: user?.id || user?.userId,
        email: user?.email,
        name: user?.name || (user?.givenName && user?.familyName ? (user.givenName + ' ' + user.familyName).trim() : ''),
        photo: user?.photo,
        idToken: signInData.idToken,
        accessToken: signInData.accessToken || signInData.serverAuthCode,
      };


      // Validate required fields
      if (!googleUser.idToken) {
        throw new Error('ID Token is missing from Google Sign-In response');
      }

      // Validate ID token format (should be JWT)
      if (!googleUser.idToken.includes('.')) {
        throw new Error('Invalid ID Token format - not a JWT');
      }

      const response = await authService.googleSignIn(googleUser);

      return {
        success: true,
        user: response.user,
        token: response.token
      };
    } catch (error: any) {

      // Extract more detailed error information
      let errorMessage = 'Backend authentication failed';
      if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      await GoogleSignin.signOut();
      console.log('[GoogleSignInService] Google sign-out successful');
    } catch (error) {
      console.error('[GoogleSignInService] Sign-out error:', error);
    }
  }

  // Get current user
  async getCurrentUser(): Promise<any> {
    try {
      const userInfo = await GoogleSignin.signInSilently();
      return userInfo;
    } catch (error) {
      console.error('[GoogleSignInService] Get current user error:', error);
      return null;
    }
  }

  // Check if user is signed in
  async isSignedIn(): Promise<boolean> {
    try {
      const userInfo = await GoogleSignin.getCurrentUser();
      return userInfo !== null;
    } catch (error) {
      console.error('[GoogleSignInService] Check signed in error:', error);
      return false;
    }
  }
}

export const googleSignInService = GoogleSignInService.getInstance();
