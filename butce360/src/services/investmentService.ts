import { apiClient } from './api';
import { authService } from './authService';

// Investment DTOs matching backend
export interface InvestmentSimulateRequest {
  asset: string;
  amount: number;
  start_date: string; // YYYY-MM-DD format (backend expects snake_case)
}

export interface InvestmentSimulateResponse {
  id?: string;
  asset: string;
  amount_invested: number;
  start_date: string;
  price_at_start: number;
  current_price: number;
  units_bought: number;
  current_value: number;
  profit: number;
  growth_rate_percent: number;
}

export interface InvestmentWhatIfRequest {
  asset: string;
  amount: number;
  hypothetical_date: string; // YYYY-MM-DD format
}

export interface InvestmentWhatIfResponse {
  asset: string;
  hypothetical_date: string;
  amount_invested: number;
  price_then: number;
  price_now: number;
  current_value: number;
  profit: number;
  growth_rate_percent: number;
}

export interface WhatIfComparisonRequest {
  title?: string;
  assets: string[];
  amounts: number[]; // per-asset amount; backend expects a single amount
  date: string; // YYYY-MM-DD format
}

export interface WhatIfAssetResult {
  asset: string;
  asset_name: string;
  price_then: number;
  price_now: number;
  amount_invested: number;
  current_value: number;
  profit: number;
  profit_percentage: number;
  units_bought: number;
  currency: string;
  risk_level: string;
  volatility_score: number;
  annualized_return: number;
  success: boolean;
  error?: string;
}

export interface PerformanceInfo {
  best_performer: {
    asset: string;
    asset_name: string;
    profit_percentage: number;
  };
  worst_performer: {
    asset: string;
    asset_name: string;
    profit_percentage: number;
  };
  total_invested: number;
  total_current_value: number;
  total_profit: number;
  average_return: number;
}

export interface ComparisonAnalysis {
  title: string;
  summary: string;
  insights: string[];
  risk_assessment: string;
  recommendation: string;
  market_context: string;
}

export interface WhatIfComparisonResponse {
  comparison_date: string;
  performance: PerformanceInfo;
  analysis: ComparisonAnalysis;
  assets: WhatIfAssetResult[];
}

// Supported assets
export const SUPPORTED_ASSETS = [
  { id: 'BTC', name: 'Bitcoin', symbol: 'BTC' },
  { id: 'ETH', name: 'Ethereum', symbol: 'ETH' },
  { id: 'GOLD', name: 'Altın', symbol: 'GOLD' },
  { id: 'USDTRY', name: 'USD/TRY', symbol: 'USD' },
  { id: 'BIST100', name: 'BIST 100', symbol: 'XU100' },
];

export class InvestmentService {
  private static instance: InvestmentService;

  private constructor() {}

  static getInstance(): InvestmentService {
    if (!InvestmentService.instance) {
      InvestmentService.instance = new InvestmentService();
    }
    return InvestmentService.instance;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await authService.getStoredToken();
    if (!token) {
      throw new Error('No authentication token available');
    }
    return {
      'Authorization': `Bearer ${token}`
    };
  }

  // Simulate investment from a past date to today
  async simulateInvestment(request: InvestmentSimulateRequest): Promise<InvestmentSimulateResponse> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await apiClient.post<InvestmentSimulateResponse>(
        '/investment/simulate',
        request,
        headers
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'Investment simulation failed');
      }
    } catch (error: any) {
      console.error('[InvestmentService] simulateInvestment error:', error);
      throw new Error(error.message || 'Investment simulation failed');
    }
  }

  // What-if simulation: what if I had invested on a specific date
  async whatIfSimulation(request: InvestmentWhatIfRequest): Promise<InvestmentWhatIfResponse> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await apiClient.post<InvestmentWhatIfResponse>(
        '/investment/whatif',
        request,
        headers
      );

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.message || 'What-if simulation failed');
      }
    } catch (error: any) {
      console.error('[InvestmentService] whatIfSimulation error:', error);
      throw new Error(error.message || 'What-if simulation failed');
    }
  }

  // What-if comparison: compare multiple assets by composing existing /investment/whatif
  async whatIfComparison(request: WhatIfComparisonRequest): Promise<WhatIfComparisonResponse> {
    try {
      // Use the first amount for all assets (UI passes same amount per asset)
      const amount = Array.isArray(request.amounts) && request.amounts.length > 0 ? request.amounts[0] : 0;

      const results = await Promise.all(
        request.assets.map(async (assetId) => {
          try {
            const res = await this.whatIfSimulation({
              asset: assetId,
              amount,
              hypothetical_date: request.date,
            });
            const assetMeta = SUPPORTED_ASSETS.find(a => a.id === assetId);
            const days = Math.max(1, Math.floor((Date.now() - new Date(request.date).getTime()) / (1000 * 60 * 60 * 24)));
            const years = days / 365;
            const annualized = years > 0 ? ((res.current_value / Math.max(1e-9, res.amount_invested)) ** (1 / years) - 1) * 100 : res.growth_rate_percent;

            return {
              asset: res.asset,
              asset_name: assetMeta?.name || res.asset,
              price_then: res.price_then,
              price_now: res.price_now,
              amount_invested: res.amount_invested,
              current_value: res.current_value,
              profit: res.profit,
              profit_percentage: res.growth_rate_percent,
              units_bought: res.price_then > 0 ? res.amount_invested / res.price_then : 0,
              currency: 'TRY',
              risk_level: 'Medium',
              volatility_score: 5,
              annualized_return: annualized,
              success: true,
            } as WhatIfAssetResult;
          } catch (err: any) {
            const assetMeta = SUPPORTED_ASSETS.find(a => a.id === assetId);
            return {
              asset: assetId,
              asset_name: assetMeta?.name || assetId,
              price_then: 0,
              price_now: 0,
              amount_invested: amount,
              current_value: 0,
              profit: 0,
              profit_percentage: 0,
              units_bought: 0,
              currency: 'TRY',
              risk_level: 'Unknown',
              volatility_score: 0,
              annualized_return: 0,
              success: false,
              error: err?.message || 'Request failed',
            } as WhatIfAssetResult;
          }
        })
      );

      const successResults = results.filter(r => r.success);
      const total_invested = results.reduce((sum, r) => sum + (r.amount_invested || 0), 0);
      const total_current_value = successResults.reduce((sum, r) => sum + (r.current_value || 0), 0);
      const total_profit = successResults.reduce((sum, r) => sum + (r.profit || 0), 0);
      const avg = successResults.length > 0
        ? successResults.reduce((sum, r) => sum + (r.profit_percentage || 0), 0) / successResults.length
        : 0;

      const best = successResults.reduce((prev, curr) => (curr.profit_percentage > prev.profit_percentage ? curr : prev), successResults[0] || {
        asset: '', asset_name: '', profit_percentage: 0
      } as any);
      const worst = successResults.reduce((prev, curr) => (curr.profit_percentage < prev.profit_percentage ? curr : prev), successResults[0] || {
        asset: '', asset_name: '', profit_percentage: 0
      } as any);

      const response: WhatIfComparisonResponse = {
        comparison_date: request.date,
        performance: {
          best_performer: {
            asset: best?.asset || '',
            asset_name: best?.asset_name || '',
            profit_percentage: best?.profit_percentage || 0,
          },
          worst_performer: {
            asset: worst?.asset || '',
            asset_name: worst?.asset_name || '',
            profit_percentage: worst?.profit_percentage || 0,
          },
          total_invested,
          total_current_value,
          total_profit,
          average_return: avg,
        },
        analysis: {
          title: request.title || `${request.assets.join(', ')} Karşılaştırması`,
          summary: 'Seçili varlıklar için basit karşılaştırma özetlendi.',
          insights: [
            'Veriler bireysel what-if sonuçlarından türetilmiştir.',
          ],
          risk_assessment: 'Bu karşılaştırma yalnızca geçmiş veriye dayanmaktadır.',
          recommendation: 'Portföy çeşitlendirmesi riskleri azaltabilir.',
          market_context: 'Piyasa koşulları geçmiş dönemlere göre değişiklik gösterebilir.',
        },
        assets: results,
      };

      return response;
    } catch (error: any) {
      console.error('[InvestmentService] whatIfComparison error:', error);
      throw new Error(error?.message || 'What-if comparison failed');
    }
  }

  // Get user's investment simulation history
  async getSimulationHistory(page: number = 1, limit: number = 10): Promise<{
    simulations: InvestmentSimulateResponse[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await apiClient.get<InvestmentSimulateResponse[]>(
        `/investment/history?page=${page}&limit=${limit}`,
        headers
      );

      if (response.success && response.data) {
        // Backend returns array directly, we need to format it
        const simulations = Array.isArray(response.data) ? response.data : [];
        return {
          simulations,
          total: simulations.length,
          page,
          totalPages: Math.ceil(simulations.length / limit),
        };
      } else {
        throw new Error(response.message || 'Failed to fetch simulation history');
      }
    } catch (error: any) {
      console.error('[InvestmentService] getSimulationHistory error:', error);
      throw new Error(error.message || 'Failed to fetch simulation history');
    }
  }

  // Helper method to format date for API
  formatDateForAPI(date: Date): string {
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  // Helper method to get asset display name
  getAssetDisplayName(assetId: string): string {
    const asset = SUPPORTED_ASSETS.find(a => a.id === assetId);
    return asset ? asset.name : assetId;
  }

  // Helper method to validate date range
  validateDateRange(startDate: Date, endDate: Date = new Date()): boolean {
    return startDate < endDate;
  }

  // Delete a simulation by ID
  async deleteSimulation(id: string): Promise<void> {
    const headers = await this.getAuthHeaders();
    await apiClient.delete(`/investment/history/${id}`, headers);
  }
}

// Export singleton instance
export const investmentService = InvestmentService.getInstance();
