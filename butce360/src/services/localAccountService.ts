import AsyncStorage from '@react-native-async-storage/async-storage';
import { Account } from '../types/models';
import { getActiveDefaultAccounts } from '../constants/defaultAccounts';
import { STORAGE_KEYS } from './storage';
import { storage } from './storage';

const STORAGE_KEY = STORAGE_KEYS.LOCAL_ACCOUNTS;

export interface LocalAccount extends Account {
  transactionCount?: number;
  lastTransactionDate?: Date;
}

class LocalAccountService {
  private static instance: LocalAccountService;

  private constructor() {}

  static getInstance(): LocalAccountService {
    if (!LocalAccountService.instance) {
      LocalAccountService.instance = new LocalAccountService();
    }
    return LocalAccountService.instance;
  }

  // Get all local accounts
  async getAccounts(): Promise<LocalAccount[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        const accounts = JSON.parse(stored);

        return accounts.map((acc: any) => ({
          ...acc,
          createdAt: new Date(acc.createdAt),
          updatedAt: new Date(acc.updatedAt),
          lastTransactionDate: acc.lastTransactionDate ? new Date(acc.lastTransactionDate) : undefined,
        }));
      }

      // First time - initialize with default accounts

      const defaultAccounts = getActiveDefaultAccounts().map(acc => ({
        ...acc,
        transactionCount: 0,
        lastTransactionDate: undefined,
      }));
      
      await this.saveAccounts(defaultAccounts);
      return defaultAccounts;
    } catch (error) {

      return getActiveDefaultAccounts().map(acc => ({
        ...acc,
        transactionCount: 0,
        lastTransactionDate: undefined,
      }));
    }
  }

  // Save accounts to storage
  async saveAccounts(accounts: LocalAccount[]): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(accounts));

    } catch (error) {

      throw error;
    }
  }

  // Get account by ID
  async getAccountById(id: string): Promise<LocalAccount | null> {
    try {
      const accounts = await this.getAccounts();
      return accounts.find(acc => acc.id === id) || null;
    } catch (error) {

      return null;
    }
  }

  // Update account balance by NAME (for guest mode)
  async updateAccountBalanceByName(accountName: string, amount: number, isIncome: boolean): Promise<void> {
    try {
      const accounts = await this.getAccounts();
      const accountIndex = accounts.findIndex(acc => acc.name === accountName);

      if (accountIndex === -1) {

        return;
      }

      const account = accounts[accountIndex];



      // Update balance
      if (isIncome) {
        account.balance += amount;
      } else {
        account.balance -= amount;
      }

      // Update transaction count and last transaction date
      account.transactionCount = (account.transactionCount || 0) + 1;
      account.lastTransactionDate = new Date();
      account.updatedAt = new Date();

      accounts[accountIndex] = account;
      await this.saveAccounts(accounts);


    } catch (error) {
      console.error('[LocalAccountService] Error updating account balance by name:', error);
      throw error;
    }
  }

  // Update account balance by ID (for authenticated mode)
  async updateAccountBalance(accountId: string, amount: number, isIncome: boolean): Promise<void> {
    try {
      const accounts = await this.getAccounts();
      const accountIndex = accounts.findIndex(acc => acc.id === accountId);

      if (accountIndex === -1) {
        console.warn('[LocalAccountService] Account not found:', accountId);
        return;
      }

      const account = accounts[accountIndex];

      // Update balance
      if (isIncome) {
        account.balance += amount;
      } else {
        account.balance -= amount;
      }

      // Update transaction count and last transaction date
      account.transactionCount = (account.transactionCount || 0) + 1;
      account.lastTransactionDate = new Date();
      account.updatedAt = new Date();

      accounts[accountIndex] = account;
      await this.saveAccounts(accounts);

      console.log('[LocalAccountService] Updated account balance:', {
        accountId,
        newBalance: account.balance,
        transactionCount: account.transactionCount
      });
    } catch (error) {
      console.error('[LocalAccountService] Error updating account balance:', error);
      throw error;
    }
  }

  // Recalculate all account balances from transactions
  async recalculateBalances(transactions: any[]): Promise<void> {
    try {
      const accounts = await this.getAccounts();
      
      // Reset all balances and counts
      accounts.forEach(account => {
        account.balance = 0;
        account.transactionCount = 0;
        account.lastTransactionDate = undefined;
      });

      // Calculate from transactions
      const accountStats: { [key: string]: { balance: number; count: number; lastDate?: Date } } = {};
      
      transactions.forEach(transaction => {
        const accountId = transaction.accountId;
        if (!accountId) return;

        if (!accountStats[accountId]) {
          accountStats[accountId] = { balance: 0, count: 0 };
        }

        const amount = Math.abs(transaction.amount);
        if (transaction.type === 'income') {
          accountStats[accountId].balance += amount;
        } else {
          accountStats[accountId].balance -= amount;
        }
        
        accountStats[accountId].count += 1;
        
        const transactionDate = new Date(transaction.date || transaction.createdAt);
        if (!accountStats[accountId].lastDate || transactionDate > accountStats[accountId].lastDate!) {
          accountStats[accountId].lastDate = transactionDate;
        }
      });

      // Update accounts with calculated values
      accounts.forEach(account => {
        const stats = account.id ? accountStats[account.id] : undefined;
        if (stats) {
          account.balance = stats.balance;
          account.transactionCount = stats.count;
          account.lastTransactionDate = stats.lastDate;
        }
      });

      await this.saveAccounts(accounts);
      console.log('[LocalAccountService] Recalculated all account balances');
    } catch (error) {
      console.error('[LocalAccountService] Error recalculating balances:', error);
      throw error;
    }
  }

  // Add new account
  async addAccount(accountData: {
    name: string;
    type: Account['type'];
    balance?: number;
    currency?: string;
    icon?: string;
    color?: string;
  }): Promise<LocalAccount> {
    try {
      const accounts = await this.getAccounts();

      // Check if account with same name already exists
      const existingAccount = accounts.find(acc =>
        acc.name.toLowerCase() === accountData.name.toLowerCase()
      );

      if (existingAccount) {
        throw new Error(`"${accountData.name}" adında bir hesap zaten mevcut.`);
      }

      const newAccount: LocalAccount = {
        id: `local-account-${Date.now()}`,
        name: accountData.name,
        type: accountData.type,
        balance: accountData.balance || 0,
        currency: accountData.currency || 'TRY',
        icon: accountData.icon || 'wallet-outline',
        color: accountData.color || '#3B82F6',
        isDefault: false,
        transactionCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      accounts.push(newAccount);
      await this.saveAccounts(accounts);

      console.log('[LocalAccountService] Added new account:', newAccount.name);
      return newAccount;
    } catch (error) {
      console.error('[LocalAccountService] Error adding account:', error);
      throw error;
    }
  }

  // Clear all accounts (reset to defaults)
  async clearAccounts(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
      console.log('[LocalAccountService] Cleared all accounts');
    } catch (error) {
      console.error('[LocalAccountService] Error clearing accounts:', error);
      throw error;
    }
  }

  // Update account
  async updateAccount(id: string, accountData: Partial<Account>): Promise<Account> {
    try {
      const accounts = await this.getAccounts();
      const accountIndex = accounts.findIndex(acc => acc.id === id);

      if (accountIndex === -1) {
        throw new Error('Account not found');
      }

      const updatedAccount: Account = {
        ...accounts[accountIndex],
        ...accountData,
        updatedAt: new Date(),
      };

      accounts[accountIndex] = updatedAccount;
      await storage.setObject(STORAGE_KEYS.LOCAL_ACCOUNTS, accounts);

      console.log('[LocalAccountService] Account updated:', updatedAccount.name);
      return updatedAccount;
    } catch (error) {
      console.error('[LocalAccountService] Error updating account:', error);
      throw error;
    }
  }

  // Delete account
  async deleteAccount(id: string): Promise<void> {
    try {
      const accounts = await this.getAccounts();
      const filteredAccounts = accounts.filter(acc => acc.id !== id);

      await storage.setObject(STORAGE_KEYS.LOCAL_ACCOUNTS, filteredAccounts);
      console.log('[LocalAccountService] Account deleted:', id);
    } catch (error) {
      console.error('[LocalAccountService] Error deleting account:', error);
      throw error;
    }
  }
}

export const localAccountService = LocalAccountService.getInstance();
