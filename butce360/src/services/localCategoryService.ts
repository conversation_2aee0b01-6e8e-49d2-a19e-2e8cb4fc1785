import AsyncStorage from '@react-native-async-storage/async-storage';
import { Category } from '../types/models';
import { getActiveDefaultCategories } from '../constants/defaultCategories';
import { STORAGE_KEYS } from './storage';

const STORAGE_KEY = STORAGE_KEYS.LOCAL_CATEGORIES;

export interface LocalCategory extends Category {
  transactionCount?: number;
  totalAmount?: number;
  lastTransactionDate?: Date;
}

class LocalCategoryService {
  private static instance: LocalCategoryService;

  private constructor() {}

  static getInstance(): LocalCategoryService {
    if (!LocalCategoryService.instance) {
      LocalCategoryService.instance = new LocalCategoryService();
    }
    return LocalCategoryService.instance;
  }

  // Get all local categories
  async getCategories(): Promise<LocalCategory[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEY);
      if (stored) {
        const categories = JSON.parse(stored);
        console.log('[LocalCategoryService] Loaded categories from storage:', categories.length);
        return categories.map((cat: any) => ({
          ...cat,
          createdAt: new Date(cat.createdAt),
          updatedAt: new Date(cat.updatedAt),
          lastTransactionDate: cat.lastTransactionDate ? new Date(cat.lastTransactionDate) : undefined,
        }));
      }

      // First time - initialize with default categories
      console.log('[LocalCategoryService] No categories found, initializing with defaults');
      const defaultCategories = getActiveDefaultCategories().map(cat => ({
        ...cat,
        transactionCount: 0,
        totalAmount: 0,
        lastTransactionDate: undefined,
      }));
      
      await this.saveCategories(defaultCategories);
      return defaultCategories;
    } catch (error) {
      console.error('[LocalCategoryService] Error loading categories:', error);
      return getActiveDefaultCategories().map(cat => ({
        ...cat,
        transactionCount: 0,
        totalAmount: 0,
        lastTransactionDate: undefined,
      }));
    }
  }

  // Save categories to storage
  async saveCategories(categories: LocalCategory[]): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(categories));
      console.log('[LocalCategoryService] Saved categories to storage:', categories.length);
    } catch (error) {
      console.error('[LocalCategoryService] Error saving categories:', error);
      throw error;
    }
  }

  // Get category by ID
  async getCategoryById(id: string): Promise<LocalCategory | null> {
    try {
      const categories = await this.getCategories();
      return categories.find(cat => cat.id === id) || null;
    } catch (error) {
      console.error('[LocalCategoryService] Error getting category by ID:', error);
      return null;
    }
  }

  // Add new category
  async addCategory(categoryData: {
    name: string;
    type: 'income' | 'expense';
    icon?: string;
    color?: string;
  }): Promise<LocalCategory> {
    try {
      const categories = await this.getCategories();

      // Check if category with same name and type already exists
      const existingCategory = categories.find(cat =>
        cat.name.toLowerCase() === categoryData.name.toLowerCase() &&
        cat.type === categoryData.type
      );

      if (existingCategory) {
        throw new Error(`"${categoryData.name}" adında bir ${categoryData.type === 'income' ? 'gelir' : 'gider'} kategorisi zaten mevcut.`);
      }

      const newCategory: LocalCategory = {
        id: `local-category-${Date.now()}`,
        name: categoryData.name,
        type: categoryData.type,
        icon: categoryData.icon || (categoryData.type === 'income' ? '💰' : '💸'),
        color: categoryData.color || (categoryData.type === 'income' ? '#10B981' : '#EF4444'),
        isDefault: false,
        transactionCount: 0,
        totalAmount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      categories.push(newCategory);
      await this.saveCategories(categories);

      console.log('[LocalCategoryService] Added new category:', newCategory.name);
      return newCategory;
    } catch (error) {
      console.error('[LocalCategoryService] Error adding category:', error);
      throw error;
    }
  }

  // Update category transaction stats
  async updateCategoryStats(categoryId: string, amount: number): Promise<void> {
    try {
      const categories = await this.getCategories();
      const categoryIndex = categories.findIndex(cat => cat.id === categoryId);
      
      if (categoryIndex === -1) {
        console.warn('[LocalCategoryService] Category not found:', categoryId);
        return;
      }

      const category = categories[categoryIndex];
      
      // Update stats
      category.transactionCount = (category.transactionCount || 0) + 1;
      category.totalAmount = (category.totalAmount || 0) + amount;
      category.lastTransactionDate = new Date();
      category.updatedAt = new Date();

      categories[categoryIndex] = category;
      await this.saveCategories(categories);
      
      console.log('[LocalCategoryService] Updated category stats:', {
        categoryId,
        transactionCount: category.transactionCount,
        totalAmount: category.totalAmount
      });
    } catch (error) {
      console.error('[LocalCategoryService] Error updating category stats:', error);
      throw error;
    }
  }

  // Recalculate all category stats from transactions
  async recalculateStats(transactions: any[]): Promise<void> {
    try {
      const categories = await this.getCategories();
      
      // Reset all stats
      categories.forEach(category => {
        category.transactionCount = 0;
        category.totalAmount = 0;
        category.lastTransactionDate = undefined;
      });

      // Calculate from transactions
      const categoryStats: { [key: string]: { count: number; total: number; lastDate?: Date } } = {};
      
      transactions.forEach(transaction => {
        const categoryId = transaction.categoryId;
        if (!categoryId) return;

        if (!categoryStats[categoryId]) {
          categoryStats[categoryId] = { count: 0, total: 0 };
        }

        const amount = Math.abs(transaction.amount);
        categoryStats[categoryId].count += 1;
        categoryStats[categoryId].total += amount;
        
        const transactionDate = new Date(transaction.date || transaction.createdAt);
        if (!categoryStats[categoryId].lastDate || transactionDate > categoryStats[categoryId].lastDate!) {
          categoryStats[categoryId].lastDate = transactionDate;
        }
      });

      // Update categories with calculated values
      categories.forEach(category => {
        const stats = category.id ? categoryStats[category.id] : undefined;
        if (stats) {
          category.transactionCount = stats.count;
          category.totalAmount = stats.total;
          category.lastTransactionDate = stats.lastDate;
        }
      });

      await this.saveCategories(categories);
      console.log('[LocalCategoryService] Recalculated all category stats');
    } catch (error) {
      console.error('[LocalCategoryService] Error recalculating stats:', error);
      throw error;
    }
  }

  // Delete category (only non-default ones)
  async deleteCategory(categoryId: string): Promise<void> {
    try {
      const categories = await this.getCategories();
      const category = categories.find(cat => cat.id === categoryId);
      
      if (!category) {
        throw new Error('Kategori bulunamadı');
      }

      if (category.isDefault) {
        throw new Error('Varsayılan kategoriler silinemez');
      }

      const filteredCategories = categories.filter(cat => cat.id !== categoryId);
      await this.saveCategories(filteredCategories);
      
      console.log('[LocalCategoryService] Deleted category:', category.name);
    } catch (error) {
      console.error('[LocalCategoryService] Error deleting category:', error);
      throw error;
    }
  }

  // Clear all categories (reset to defaults)
  async clearCategories(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEY);
      console.log('[LocalCategoryService] Cleared all categories');
    } catch (error) {
      console.error('[LocalCategoryService] Error clearing categories:', error);
      throw error;
    }
  }

  // Update category
  async updateCategory(id: string, categoryData: Partial<LocalCategory>): Promise<LocalCategory> {
    try {
      const categories = await this.getCategories();
      const categoryIndex = categories.findIndex(cat => cat.id === id);

      if (categoryIndex === -1) {
        throw new Error('Category not found');
      }

      const updatedCategory: LocalCategory = {
        ...categories[categoryIndex],
        ...categoryData,
        updatedAt: new Date(),
      };

      categories[categoryIndex] = updatedCategory;
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(categories));

      console.log('[LocalCategoryService] Category updated:', updatedCategory.name);
      return updatedCategory;
    } catch (error) {
      console.error('[LocalCategoryService] Error updating category:', error);
      throw error;
    }
  }
}

export const localCategoryService = LocalCategoryService.getInstance();
