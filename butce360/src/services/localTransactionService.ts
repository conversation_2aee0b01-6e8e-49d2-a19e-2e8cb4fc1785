import { storage, STORAGE_KEYS } from './storage';

export interface LocalTransaction {
  id: string;
  title: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  categoryId?: string;
  account?: string;
  accountId?: string;
  note?: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export interface TransactionFilter {
  type?: 'income' | 'expense';
  month?: number;
  year?: number;
  categoryId?: string;
  accountId?: string;
}

class LocalTransactionService {
  private readonly STORAGE_KEY = STORAGE_KEYS.LOCAL_TRANSACTIONS;

  // Get all transactions
  async getTransactions(filter?: TransactionFilter): Promise<LocalTransaction[]> {
    try {
      const transactionsJson = await storage.getItem(this.STORAGE_KEY);
      let transactions: LocalTransaction[] = transactionsJson ? JSON.parse(transactionsJson) : [];

      // Apply filters
      if (filter) {
        transactions = this.applyFilters(transactions, filter);
      }

      // Sort by date (newest first)
      transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      return transactions;
    } catch (error) {
      console.error('[LocalTransactionService] Error getting transactions:', error);
      return [];
    }
  }

  // Get transaction by ID
  async getTransactionById(id: string): Promise<LocalTransaction | null> {
    try {
      const transactions = await this.getTransactions();
      return transactions.find(t => t.id === id) || null;
    } catch (error) {
      console.error('[LocalTransactionService] Error getting transaction by ID:', error);
      return null;
    }
  }

  // Add new transaction
  async addTransaction(transactionData: Omit<LocalTransaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<LocalTransaction> {
    try {
      const transactions = await this.getTransactions();
      
      const newTransaction: LocalTransaction = {
        ...transactionData,
        id: this.generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      transactions.push(newTransaction);
      await storage.setItem(this.STORAGE_KEY, JSON.stringify(transactions));

      console.log('[LocalTransactionService] Transaction added:', newTransaction);
      return newTransaction;
    } catch (error) {
      console.error('[LocalTransactionService] Error adding transaction:', error);
      throw error;
    }
  }

  // Update transaction
  async updateTransaction(id: string, updates: Partial<LocalTransaction>): Promise<LocalTransaction | null> {
    try {
      const transactions = await this.getTransactions();
      const index = transactions.findIndex(t => t.id === id);

      if (index === -1) {
        throw new Error('Transaction not found');
      }

      transactions[index] = {
        ...transactions[index],
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      await storage.setItem(this.STORAGE_KEY, JSON.stringify(transactions));

      console.log('[LocalTransactionService] Transaction updated:', transactions[index]);
      return transactions[index];
    } catch (error) {
      console.error('[LocalTransactionService] Error updating transaction:', error);
      throw error;
    }
  }

  // Delete transaction
  async deleteTransaction(id: string): Promise<boolean> {
    try {
      const transactions = await this.getTransactions();
      const filteredTransactions = transactions.filter(t => t.id !== id);

      if (filteredTransactions.length === transactions.length) {
        throw new Error('Transaction not found');
      }

      await storage.setItem(this.STORAGE_KEY, JSON.stringify(filteredTransactions));

      console.log('[LocalTransactionService] Transaction deleted:', id);
      return true;
    } catch (error) {
      console.error('[LocalTransactionService] Error deleting transaction:', error);
      throw error;
    }
  }

  // Get monthly summary
  async getMonthlySummary(month: number, year: number): Promise<{
    totalIncome: number;
    totalExpense: number;
    balance: number;
    transactionCount: number;
  }> {
    try {
      const transactions = await this.getTransactions({ month, year });
      
      const totalIncome = transactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + Math.abs(t.amount), 0);

      const totalExpense = transactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + Math.abs(t.amount), 0);

      return {
        totalIncome,
        totalExpense,
        balance: totalIncome - totalExpense,
        transactionCount: transactions.length,
      };
    } catch (error) {
      console.error('[LocalTransactionService] Error getting monthly summary:', error);
      return {
        totalIncome: 0,
        totalExpense: 0,
        balance: 0,
        transactionCount: 0,
      };
    }
  }

  // Clear all transactions
  async clearAllTransactions(): Promise<void> {
    try {
      await storage.removeItem(this.STORAGE_KEY);
      console.log('[LocalTransactionService] All transactions cleared');
    } catch (error) {
      console.error('[LocalTransactionService] Error clearing transactions:', error);
      throw error;
    }
  }

  // Export transactions for API sync
  async exportTransactions(): Promise<LocalTransaction[]> {
    return this.getTransactions();
  }

  // Import transactions from API
  async importTransactions(transactions: LocalTransaction[]): Promise<void> {
    try {
      await storage.setItem(this.STORAGE_KEY, JSON.stringify(transactions));
      console.log('[LocalTransactionService] Transactions imported:', transactions.length);
    } catch (error) {
      console.error('[LocalTransactionService] Error importing transactions:', error);
      throw error;
    }
  }

  // Private helper methods
  private applyFilters(transactions: LocalTransaction[], filter: TransactionFilter): LocalTransaction[] {
    return transactions.filter(transaction => {
      // Type filter
      if (filter.type && transaction.type !== filter.type) {
        return false;
      }

      // Date filter
      if (filter.month !== undefined || filter.year !== undefined) {
        const transactionDate = new Date(transaction.date);
        
        if (filter.year !== undefined && transactionDate.getFullYear() !== filter.year) {
          return false;
        }
        
        if (filter.month !== undefined && transactionDate.getMonth() !== filter.month) {
          return false;
        }
      }

      // Category filter
      if (filter.categoryId && transaction.categoryId !== filter.categoryId) {
        return false;
      }

      // Account filter
      if (filter.accountId && transaction.accountId !== filter.accountId) {
        return false;
      }

      return true;
    });
  }

  // Update account balance when transaction is deleted (reverse the operation)
  async updateAccountBalanceOnDelete(accountName: string, amount: number, wasIncome: boolean): Promise<void> {
    try {
      const { localAccountService } = await import('./localAccountService');

      // Reverse the original operation
      await localAccountService.updateAccountBalanceByName(
        accountName,
        amount,
        !wasIncome // Reverse: if it was income, now subtract; if it was expense, now add
      );

      console.log('[LocalTransactionService] Reversed account balance for deleted transaction:', {
        accountName,
        amount,
        wasIncome,
        reversedOperation: !wasIncome
      });
    } catch (error) {
      console.error('[LocalTransactionService] Error updating account balance on delete:', error);
      throw error;
    }
  }

  private generateId(): string {
    return `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const localTransactionService = new LocalTransactionService();
