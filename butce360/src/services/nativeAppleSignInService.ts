import { NativeModules, Platform } from 'react-native';

interface AppleSignInResult {
  success: boolean;
  userIdentifier?: string;
  email?: string;
  fullName?: {
    givenName?: string;
    familyName?: string;
  };
  identityToken?: string;
  authorizationCode?: string;
  error?: string;
}

interface AppleSignInModule {
  signIn(): Promise<AppleSignInResult>;
  isAvailable(): Promise<boolean>;
}

class NativeAppleSignInService {
  private appleSignIn: AppleSignInModule | null = null;

  constructor() {
    if (Platform.OS === 'ios') {
      this.appleSignIn = NativeModules.AppleSignInManager;
    }
  }

  async isAvailable(): Promise<boolean> {
    console.log('[NativeAppleSignInService] Checking availability...');
    console.log('[NativeAppleSignInService] Platform:', Platform.OS);
    console.log('[NativeAppleSignInService] Module exists:', !!this.appleSignIn);

    if (Platform.OS !== 'ios') {
      console.log('[NativeAppleSignInService] Not iOS platform');
      return false;
    }

    if (!this.appleSignIn) {
      console.log('[NativeAppleSignInService] Native module not found');
      return false;
    }

    try {
      const result = await this.appleSignIn.isAvailable();
      console.log('[NativeAppleSignInService] Native module result:', result);
      return result;
    } catch (error) {
      console.error('[NativeAppleSignInService] Error checking availability:', error);
      return false;
    }
  }

  async signIn(): Promise<AppleSignInResult> {
    if (Platform.OS !== 'ios' || !this.appleSignIn) {
      return {
        success: false,
        error: 'Apple Sign-In is only available on iOS'
      };
    }

    try {
      console.log('[NativeAppleSignInService] Starting Apple Sign-In...');
      const result = await this.appleSignIn.signIn();
      console.log('[NativeAppleSignInService] Apple Sign-In result:', result);
      
      return {
        ...result,
        success: true
      };
    } catch (error: any) {
      console.error('[NativeAppleSignInService] Apple Sign-In error:', error);
      
      // Handle specific error codes
      if (error.code === 'USER_CANCELED') {
        return {
          success: false,
          error: 'User canceled Apple Sign-In'
        };
      }
      
      return {
        success: false,
        error: error.message || 'Apple Sign-In failed'
      };
    }
  }

  // Format user data for our app
  formatUserData(result: AppleSignInResult) {
    if (!result.success) {
      return null;
    }

    const fullName = result.fullName;
    const displayName = fullName 
      ? `${fullName.givenName || ''} ${fullName.familyName || ''}`.trim()
      : 'Apple User';

    return {
      id: result.userIdentifier,
      email: result.email || '',
      name: displayName,
      provider: 'apple',
      identityToken: result.identityToken,
      authorizationCode: result.authorizationCode,
    };
  }
}

export const nativeAppleSignInService = new NativeAppleSignInService();
