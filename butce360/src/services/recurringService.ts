import { apiClient } from './api';
import { authService } from './authService';

export type RecurringInterval = 'daily' | 'weekly' | 'monthly' | 'yearly';

export interface CreateRecurringTransactionRequest {
  title: string;
  type: 'income' | 'expense';
  amount: number;
  currency: string; // e.g., 'TRY'
  interval: RecurringInterval;
  start_date: string; // ISO string or YYYY-MM-DD
  end_date?: string;  // optional ISO/YYY-MM-DD
  category_id: string;
  payment_method: string;
  account_id: string;
  note?: string;
}

export interface RecurringTransactionResponse {
  id: string;
  title: string;
  type: 'income' | 'expense';
  amount: number;
  currency: string;
  interval: RecurringInterval;
  start_date: string;
  end_date?: string;
  category_id: string;
  account_id: string;
  payment_method: string;
  note?: string;
  created_at?: string;
  updated_at?: string;
}

class RecurringService {
  async createRecurringTransaction(req: CreateRecurringTransactionRequest): Promise<RecurringTransactionResponse> {
    try {
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<RecurringTransactionResponse>(
        '/recurring-transactions',
        {
          method: 'POST',
          body: JSON.stringify(req)
        },
        token!
      );

      if (!response.success || !response.data) {
        throw new Error(response.message || 'Failed to create recurring transaction');
      }

      return response.data;
    } catch (error: any) {
      console.error('[RecurringService] Error creating recurring transaction:', error);
      throw new Error(error.message || 'Failed to create recurring transaction');
    }
  }
}

export const recurringService = new RecurringService();

