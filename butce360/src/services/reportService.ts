import { apiClient } from './api';
import { authService } from './authService';

export interface ReportSummary {
  totalIncome: number;
  totalExpense: number;
  netIncome: number;
  totalBalance: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  totalTransactions: number;
  balanceChange: string;
  incomeChange: string;
  expenseChange: string;
  currency: string;
  period?: string;
}

// Backend response format
interface BackendReportSummary {
  total_income: number;
  total_expense: number;
  net_balance: number;
  total_balance: number;
  monthly_income: number;
  monthly_expenses: number;
  total_transactions: number;
  balance_change: string;
  income_change: string;
  expense_change: string;
  currency?: string;
}

export interface CategoryBreakdown {
  categoryId: string;
  categoryName: string;
  amount: number;
  percentage: number;
  color?: string;
}

export interface MonthlyReport {
  month: string;
  year: number;
  totalIncome: number;
  totalExpense: number;
  netIncome: number;
  transactionCount: number;
}

export interface LocationSummary {
  location: string;
  amount: number;
  transactionCount: number;
}

export class ReportService {
  private static instance: ReportService;

  private constructor() {}

  static getInstance(): ReportService {
    if (!ReportService.instance) {
      ReportService.instance = new ReportService();
    }
    return ReportService.instance;
  }


  // Get summary report
  async getSummary(period?: 'week' | 'month' | 'year' | 'all'): Promise<ReportSummary | null> {
    try {
      const token = await authService.getStoredToken();
      const endpoint = period ? `/reports/summary?period=${period}` : '/reports/summary';
      const response = await apiClient.authenticatedRequest<BackendReportSummary>(endpoint, { method: 'GET' }, token!);

      console.log('[ReportService] Fetched summary:', response.data);

      // Convert backend format to frontend format
      const backendData = response.data;

      // Normalize currency
      let currency = backendData.currency || 'TL';
      if (currency === 'TL' || currency === 'tl') {
        currency = 'TRY';
      }

      const frontendData: ReportSummary = {
        totalIncome: backendData.total_income || 0,
        totalExpense: backendData.total_expense || 0,
        netIncome: backendData.net_balance || 0,
        totalBalance: backendData.total_balance || 0,
        monthlyIncome: backendData.monthly_income || 0,
        monthlyExpenses: backendData.monthly_expenses || 0,
        totalTransactions: backendData.total_transactions || 0,
        balanceChange: backendData.balance_change || '0%',
        incomeChange: backendData.income_change || '0%',
        expenseChange: backendData.expense_change || '0%',
        currency: currency,
        period: period || 'month',
      };

      return frontendData;
    } catch (error) {
      console.error('[ReportService] Error fetching summary:', error);
      // Return null instead of throwing error for UI stability
      return null;
    }
  }

  // Get category breakdown
  async getCategoryBreakdown(period?: 'month' | 'year' | 'all'): Promise<CategoryBreakdown[]> {
    try {
      const token = await authService.getStoredToken();
      const endpoint = period ? `/reports/category-breakdown?period=${period}` : '/reports/category-breakdown';
      const response = await apiClient.authenticatedRequest<CategoryBreakdown[]>(endpoint, { method: 'GET' }, token!);

      console.log('[ReportService] Fetched category breakdown:', response.data);
      return response.data;
    } catch (error) {
      console.error('[ReportService] Error fetching category breakdown:', error);
      throw error;
    }
  }

  // Get monthly report
  async getMonthlyReport(year?: number): Promise<MonthlyReport[]> {
    try {
      const token = await authService.getStoredToken();
      const endpoint = year ? `/reports/monthly?year=${year}` : '/reports/monthly';
      const response = await apiClient.authenticatedRequest<MonthlyReport[]>(endpoint, { method: 'GET' }, token!);

      console.log('[ReportService] Fetched monthly report:', response.data);
      return response.data;
    } catch (error) {
      console.error('[ReportService] Error fetching monthly report:', error);
      throw error;
    }
  }

  // Get location summary
  async getLocationSummary(period?: 'month' | 'year' | 'all'): Promise<LocationSummary[]> {
    try {
      const token = await authService.getStoredToken();
      const endpoint = period ? `/reports/location-summary?period=${period}` : '/reports/location-summary';
      const response = await apiClient.authenticatedRequest<LocationSummary[]>(endpoint, { method: 'GET' }, token!);

      console.log('[ReportService] Fetched location summary:', response.data);
      return response.data;
    } catch (error) {
      console.error('[ReportService] Error fetching location summary:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const reportService = ReportService.getInstance();
