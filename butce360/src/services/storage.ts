import AsyncStorage from '@react-native-async-storage/async-storage';

class StorageService {
  async setItem(key: string, value: string): Promise<void> {
    try {
      await AsyncStorage.setItem(key, value);
      console.log(`[Storage] Set item: ${key}`);
    } catch (error) {
      console.error(`[Storage] Failed to set item ${key}:`, error);
      throw error;
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      const value = await AsyncStorage.getItem(key);
      console.log(`[Storage] Get item: ${key} = ${value ? 'found' : 'not found'}`);
      return value;
    } catch (error) {
      console.error(`[Storage] Failed to get item ${key}:`, error);
      return null;
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(key);
      console.log(`[Storage] Removed item: ${key}`);
    } catch (error) {
      console.error(`[Storage] Failed to remove item ${key}:`, error);
      throw error;
    }
  }

  async multiRemove(keys: string[]): Promise<void> {
    try {
      await AsyncStorage.multiRemove(keys);
      console.log(`[Storage] Removed items: ${keys.join(', ')}`);
    } catch (error) {
      console.error(`[Storage] Failed to remove items:`, error);
      throw error;
    }
  }

  async clear(): Promise<void> {
    try {
      await AsyncStorage.clear();
      console.log('[Storage] Cleared all items');
    } catch (error) {
      console.error('[Storage] Failed to clear storage:', error);
      throw error;
    }
  }

  async getAllKeys(): Promise<string[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      return [...keys]; // Spread to make it mutable
    } catch (error) {
      console.error('[Storage] Failed to get all keys:', error);
      return [];
    }
  }

  // Utility methods for JSON data
  async setObject<T>(key: string, value: T): Promise<void> {
    await this.setItem(key, JSON.stringify(value));
  }

  async getObject<T>(key: string): Promise<T | null> {
    const value = await this.getItem(key);
    if (!value) return null;

    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.error(`[Storage] Failed to parse JSON for key ${key}:`, error);
      return null;
    }
  }
}

// Export singleton instance
export const storage = new StorageService();

// Storage keys constants
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  AUTH_USER: 'auth_user',
  USER_PREFERENCES: 'user_preferences',
  CACHED_CATEGORIES: 'cached_categories',
  CACHED_ACCOUNTS: 'cached_accounts',
  GUEST_MODE: 'guest_mode',

  // Guest mode local data keys
  LOCAL_TRANSACTIONS: 'local_transactions',
  LOCAL_ACCOUNTS: 'local_accounts',
  LOCAL_CATEGORIES: 'local_categories',
  LOCAL_BUDGETS: 'local_budgets',
} as const;