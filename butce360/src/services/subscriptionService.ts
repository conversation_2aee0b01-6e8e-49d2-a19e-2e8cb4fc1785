import { apiClient } from './api';
import { authService } from './authService';

// Subscription DTOs
export interface SubscriptionStatus {
  isPremium: boolean;
  plan: string;
  expiresAt: string | null;
  remainingDays: number;
  productId?: string;
  autoRenewStatus?: boolean;
  store?: string;
}

export interface PurchaseNotification {
  customerInfo: any;
  productId: string;
  originalTransactionId: string;
  transactionId: string;
  purchaseDate: Date;
  expirationDate: Date;
}

export interface ActivateSubscriptionRequest {
  userId?: string;
  productId: string;
  originalTransactionId: string;
  expiresDate: Date;
  revenueCatCustomerId?: string;
}

class SubscriptionService {
  private async getAuthHeaders() {
    const token = await authService.getStoredToken();
    if (!token) {
      throw new Error('No authentication token found');
    }
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  // Get current user's subscription status
  async getSubscriptionStatus(): Promise<SubscriptionStatus> {
    try {
      const headers = await this.getAuthHeaders();

      const response = await apiClient.get<{ data: SubscriptionStatus }>('/subscription/status', headers);

      if (response.success && response.data) {
        // Handle different response structures safely
        let subscriptionData: any = response.data;

        // If response has nested data property, use it
        if (subscriptionData.data && typeof subscriptionData.data === 'object') {
          subscriptionData = subscriptionData.data;
        }

        // Validate and return subscription data with safe defaults
        return {
          isPremium: Boolean(subscriptionData.isPremium || subscriptionData.is_premium),
          plan: subscriptionData.plan || 'free',
          expiresAt: subscriptionData.expiresAt || subscriptionData.expires_at || null,
          remainingDays: subscriptionData.remainingDays || subscriptionData.remaining_days || 0,
          productId: subscriptionData.productId || subscriptionData.product_id,
          autoRenewStatus: subscriptionData.autoRenewStatus || subscriptionData.auto_renew_status,
          store: subscriptionData.store,
        };
      } else {
        throw new Error(response.message || 'Failed to get subscription status');
      }
    } catch (error: any) {
      console.error('[SubscriptionService] getSubscriptionStatus error:', error);

      // For any error (404, 401, 500, etc.), return default free status
      // This prevents the app from breaking when subscription data is not available
      return {
        isPremium: false,
        plan: 'free',
        expiresAt: null,
        remainingDays: 0,
      };
    }
  }

  // Notify backend about a purchase made through RevenueCat
  async notifyPurchase(notification: PurchaseNotification): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await apiClient.post('/subscription/purchase-notification', notification, headers);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to notify purchase');
      }
      
      console.log('[SubscriptionService] Purchase notification sent successfully');
    } catch (error: any) {
      console.error('[SubscriptionService] notifyPurchase error:', error);
      throw new Error(error.message || 'Failed to notify purchase');
    }
  }

  // Manually activate subscription (for testing)
  async activateSubscription(request: ActivateSubscriptionRequest): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await apiClient.post('/subscription/activate', request, headers);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to activate subscription');
      }
      
      console.log('[SubscriptionService] Subscription activated successfully');
    } catch (error: any) {
      console.error('[SubscriptionService] activateSubscription error:', error);
      throw new Error(error.message || 'Failed to activate subscription');
    }
  }

  // Check if a feature requires premium access
  isPremiumFeature(featureName: string): boolean {
    const premiumFeatures = [
      'investment',
      'bank-statement',
      'advanced-reports',
      'export-data',
      'unlimited-transactions',
    ];
    
    return premiumFeatures.includes(featureName.toLowerCase());
  }

  // Get premium feature list
  getPremiumFeatures(): string[] {
    return [
      'Yatırım Simülasyonu',
      'Banka Ekstresi Okuma',
      'Gelişmiş Raporlar',
      'Veri Dışa Aktarma',
      'Sınırsız İşlem',
      'Otomatik Yedekleme',
      'Özel Temalar',
      'Öncelikli Destek',
    ];
  }

  // Get subscription plans
  getSubscriptionPlans() {
    return [
      {
        id: 'monthly',
        name: 'Aylık Premium',
        productId: 'com.butce360.app.one.month',
        price: '₺29,99',
        period: 'ay',
        features: this.getPremiumFeatures(),
      },
      {
        id: 'yearly',
        name: 'Yıllık Premium',
        productId: 'com.butce360.app.one.year',
        price: '₺299,99',
        period: 'yıl',
        savings: '₺59,89',
        features: this.getPremiumFeatures(),
        recommended: true,
      },
    ];
  }
}

export const subscriptionService = new SubscriptionService();
