// User and Authentication Types
export interface User {
  id: string;
  username: string;
  email: string;
  name: string;
  isGuest: boolean;
  guestId?: string;
  plan: string;
  transactionLimit: number;
  transactionCount: number;
  oauthProvider?: string; // 'google', 'apple', null for standard users
  googleId?: string;
  appleId?: string;
  profileImageUrl?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  name: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}


export interface GuestToRegisteredRequest {
  username: string;
  email: string;
  password: string;
  name: string;
}

export interface TransactionLimitResponse {
  canCreateTransaction: boolean;
  transactionCount: number;
  transactionLimit: number;
  remainingTransactions: number;
  requiresUpgrade: boolean;
}

// Account Types
export interface Account {
  id?: string;
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'cash' | 'investment' | 'bank' | 'credit_card';
  balance: number;
  currency: string;
  color: string;
  icon: string;
  isDefault: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AccountInput {
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'cash' | 'investment' | 'bank' | 'credit_card';
  balance: number;
  currency: string;
  color: string;
  icon: string;
  isDefault?: boolean;
}

// Category Types
export interface Category {
  id?: string;
  name: string;
  type: 'income' | 'expense';
  color: string;
  icon: string;
  isDefault: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CategoryInput {
  name: string;
  type: 'income' | 'expense';
  color: string;
  icon: string;
  isDefault?: boolean;
}

// Transaction Types
export interface Transaction {
  id?: string;
  title: string;
  type: 'income' | 'expense';
  amount: number;
  currency: string;
  categoryId: string;
  paymentMethod: string;
  accountId: string;
  note?: string;
  transactionDate: Date;
  location?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // Additional fields for display
  categoryName?: string;
  categoryColor?: string;
  accountName?: string;
}

export interface TransactionInput {
  title: string;
  type: 'income' | 'expense';
  amount: number;
  currency: string;
  categoryId: string;
  paymentMethod: string;
  accountId: string;
  note?: string;
  transactionDate: Date;
  location?: string;
}

// Budget Types
export interface Budget {
  id?: string;
  name: string;
  categoryId: string;
  amount: number;
  currency: string;
  period: 'monthly' | 'weekly' | 'yearly';
  startDate: Date;
  endDate: Date;
  spent: number;
  remaining: number;
  color?: string; // Color for budget visualization
  createdAt?: Date;
  updatedAt?: Date;
}

export interface BudgetInput {
  name: string;
  categoryId: string;
  amount: number;
  currency: string;
  period: 'monthly' | 'weekly' | 'yearly';
  startDate: Date;
  endDate: Date;
  color?: string; // Optional color for budget
}

// Transaction Summary Types
export interface TransactionSummary {
  totalIncome: number;
  totalExpense: number;
  netIncome: number;
  transactionCount: number;
  period: string;
}

// Request Types
export interface CreateTransactionRequest {
  title: string;
  amount: number;
  type: 'income' | 'expense';
  categoryId: string;
  accountId: string;
  description?: string;
  date: Date;
}

export interface UpdateTransactionRequest {
  title?: string;
  amount?: number;
  type?: 'income' | 'expense';
  categoryId?: string;
  accountId?: string;
  description?: string;
  date?: Date;
}

export interface CreateAccountRequest {
  name: string;
  type: 'cash' | 'bank' | 'credit_card' | 'investment';
  balance: number;
  currency: string;
  description?: string;
}

export interface UpdateAccountRequest {
  name?: string;
  type?: 'checking' | 'savings' | 'credit' | 'cash' | 'investment' | 'bank' | 'credit_card';
  balance?: number;
  currency?: string;
  description?: string;
  color?: string;
  icon?: string;
}

export interface CreateCategoryRequest {
  name: string;
  type: 'income' | 'expense';
  icon?: string;
  color?: string;
  description?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  type?: 'income' | 'expense';
  icon?: string;
  color?: string;
  description?: string;
}

// Report Types
export interface ReportSummary {
  totalIncome: number;
  totalExpense: number;
  netIncome: number;
  currency: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
}

export interface CategoryReport {
  categoryId: string;
  categoryName: string;
  categoryColor: string;
  categoryIcon: string;
  totalAmount: number;
  transactionCount: number;
  percentage: number;
}

export interface MonthlyReport {
  month: string;
  year: number;
  totalIncome: number;
  totalExpense: number;
  netIncome: number;
  categories: CategoryReport[];
}

// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  status: number;
}

export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

// Pagination Types
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter Types
export interface TransactionFilter {
  startDate?: Date;
  endDate?: Date;
  categoryId?: string;
  accountId?: string;
  type?: 'income' | 'expense';
  minAmount?: number;
  maxAmount?: number;
  searchTerm?: string;
}

// Default Data Types
export interface DefaultCategories {
  income: CategoryInput[];
  expense: CategoryInput[];
}

export interface DefaultAccounts {
  accounts: AccountInput[];
}

// Investment Types
export interface InvestmentSimulation {
  id?: string;
  asset: string;
  amount_invested: number;
  start_date: string;
  price_at_start: number;
  current_price: number;
  units_bought: number;
  current_value: number;
  profit: number;
  growth_rate_percent: number;
  simulation_type: 'simulate' | 'whatif';
  hypothetical_date?: string;
  price_then?: number;
  price_now?: number;
  created_at?: string;
}

export interface InvestmentAsset {
  id: string;
  name: string;
  symbol: string;
  category: 'crypto' | 'commodity' | 'currency' | 'stock';
  icon?: string;
}

export interface InvestmentSimulationFilter {
  asset?: string;
  simulation_type?: 'simulate' | 'whatif';
  date_from?: string;
  date_to?: string;
}