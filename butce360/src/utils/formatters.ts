/**
 * Format number with commas for thousands separator
 * @param amount - The number to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted string with commas
 */
export const formatAmount = (amount: number, decimals: number = 2): string => {
  return amount.toLocaleString('tr-TR', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

/**
 * Format currency amount with Turkish Lira symbol
 * @param amount - The amount to format
 * @param showSymbol - Whether to show ₺ symbol (default: true)
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, showSymbol: boolean = true): string => {
  const formatted = formatAmount(amount, 2);
  return showSymbol ? `₺${formatted}` : formatted;
};

/**
 * Parse formatted amount string back to number
 * @param formattedAmount - String with commas and dots
 * @returns Parsed number
 */
export const parseAmount = (formattedAmount: string): number => {
  // Remove currency symbols and spaces
  const cleaned = formattedAmount.replace(/[₺\s]/g, '');
  
  // Handle Turkish number format (comma as thousands separator, dot as decimal)
  const normalized = cleaned.replace(/\./g, '').replace(',', '.');
  
  return parseFloat(normalized) || 0;
};
