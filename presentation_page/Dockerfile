# Use Node.js Alpine image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Build the app
RUN npm run build

# Install serve globally to serve static files
RUN npm install -g serve

# Expose port 3110
EXPOSE 3110

# Start the app with serve on port 3110
CMD ["serve", "-s", "build", "-l", "3110"]
