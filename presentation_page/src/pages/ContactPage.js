import React from 'react';
import Contact from '../components/Contact';

const ContactPage = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Bizimle
              <span className="text-emerald-600 block">İletişime Geçin</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              So<PERSON>larınız mı var? Uzman ekibimiz size yardımcı olmak için burada. 
              Hemen iletişime geçin ve size en uygun çözümü bulalım.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://app.butce360.com/register"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-emerald-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-emerald-700 transition-colors shadow-lg"
              >
                Ücretsiz Başla
              </a>
              <a
                href="mailto:<EMAIL>"
                className="bg-blue-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-blue-700 transition-colors shadow-lg"
              >
                Email ile İletişim
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Component */}
      <Contact />

      {/* Additional Info */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Diğer İletişim Yolları
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Size en uygun iletişim kanalını seçin. Her zaman yanınızdayız.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-white rounded-2xl p-8 text-center shadow-sm border border-gray-100">
                <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-3">Email Destek</h3>
                <p className="text-gray-600 mb-4">7/24 email desteği</p>
                <a href="mailto:<EMAIL>" className="text-emerald-600 font-semibold hover:text-emerald-700">
                  <EMAIL>
                </a>
              </div>



              <div className="bg-white rounded-2xl p-8 text-center shadow-sm border border-gray-100">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-3">SSS</h3>
                <p className="text-gray-600 mb-4">Sık sorulan sorular</p>
                <a href="/sss" className="text-purple-600 font-semibold hover:text-purple-700">
                  SSS Sayfası
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Office Hours */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-br from-emerald-50 to-blue-50 rounded-3xl p-12 text-center border border-emerald-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Çalışma Saatlerimiz
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Hafta İçi</h3>
                  <p className="text-gray-600">Pazartesi - Cuma<br />09:00 - 18:00</p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Hafta Sonu</h3>
                  <p className="text-gray-600">Cumartesi - Pazar<br />Email desteği</p>
                </div>
              </div>
              <div className="mt-8">
                <p className="text-gray-600 mb-6">
                  Acil durumlar için email üzerinden 7/24 ulaşabilirsiniz.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="bg-blue-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-blue-700 transition-colors shadow-lg inline-block"
                >
                  Acil Destek için Email
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
