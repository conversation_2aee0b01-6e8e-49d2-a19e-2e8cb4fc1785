import React, { useState } from 'react';

const FAQPage = () => {
  const [openFAQ, setOpenFAQ] = useState(null);

  const faqs = [
    {
      category: "Genel Sorular",
      questions: [
        {
          question: "Butce360 nedir ve nasıl çalışır?",
          answer: "Butce360, gelir ve giderlerinizi takip etmenizi, bütçe planlaması yapmanızı ve finansal hedeflerinize ulaşmanızı sağlayan akıllı bir finansal yönetim platformudur. Banka ekstrelerinizi otomatik olarak analiz eder ve size kişiselleştirilmiş öneriler sunar."
        },
        {
          question: "Verilerim güvende mi?",
          answer: "Evet, verileriniz banka seviyesinde güvenlik ile korunur. SSL şifreleme kullanıyoruz ve KVKK uyumlu veri işleme yapıyoruz. Finansal verileriniz hiçbir zaman üçüncü taraflarla paylaşılmaz."
        },
        {
          question: "Hangi bankaları destekliyorsunuz?",
          answer: "Şu anda Türkiye'deki tüm büyük bankaların PDF ekstrelerini destekliyoruz. VakıfBank, QNB, Garanti BBVA, İş Bankası, Akbank ve diğer bankaların ekstrelerini yükleyebilirsiniz."
        },
        {
          question: "Mobil uygulamanız var mı?",
          answer: "Evet, iOS ve Android için mobil uygulamamız mevcuttur. Web platformumuz da mobil uyumlu olarak tasarlanmıştır, böylece her cihazdan erişebilirsiniz."
        }
      ]
    },
    {
      category: "Özellikler",
      questions: [
        {
          question: "Otomatik kategorilendirme nasıl çalışır?",
          answer: "Yapay zeka algoritmalarımız, işlem açıklamalarını analiz ederek otomatik olarak uygun kategorilere atar. Sistem zamanla öğrenir ve daha doğru kategorilendirme yapar. İsterseniz manuel olarak da düzenleyebilirsiniz."
        },
        {
          question: "Bütçe planlama özelliği nasıl kullanılır?",
          answer: "Her kategori için aylık bütçe limitleri belirleyebilirsiniz. Sistem, harcamalarınızı takip eder ve limite yaklaştığınızda sizi uyarır. Ayrıca geçmiş verilerinize dayanarak akıllı bütçe önerileri sunar."
        },
        {
          question: "Hangi tür raporlar alabilirim?",
          answer: "Aylık/yıllık harcama raporları, kategori bazlı analizler, gelir-gider karşılaştırmaları, trend analizleri ve özel dönem raporları alabilirsiniz. Tüm raporları PDF olarak indirebilirsiniz."
        },
        {
          question: "Çoklu hesap yönetimi nasıl çalışır?",
          answer: "Birden fazla banka hesabı, kredi kartı ve nakit hesabınızı tek platformda yönetebilirsiniz. Her hesap için ayrı bakiye takibi yapılır ve toplam finansal durumunuzu görebilirsiniz."
        }
      ]
    },
    {
      category: "Fiyatlandırma",
      questions: [
        {
          question: "Ücretsiz plan sınırları nelerdir?",
          answer: "Ücretsiz planda 3 hesap bağlayabilir, ayda 100 işlem kaydedebilir ve temel raporlara erişebilirsiniz. Mobil uygulama ve email desteği dahildir."
        },
        {
          question: "Ödeme yöntemleri nelerdir?",
          answer: "Kredi kartı, banka kartı ve havale ile ödeme yapabilirsiniz. Tüm ödemeler güvenli SSL bağlantısı üzerinden işlenir."
        },
        {
          question: "İptal etmek istersem ne yapmalıyım?",
          answer: "İstediğiniz zaman hesap ayarlarından aboneliğinizi iptal edebilirsiniz. İptal sonrası mevcut dönem sonuna kadar hizmet almaya devam edersiniz."
        },
        {
          question: "Para iade politikanız nedir?",
          answer: "Tüm paketlerde 30 gün para iade garantisi sunuyoruz. Memnun kalmazsanız, tam ücret iadesi alabilirsiniz."
        }
      ]
    },
    {
      category: "Teknik Destek",
      questions: [
        {
          question: "Teknik sorun yaşarsam ne yapmalıyım?",
          answer: "Teknik sorunlar için <EMAIL> adresine email gönderebilirsiniz. Genellikle 24 saat içinde yanıt veriyoruz."
        },
        {
          question: "Veri yedekleme yapıyor musunuz?",
          answer: "Evet, verileriniz günlük olarak yedeklenir ve birden fazla sunucuda saklanır. Veri kaybı riski minimumdur."
        },
        {
          question: "API erişimi var mı?",
          answer: "Profesyonel ve Kurumsal paketlerde API erişimi mevcuttur. Kendi uygulamalarınızla entegrasyon yapabilirsiniz."
        },
        {
          question: "Sistem güncellemeleri nasıl yapılır?",
          answer: "Tüm güncellemeler otomatik olarak yapılır. Yeni özellikler ve iyileştirmeler hakkında email ile bilgilendirilirsiniz."
        }
      ]
    }
  ];

  const toggleFAQ = (categoryIndex, questionIndex) => {
    const faqKey = `${categoryIndex}-${questionIndex}`;
    setOpenFAQ(openFAQ === faqKey ? null : faqKey);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Sıkça Sorulan
              <span className="text-emerald-600 block">Sorular</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Butce360 hakkında merak ettiklerinizin yanıtlarını burada bulabilirsiniz. 
              Aradığınızı bulamazsanız bizimle iletişime geçin.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="bg-blue-600 text-white px-8 py-4 rounded-2xl font-bold hover:bg-blue-700 transition-colors shadow-lg"
              >
                Email ile Sor
              </a>
              <a
                href="/iletisim"
                className="border-2 border-emerald-600 text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-emerald-600 hover:text-white transition-colors"
              >
                İletişim Formu
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {faqs.map((category, categoryIndex) => (
              <div key={categoryIndex} className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-8 pb-4 border-b-2 border-emerald-100">
                  {category.category}
                </h2>
                <div className="space-y-4">
                  {category.questions.map((faq, questionIndex) => {
                    const faqKey = `${categoryIndex}-${questionIndex}`;
                    const isOpen = openFAQ === faqKey;
                    
                    return (
                      <div 
                        key={questionIndex}
                        className="bg-gray-50 rounded-2xl overflow-hidden border border-gray-100 hover:border-emerald-200 transition-colors"
                      >
                        <button
                          onClick={() => toggleFAQ(categoryIndex, questionIndex)}
                          className="w-full px-6 py-6 text-left flex justify-between items-center hover:bg-gray-100 transition-colors"
                        >
                          <span className="text-lg font-semibold text-gray-900 pr-4">
                            {faq.question}
                          </span>
                          <svg 
                            className={`w-6 h-6 text-emerald-600 transform transition-transform ${isOpen ? 'rotate-180' : ''}`}
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                        {isOpen && (
                          <div className="px-6 pb-6">
                            <p className="text-gray-600 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Still Have Questions */}
      <section className="py-20 bg-gradient-to-br from-emerald-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            Hala Sorunuz mu Var?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Uzman ekibimiz size yardımcı olmak için burada. 
            Hemen iletişime geçin ve tüm sorularınızın yanıtını alın.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/iletisim"
              className="bg-white text-emerald-600 px-8 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-colors shadow-lg"
            >
              İletişim Formu
            </a>
            <a
              href="mailto:<EMAIL>"
              className="border-2 border-white text-white px-8 py-4 rounded-2xl font-bold hover:bg-white hover:text-emerald-600 transition-colors"
            >
              Email Destek
            </a>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Hızlı Erişim
              </h2>
              <p className="text-lg text-gray-600">
                Sık kullanılan sayfalara hızlı erişim
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <a href="/nedir" className="bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group">
                <div className="text-4xl mb-4">❓</div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors">
                  Butce360 Nedir?
                </h3>
              </a>

              <a href="/nasil-calisir" className="bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group">
                <div className="text-4xl mb-4">⚙️</div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors">
                  Nasıl Çalışır?
                </h3>
              </a>

              <a href="/ucretlendirme" className="bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group">
                <div className="text-4xl mb-4">💰</div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors">
                  Fiyatlandırma
                </h3>
              </a>

              <a href="https://app.butce360.com/register" target="_blank" rel="noopener noreferrer" className="bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 hover:shadow-lg transition-shadow group">
                <div className="text-4xl mb-4">🚀</div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-emerald-600 transition-colors">
                  Hemen Başla
                </h3>
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FAQPage;
