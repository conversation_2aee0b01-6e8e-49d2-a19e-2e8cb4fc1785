import React from 'react';
import Pricing from '../components/Pricing';

const PricingPage = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Uygun Fiyatlı
              <span className="text-emerald-600 block"><PERSON><PERSON> Seçenekleri</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              İhtiyacınıza uygun paketi seçin ve finansal takibin gücünü keşfedin. 
              1 ay ücretsiz deneme imkanı.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="bg-emerald-100 text-emerald-800 px-6 py-3 rounded-full font-semibold">
                ✨ 1 ay ücretsiz deneme
              </div>
              <div className="bg-blue-100 text-blue-800 px-6 py-3 rounded-full font-semibold">
                💳 Kredi kartı gerektirmez
              </div>
              <div className="bg-purple-100 text-purple-800 px-6 py-3 rounded-full font-semibold">
                🔄 İstediğiniz zaman iptal
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Component */}
      <Pricing />

      {/* Comparison Table */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Detaylı Özellik Karşılaştırması
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Hangi paketin size uygun olduğunu görmek için özellik karşılaştırmasını inceleyin.
              </p>
            </div>

            <div className="bg-white rounded-3xl shadow-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Özellikler</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900 bg-emerald-50">Profesyonel</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">Hesap Sayısı</td>
                      <td className="px-6 py-4 text-center text-sm text-gray-600 bg-emerald-50">Sınırsız</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">Aylık İşlem Limiti</td>
                      <td className="px-6 py-4 text-center text-sm text-gray-600">Sınırsız</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">Temel Raporlar</td>
                      <td className="px-6 py-4 text-center">
                        <svg className="w-5 h-5 text-emerald-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">Gelişmiş Raporlar</td>
                      <td className="px-6 py-4 text-center">
                        <svg className="w-5 h-5 text-emerald-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">Bütçe Planlama</td>
                      <td className="px-6 py-4 text-center">
                        <svg className="w-5 h-5 text-emerald-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">PDF Ekstres Analizi</td>
                      <td className="px-6 py-4 text-center">
                        <svg className="w-5 h-5 text-emerald-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">API Erişimi</td>
                      <td className="px-6 py-4 text-center">
                        <svg className="w-5 h-5 text-emerald-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">Çoklu Kullanıcı</td>
                      <td className="px-6 py-4 text-center">
                        <svg className="w-5 h-5 text-emerald-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 text-sm text-gray-900 font-medium">Destek</td>
                      <td className="px-6 py-4 text-center text-sm text-gray-600">7/24 Telefon</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Fiyatlandırma Hakkında SSS
              </h2>
            </div>

            <div className="space-y-6">
              <div className="bg-gray-50 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Ücretsiz deneme süresi sonunda ne oluyor?
                </h3>
                <p className="text-gray-600">
                  7 günlük ücretsiz deneme süreniz sonunda, seçtiğiniz pakete göre ücretlendirme başlar. 
                  İstediğiniz zaman iptal edebilirsiniz.
                </p>
              </div>

              <div className="bg-gray-50 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Paket değişikliği yapabilir miyim?
                </h3>
                <p className="text-gray-600">
                  Evet, istediğiniz zaman paketinizi yükseltebilir veya düşürebilirsiniz. 
                  Değişiklik hemen etkili olur ve fatura döngünüze göre hesaplanır.
                </p>
              </div>

              <div className="bg-gray-50 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Para iade garantisi var mı?
                </h3>
                <p className="text-gray-600">
                  Evet, tüm paketlerde 30 gün para iade garantisi sunuyoruz. 
                  Memnun kalmazsanız, tam ücret iadesi alabilirsiniz.
                </p>
              </div>

              <div className="bg-gray-50 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  Kurumsal paket için özel fiyat alabilir miyim?
                </h3>
                <p className="text-gray-600">
                  Büyük organizasyonlar için özel fiyatlandırma seçeneklerimiz mevcuttur. 
                  Detaylar için bizimle iletişime geçin.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PricingPage;
